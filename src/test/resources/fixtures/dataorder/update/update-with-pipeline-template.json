{"commandType": "UPDATE", "objectId": "{{dataOrderId}}", "type": "DataOrder", "properties": {"request": {"name": "UseCase.abc.xyz2342432", "description": "This is a sample UseCase#2", "projectId": "a672201e-4fd1-46d9-97be-931262aa7f5a", "requiresDataHold": false, "orderId": "085e2137-39e8-2cb7-a33e-c27f1ca6d77f", "schemas": ["{{schemaId}}"], "workbenches": {"new": null, "existing": [{"id": "78c12ab5-bfcc-4737-b855-2f5df395e6e6", "spnId": "def321d9-dd44-47c4-b96e-2609a4af0713", "spnClientId": "acc56f62-c46e-472c-a6dd-f8306989ebbf", "spnName": "service-principal-name"}]}, "repositories": ["KafkaTopic"], "configuration": {"keepHistoricalData": true, "streamingTimeToConsumption": 0, "dataLake": {"adlsgen2": {"format": "Pa<PERSON><PERSON>", "batchConfiguration": {"batchFrequency": 5, "maxBatchSize": 10}, "batching": true, "retentionPeriod": 7, "retentionPeriodUnit": "Days"}, "config": {"retentionPeriod": 7, "partitionCount": 3}}, "retentionPeriod": 1, "partitionCount": 1}, "additionalProperties": {"gIds": [{"value": {"linear": {"scale": 2, "offset": 5}, "texttable": {}, "identical": false}, "unit": {"identical": true, "rational": {"denominator": {}, "numerators": {}}}, "gid": 100042, "name": "instrumentClusterTime"}, {"value": {"linear": {}, "identical": true}, "unit": {"identical": false, "rational": {"denominator": {}, "numerators": {}}}, "gid": 1001, "name": "Diag.ReadUncachedDtc"}], "orderId": "bede0b1f-db3e-4a87-bec3-305289eb2588", "orderCreationId": 6627, "signalPackageEnabled": false}, "metadataProperties": {"dataEnrichment": [{"schemaId": "25124b05-29b9-4707-87f7-f8b8af17b70c", "vehicleProps": ["BRAND"]}]}}, "dao": {"creationDate": "2025-03-03T08:19:49.268238Z", "lastUpdateDate": "2025-03-03T08:19:49.268238Z", "createdBy": "managementapi", "lastUpdatedBy": "managementapi", "id": "{{daoId}}", "name": "Data access object-GSK", "description": "Some description", "dataOrderManagerEntraGroupId": "7d6fd8a6-0bca-413a-9e98-e12da7bf2b67", "dataOrderReaderEntraGroupId": "085e2137-39e8-4cb7-a33e-c27f1ca6d77f", "dataConsumerEntraGroupId": "d075d5eb-d197-42ce-b968-45009649c1af", "dataProducerEntraGroupId": "cdb3b99e-e19a-47f5-8bc0-e6ffda747eb4"}, "pipeline": {"pipelineId": "{{pipelineId}}", "category": "Custom", "dataOrders": ["{{dataOrderId}}"]}, "dataPlatformObjectStatus": [{"step": "{{securityGroupProvisioning}}", "stepId": "c36c0989-0376-41e0-b8b1-d3d7e7226568", "type": "SecurityGroup", "status": "Completed", "timeStamp": "2025-07-01T11:46:14.71316656Z", "message": null, "additionalInfo": null}, {"step": "{{kafkaTopicProvisioning}}", "stepId": "a16e92cc-5195-4a72-b6d4-11b99f0479a3", "type": "Repository", "status": "Completed", "timeStamp": "2025-07-01T11:46:14.713210271Z", "message": null, "additionalInfo": {"topics": ["private.ude-str.{{dataOrderId}}.input.json", "public.ude-str.{{dataOrderId}}.output.json", "private.ude-str.{{dataOrderId}}.non-retryable.dlq.json"], "inputTopicSchema": {"id": 100007}, "connectionInfo": {"topic": "public.ude-str.{{dataOrderId}}.output.json", "consumerGroupPrefix": "public.", "properties": {"sasl.oauthbearer.token.endpoint.url": "https://login.microsoftonline.com/c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c/oauth2/v2.0/token", "compression.type": "none", "extension.logical_cluster": "lkc-9oy7vm", "sasl.mechanism": "OAUTHBEARER", "sasl.login.callback.handler.class": "org.apache.kafka.common.security.oauthbearer.secured.OAuthBearerLoginCallbackHandler", "group.id": "public.<CONSUMER_GROUP_NAME>", "client.dns.lookup": "use_all_dns_ips", "security.protocol": "SASL_SSL", "sasl.jaas.config": "org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule required clientId=\"<SPN_CLIENT_ID>\" clientSecret=\"<SPN_CLIENT_SECRET>\" scope=\"<SPN_SCOPE>\" extension_logicalCluster=\"lkc-cluster\" extension_identityPoolId=\"pool-id\";", "extension.identity_pool_id": "pool-id"}}}}, {"step": "{{objectRegistration}}", "stepId": "8ff05478-bb1d-4952-aab5-13f595cea071", "type": "{{postgresDbObject}}", "status": "Completed", "timeStamp": "2025-07-01T11:45:27.514085097Z", "message": null, "additionalInfo": null}, {"step": "{{schemaRegistryProvisioning}}", "stepId": "5d317b01-d6f0-4b3f-ac1d-85d886e79002", "type": "SchemaRegistry", "status": "Completed", "timeStamp": "2025-07-01T11:46:14.713190755Z", "message": null, "additionalInfo": null}], "configurationStatus": "Active"}, "commandId": "f05ad021-735e-495e-9ee5-78f903efd64e"}