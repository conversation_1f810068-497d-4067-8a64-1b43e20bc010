{"commandId": "f05ad021-735e-495e-9ee5-78f903efd64e", "commandType": "UPDATE", "objectId": "{{pipelineId}}", "type": "FlinkJob<PERSON><PERSON>eline", "properties": {"dataOrderId": "{{dataOrderId}}", "schemaIds": ["{{processingSchemaId}}", "{{storageSchemaId}}"], "configuration": {"retentionPeriod": 1, "partitionCount": 1}, "additionalProperties": {"gIds": [{"value": {"linear": {"scale": 2, "offset": 5}, "texttable": {}, "identical": false}, "unit": {"identical": true, "rational": {"denominator": {}, "numerators": {}}}, "gid": 100042, "name": "instrumentClusterTime"}, {"value": {"linear": {}, "identical": true}, "unit": {"identical": false, "rational": {"denominator": {}, "numerators": {}}}, "gid": 1001, "name": "Diag.ReadUncachedDtc"}], "orderId": "{{dataOrderId}}", "orderCreationId": 6627, "signalPackageEnabled": false}, "metadataProperties": {"dataEnrichment": [{"schemaId": "{{storageSchemaId}}", "vehicleProps": ["BRAND"]}]}, "pipeline": {"pipelineId": "{{pipelineId}}", "category": "Custom"}}}