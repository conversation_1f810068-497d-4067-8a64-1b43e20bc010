{"commandId": "f05ad021-735e-495e-9ee5-78f903efd64e", "commandType": "UPDATE", "objectId": "54871773-5fcb-4602-a7e9-3d52307546eb", "type": "FlinkJob<PERSON><PERSON>eline", "properties": {"dataOrderId": "54871773-5fcb-4602-a7e9-3d52307546eb", "schemaIds": ["processingSchemaUuid", "storageSchemaUuid"], "additionalProperties": null, "metadataProperties": null, "configuration": {"keepHistoricalData": true, "streamingTimeToConsumption": 0, "dataLake": {"adlsgen2": {"format": "Pa<PERSON><PERSON>", "batchConfiguration": {"batchFrequency": 5, "maxBatchSize": 10}, "batching": true, "retentionPeriod": 7, "retentionPeriodUnit": "Days"}, "config": {"retentionPeriod": 7, "partitionCount": 3}}, "retentionPeriod": 1, "partitionCount": 1}, "pipeline": {"pipelineId": "54871773-5fcb-4602-a7e9-3d52307546eb", "category": "Custom"}}}