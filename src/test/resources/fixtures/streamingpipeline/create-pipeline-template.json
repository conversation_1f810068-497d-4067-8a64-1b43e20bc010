{"commandType": "CREATE", "objectId": "c32edd8f-a8a8-4dcd-b86e-695436d7150e", "type": "StreamingPipeline", "properties": {"name": "{{name}}", "description": "Processing of the data from the source to the destination.", "projectId": "d2a477f7-a580-461d-98ea-35a63e0c95e5", "category": "{{category}}", "mappings": [{"schemaId": "60ac1662-e35b-42d0-8a37-422ea9812c38", "dataOrders": [{"id": "{{dataOrderId}}", "name": "UseCase.24105856.TPYAAMN1065", "schemaIds": ["{{processingSchemaId}}", "{{storageSchemaId}}"], "dataOrder": {"details": {"configuration": {}}}, "metadataProperties": {"dataEnrichment": [{"schemaId": "{{storageSchemaId}}", "vehicleProps": ["VIN"]}]}, "additionalProperties": {"type": "string"}}]}], "additionalProperties": {"client": "{{client}}"}}, "commandId": "d1eba093-9425-4fa0-a184-810b316118e0"}