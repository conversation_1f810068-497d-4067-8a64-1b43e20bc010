{"commandType": "CREATE", "objectId": "c32edd8f-a8a8-4dcd-b86e-695436d7150e", "type": "StreamingPipeline", "properties": {"name": "{{name}}", "description": "Processing of the data from the source to the destination.", "projectId": "d2a477f7-a580-461d-98ea-35a63e0c95e5", "category": "{{category}}", "mappings": [{"schemaId": "60ac1662-e35b-42d0-8a37-422ea9812c38", "dataOrders": [{"id": "{{dataOrderId}}", "name": "{{name}}", "dataOrder": {"details": {"configuration": {}}}, "schemaIds": [], "metadataProperties": {"dataEnrichment": [{"schemaId": "60ac1662-e35b-42d0-8a37-422ea9812c38", "vehicleProps": ["VIN"]}]}, "additionalProperties": {"type": "string"}}]}]}, "commandId": "d1eba093-9425-4fa0-a184-810b316118e0"}