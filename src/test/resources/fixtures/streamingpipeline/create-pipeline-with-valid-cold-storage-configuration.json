{"commandType": "CREATE", "objectId": "6b80a7a8-a286-4a9d-b649-c9c2b036fe93", "type": "StreamingPipeline", "properties": {"name": "StreamingPipeline.30472465.NIYISUF1", "dataPlatformObjectStatus": null, "description": "Processing of the data from the source to the destination.", "projectId": "f595e2b7-c30e-4a2c-ac08-b3340bb4242e", "category": "Custom", "mappings": [{"schemaId": "0476daec-63d0-434a-9fac-259064c2b007", "dataOrders": [{"id": "21f9bec4-9f07-4515-a58c-4b338e1840dd", "dataOrder": {"creationDate": "2025-06-16T13:20:14.55374Z", "lastUpdateDate": "2025-06-16T13:21:06.309722Z", "createdBy": "managementapi", "lastUpdatedBy": "DEPLOYMENT_SERVICE", "id": "21f9bec4-9f07-4515-a58c-4b338e1840dd", "configurationStatus": "Active", "type": "DataOrder", "requestJobStatus": "Completed", "dataPlatformObjectId": null, "dataPlatformObjectStatus": [{"step": "Object Registration", "stepId": "8d3cf571-397f-4c8b-b256-c6b6921a0087", "type": "Postgres DB Object", "status": "Completed", "timeStamp": "2025-06-16T13:20:19.992073088Z", "message": null, "additionalInfo": null}, {"step": "Schema Registry Provisioning", "stepId": "01873046-e641-44a3-beb1-8210b4044dcf", "type": "SchemaRegistry", "status": "Completed", "timeStamp": "2025-06-16T13:21:05.998380176Z", "message": null, "additionalInfo": null}, {"step": "Kafka Topic Provisioning", "stepId": "5d4eee5e-1a8a-42f8-b581-3e83913a07ae", "type": "Repository", "status": "Completed", "timeStamp": "2025-06-16T13:21:05.998395759Z", "message": null, "additionalInfo": {"topics": ["private.ude-str.21f9bec4-9f07-4515-a58c-4b338e1840dd.input.json", "public.ude-str.21f9bec4-9f07-4515-a58c-4b338e1840dd.output.json", "private.ude-str.21f9bec4-9f07-4515-a58c-4b338e1840dd.non-retryable.dlq.json"], "connectionInfo": {"topic": "public.ude-str.21f9bec4-9f07-4515-a58c-4b338e1840dd.output.json", "properties": {"group.id": "public.<CONSUMER_GROUP_NAME>", "sasl.mechanism": "OAUTHBEARER", "compression.type": "none", "sasl.jaas.config": "org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule required clientId=\"<SPN_CLIENT_ID>\" clientSecret=\"<SPN_CLIENT_SECRET>\" scope=\"<SPN_SCOPE>\" extension_logicalCluster=\"lkc-9od2y7\" extension_identityPoolId=\"pool-O08Q9\";", "client.dns.lookup": "use_all_dns_ips", "security.protocol": "SASL_SSL", "extension.logical_cluster": "lkc-9od2y7", "extension.identity_pool_id": "pool-O08Q9", "sasl.login.callback.handler.class": "org.apache.kafka.common.security.oauthbearer.secured.OAuthBearerLoginCallbackHandler", "sasl.oauthbearer.token.endpoint.url": "https://login.microsoftonline.com/c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c/oauth2/v2.0/token"}, "consumerGroupPrefix": "public."}, "inputTopicSchema": {"id": 100046}}}, {"step": "Security Group Provisioning", "stepId": "0fbef96a-82f8-403d-af73-f3d39aac28e1", "type": "SecurityGroup", "status": "Completed", "timeStamp": "2025-06-16T13:21:05.998362331Z", "message": null, "additionalInfo": null}], "etag": null, "configurationType": "DataOrder", "details": {"name": "UseCase.Automation.ZZFALTU1", "description": "This is an UseCase for testing.", "projectId": "f595e2b7-c30e-4a2c-ac08-b3340bb4242e", "requiresDataHold": false, "orderId": "15374073-41a9-48dc-916e-c32bf67d36eb", "schemas": ["0476daec-63d0-434a-9fac-259064c2b007", "cb7095a4-2222-4c50-bbfa-40d16100eba2"], "workbenches": null, "repositories": ["KafkaTopic", "ADLSGen2"], "configuration": {"keepHistoricalData": true, "streamingTimeToConsumption": 0, "dataLake": {"adlsgen2": {"format": "Json", "batchConfiguration": {"batchFrequency": 7, "maxBatchSize": 6}, "batching": true, "retentionPeriod": 5, "retentionPeriodUnit": "Days"}, "config": {"retentionPeriod": 1, "partitionCount": 1}}, "retentionPeriod": 1, "partitionCount": 1}, "additionalProperties": {}, "metadataProperties": null, "dataAccessObjectId": "f3766b27-dbbb-42f5-ac40-6e10a5aada35", "billingProjectId": null}, "billingProjectId": null, "sapProjectId": null, "cariadLeanIxId": null, "azureUserId": null, "dataAccessObjectIdOptional": "f3766b27-dbbb-42f5-ac40-6e10a5aada35", "deleted": false}, "name": "UseCase.Automation.ZZFALTU1", "schemaIds": ["0476daec-63d0-434a-9fac-259064c2b007", "cb7095a4-2222-4c50-bbfa-40d16100eba2"], "retentionPeriod": 1, "dataLakeRetentionPeriod": 1, "metadataProperties": null, "additionalProperties": {}}]}], "additionalProperties": null}, "commandId": "bda49ed2-3121-48ba-8ae0-1b375435078c"}