{"commandType": "UPDATE", "objectId": "{{objectId}}", "type": "<PERSON><PERSON><PERSON>", "properties": {"configurationStatus": "{{#if configurationStatus}}{{configurationStatus}}{{else}}Disabled{{/if}}", "schema": {"departmentId": "33b647e4-b1a2-4835-91d0-aff6b4606088", "anonymizedMethodId": "string", "name": "Schema.VW.tiregripschema6244146813744.GOQAAQK1UUX", "version": 1, "uri": null, "format": "json", "category": "personal", "structure": {"type": "string", "$schema": "http://json-schema.org/draft-07/schema#", "required": ["anonymizedMethod", "keyId", "timestamp", "keyName", "value", "unit"], "properties": {"unit": {"type": "string"}, "keyId": {"type": "string"}, "value": {"type": "string"}, "keyName": {"type": "string"}, "timestamp": {"type": "string"}, "anonymizedMethod": {"type": "string"}}}, "storage": null}, "dataPlatformObjectStatus": [{"stepId": "c693aa41-9354-4c56-bbb9-a5f877af29e3", "step": "Object Registration", "type": "Postgres DB Object", "status": "Completed"}]}, "commandId": "0be8d291-4bf8-49aa-8b28-3fd3a3d01b00"}