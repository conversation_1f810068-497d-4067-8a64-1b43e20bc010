{"details": {"departmentId": "c693aa41-9354-4c56-bbb9-a5f877af29e3", "anonymizedMethodId": "string", "name": "Schema.VW.RTG14", "version": 1, "format": "json", "category": "personal", "structure": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["anonymizedMethod", "keyId", "timestamp", "keyName", "value", "unit"], "properties": {"anonymizedMethod": {"type": "string"}, "timestamp": {"type": "string"}, "keyId": {"type": "string"}, "keyName": {"type": "string"}, "value": {"type": "string"}, "unit": {"type": "string"}}}, "storage": null, "uri": null}, "receptorSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"payload": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["anonymizedMethod", "keyId", "timestamp", "keyName", "value", "unit"], "properties": {"anonymizedMethod": {"type": "string"}, "timestamp": {"type": "string"}, "keyId": {"type": "string"}, "keyName": {"type": "string"}, "value": {"type": "string"}, "unit": {"type": "string"}}}, "payloadProperties": {"additionalProperties": {"type": ["string", "number", "boolean"]}, "type": "object"}, "payloadUrl": {"type": "string"}, "schemaId": {"type": "string", "description": "Unique identifier of the schema in the system"}, "systemProperties": {"additionalProperties": {"type": ["string", "number", "boolean"]}, "type": "object"}, "time": {"format": "date-time", "type": "string"}, "useCaseIds": {"items": {"type": "string"}, "type": "array"}, "userId": {"type": "string", "description": "Unique identifier of user"}, "userMessageId": {"type": "string"}, "vehicleId": {"type": "string", "description": "Unique identifier of vehicle"}}, "required": ["userId", "vehicleId", "payload", "time"], "title": "Confluent Cloud Message Schema", "type": "object"}}