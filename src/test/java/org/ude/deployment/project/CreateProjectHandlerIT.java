package org.ude.deployment.project;

import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.common.infrastructure.kafka.util.Constants.BEAN_VALIDATION_FAILURE;
import static org.ude.deployment.util.KafkaUtils.getLastStatusMessage;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesDoNotContainResults;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import java.time.Duration;
import java.util.List;
import java.util.UUID;
import java.util.stream.IntStream;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;

@QuarkusTest
class CreateProjectHandlerIT extends BaseHandlerIntegrationTest {

  @DisplayName("Given a command for a project creation, when it is consumed, then it is routed.")
  @Test
  void givenACommand_whenConsume_thenRoute() {
    // Arrange
    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            UUID.randomUUID(),
            ConfigurationType.DEPARTMENT,
            new JsonObject());

    kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
        .awaitCompletion();

    // Act
    kafkaCompanion.topics().waitForTopic(statusTopic).await().indefinitely();
    var consumerRecord = getLastStatusMessage(kafkaCompanion, statusTopic);

    // Assert
    assertThat(consumerRecord).isNotNull();
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());
    var expectedOrderOfDpoStatusTypes = createExpectedDpoStatuses();
    var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
    assertDpoAndConfigStatusesMatchExpected(
        List.of(consumerRecord), expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    assertThat(dataPlatformObjectStatuses).hasSize(1);
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);
    removeDpoAndConfigurationStatus(consumerRecord.value().properties());
    assertThat(consumerRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                RequestJobStatus.COMPLETED,
                command.objectId(),
                command.type(),
                command.properties(),
                null,
                List.of()));
  }

  @DisplayName(
      "Given commands with invalid key-value pairs, when they are consumed, then they are not"
          + " routed, no status is sent, but a dead letter message is emitted for each.")
  @Test
  void givenCommandsWithInvalidKeyValues_whenConsume_thenNoRouteNoStatusSentButDeadLetterEmitted() {
    // Arrange
    var commands =
        List.of(
            new Command(
                UUID.randomUUID().toString(),
                CommandType.CREATE,
                UUID.randomUUID(),
                ConfigurationType.DEPARTMENT,
                null),
            new Command(
                UUID.randomUUID().toString(),
                null,
                UUID.randomUUID(),
                ConfigurationType.DEPARTMENT,
                new JsonObject().put("body", "<Resource Body>")),
            new Command(
                UUID.randomUUID().toString(),
                CommandType.CREATE,
                UUID.randomUUID(),
                null,
                new JsonObject().put("body", "<Resource Body>")));

    commands.forEach(
        command ->
            kafkaCompanion
                .produce(Command.class)
                .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
                .awaitCompletion());

    kafkaCompanion.topics().waitForTopic(deadLetterTopic).await().indefinitely();
    assertThat(kafkaCompanion.topics().list()).contains(deadLetterTopic);

    // Act
    var statusConsumerTask =
        kafkaCompanion
            .consume(Status.class)
            .withOffsetReset(OffsetResetStrategy.EARLIEST)
            .fromTopics(statusTopic)
            .awaitNoRecords(Duration.ofSeconds(3));

    var deadLetterConsumerTask =
        kafkaCompanion
            .consume(byte[].class)
            .withOffsetReset(OffsetResetStrategy.EARLIEST)
            .fromTopics(deadLetterTopic, 3);

    // Assert
    var statusRecords = statusConsumerTask.getRecords();
    assertThat(statusRecords).isEmpty();

    var deadLetterRecords = deadLetterConsumerTask.awaitCompletion().getRecords();
    assertThat(deadLetterRecords).hasSize(commands.size());
    deadLetterRecords.forEach(record -> assertThat(record).isNotNull());

    var expectedHeaders =
        List.of(
            new RecordHeaders()
                .add("topic", commandsTopic.getBytes())
                .add("isKey", "false".getBytes())
                .add("failureType", BEAN_VALIDATION_FAILURE.getBytes())
                .add("errorDetails", "['properties': properties is mandatory]".getBytes()),
            new RecordHeaders()
                .add("topic", commandsTopic.getBytes())
                .add("isKey", "false".getBytes())
                .add("failureType", BEAN_VALIDATION_FAILURE.getBytes())
                .add("errorDetails", "['commandType': commandType is mandatory]".getBytes()),
            new RecordHeaders()
                .add("topic", commandsTopic.getBytes())
                .add("isKey", "false".getBytes())
                .add("failureType", BEAN_VALIDATION_FAILURE.getBytes())
                .add("errorDetails", "['type': type is mandatory]".getBytes()));

    IntStream.range(0, deadLetterRecords.size())
        .forEach(
            i -> {
              var actualHeaders = deadLetterRecords.get(i).headers();
              var expHeaders = expectedHeaders.get(i);
              assertThat(actualHeaders).containsAll(expHeaders);
            });
  }

  private static @NotNull List<List<DataPlatformObjectStatus>> createExpectedDpoStatuses() {
    return List.of(
        List.of(
            DataPlatformObjectStatus.of(
                DataPlatformObjectStatus.ProvisioningStep.OBJECT_REGISTRATION,
                DataPlatformObjectStatus.ProvisioningStepType.POSTGRES_DB_OBJECT,
                ProvisioningStatus.COMPLETED)));
  }

  private static @NotNull List<ConfigurationStatus> createExpectedConfigurationStatuses() {
    return List.of(ConfigurationStatus.ACTIVE);
  }
}
