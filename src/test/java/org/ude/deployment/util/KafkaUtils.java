package org.ude.deployment.util;

import io.smallrye.reactive.messaging.kafka.companion.KafkaCompanion;
import java.util.List;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.common.errors.OffsetOutOfRangeException;
import org.ude.deployment.common.status.Status;

public class KafkaUtils {

  public static ConsumerRecord<String, Status> getLastStatusMessage(
      KafkaCompanion kafkaCompanion, String statusTopic) {
    return getLastNStatusMessages(kafkaCompanion, statusTopic, 1).getLast();
  }

  public static List<ConsumerRecord<String, Status>> getLastNStatusMessages(
      KafkaCompanion kafkaCompanion, String statusTopic, int numberOfMessages) {
    var consumerTask =
        kafkaCompanion
            .consume(Status.class)
            .withOffsetReset(OffsetResetStrategy.EARLIEST)
            .fromTopics(statusTopic, numberOfMessages);

    return consumerTask.awaitCompletion().getRecords();
  }

  /**
   * Fix for situation described in
   * https://github.com/smallrye/smallrye-reactive-messaging/discussions/1852
   */
  public static void clearTopic(KafkaCompanion kafkaCompanion, String topic) {
    if (kafkaCompanion.topics().list().contains(topic)) {
      try {
        kafkaCompanion.topics().clear(topic);
      } catch (Exception e) {
        if (!(e instanceof OffsetOutOfRangeException)) {
          throw e;
        }
      }
    }
  }
}
