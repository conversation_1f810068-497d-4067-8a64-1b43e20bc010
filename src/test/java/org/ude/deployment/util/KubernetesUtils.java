package org.ude.deployment.util;

import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.experimental.UtilityClass;

@UtilityClass
public class KubernetesUtils {
  public void loadCustomResourceDefinition(final KubernetesClient client) {
    final var yml = "/kubernetes/flinkdeployments.flink.apache.org-v1.yml";
    client.load(KubernetesUtils.class.getResourceAsStream(yml)).create();
  }
}
