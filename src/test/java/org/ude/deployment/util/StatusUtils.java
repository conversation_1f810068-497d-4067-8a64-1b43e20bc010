package org.ude.deployment.util;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import io.vertx.core.json.JsonObject;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;

public class StatusUtils {
  public static void removeDpoAndConfigurationStatus(JsonObject properties) {
    properties.remove("dataPlatformObjectStatus");
    properties.remove("configurationStatus");
  }

  public static List<DataPlatformObjectStatus> extractDataPlatformObjectStatuses(
      JsonObject properties) {
    return Optional.ofNullable(properties.getJsonArray("dataPlatformObjectStatus"))
        .map(
            jsonArray ->
                new ObjectMapper()
                    .registerModule(new JavaTimeModule())
                    .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                    .convertValue(
                        jsonArray.getList(),
                        new TypeReference<List<DataPlatformObjectStatus>>() {}))
        .orElseThrow();
  }

  public static void assertDpoAndConfigStatusesMatchExpected(
      List<ConsumerRecord<String, Status>> actualStatusRecords,
      List<List<DataPlatformObjectStatus>> expectedOrderOfDpoStatuses,
      List<ConfigurationStatus> expectedOrderOfConfigurationStatuses) {
    for (int i = 0; i < expectedOrderOfDpoStatuses.size(); i++) {
      var currentStatusRecord = actualStatusRecords.get(i);
      var actualDpoStatuses =
          extractDataPlatformObjectStatuses(currentStatusRecord.value().properties());
      var configStatus = currentStatusRecord.value().properties().getString("configurationStatus");

      assertDpoStatusesMatchExpected(actualDpoStatuses, expectedOrderOfDpoStatuses.get(i));
      assertConfigurationStatusMatchesExpected(
          configStatus, expectedOrderOfConfigurationStatuses.get(i), i);
    }
  }

  private static void assertDpoStatusesMatchExpected(
      List<DataPlatformObjectStatus> actualStatuses,
      List<DataPlatformObjectStatus> expectedStatuses) {
    for (int i = 0; i < expectedStatuses.size(); i++) {
      var expected = expectedStatuses.get(i);
      assertThat(actualStatuses)
          .withFailMessage(
              "Actual statuses: %s\nAt index %s of the DPO statuses, expected status to exist: %s",
              actualStatuses, i, expectedStatuses)
          .anyMatch(
              actual ->
                  actual.step().equals(expected.step())
                      && actual.type().equals(expected.type())
                      && actual.status().equals(expected.status()));
    }
  }

  private static void assertConfigurationStatusMatchesExpected(
      String actualConfigStatus, ConfigurationStatus expectedStatus, int index) {
    assertThat(ConfigurationStatus.fromValue(actualConfigStatus))
        .withFailMessage(
            "Configuration status at index %s was %s instead of %s",
            index, actualConfigStatus, expectedStatus)
        .isEqualTo(expectedStatus);
  }

  public static @NotNull <T> T extractResultOfType(
      List<DataPlatformObjectStatus> dataPlatformObjectStatus, Class<T> type) {
    var objectMapper =
        new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .registerModule(new Jdk8Module())
            .registerModule(new ParameterNamesModule());
    return dataPlatformObjectStatus.stream()
        .filter(
            dpoStatus -> {
              var info = dpoStatus.additionalInfo();
              return info != null && objectMapper.convertValue(info, type) != null;
            })
        .map(dpoStatus -> objectMapper.convertValue(dpoStatus.additionalInfo(), type))
        .findFirst()
        .orElseThrow();
  }

  public static void assertDpoStatusesDoNotContainResults(
      List<DataPlatformObjectStatus> dataPlatformObjectStatus) {
    assertThat(dataPlatformObjectStatus).allMatch(dpoStatus -> dpoStatus.additionalInfo() == null);
  }

  public static void assertDpoStatusesReportedNoErrors(
      List<DataPlatformObjectStatus> dataPlatformObjectStatus) {
    assertThat(dataPlatformObjectStatus).allMatch(dpoStatus -> dpoStatus.message() == null);
  }

  public static void assertDpoStatusReportedError(
      List<DataPlatformObjectStatus> dataPlatformObjectStatuses,
      DataPlatformObjectStatus.ProvisioningStep provisioningStep,
      String errorMessage) {
    assertThat(dataPlatformObjectStatuses)
        .anyMatch(
            dpoStatus ->
                dpoStatus.step().equals(provisioningStep)
                    && dpoStatus.message().equals(errorMessage));
  }

  public static void assertDpoStatusesReportedError(
      List<DataPlatformObjectStatus> dataPlatformObjectStatuses, String errorMessage) {
    assertThat(dataPlatformObjectStatuses)
        .anyMatch(
            dpoStatus -> dpoStatus.message() != null && dpoStatus.message().equals(errorMessage));
  }

  public static class PipelineUtils {
    public static @NotNull List<List<DataPlatformObjectStatus>>
        createExpectedDpoStatusesForCompletedCreation() {
      var dbDpoStatuses =
          List.of(
              List.of(
                  DataPlatformObjectStatus.of(
                      DataPlatformObjectStatus.ProvisioningStep.OBJECT_REGISTRATION,
                      DataPlatformObjectStatus.ProvisioningStepType.POSTGRES_DB_OBJECT,
                      ProvisioningStatus.COMPLETED),
                  DataPlatformObjectStatus.of(
                      DataPlatformObjectStatus.ProvisioningStep.UPDATE_PIPELINES_MAPPING_LIST,
                      DataPlatformObjectStatus.ProvisioningStepType.POSTGRES_DB_OBJECT,
                      ProvisioningStatus.COMPLETED)));

      var pipelineChannelDpoStatuses =
          List.of(
              List.of(
                  DataPlatformObjectStatus.of(
                      DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING,
                      DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                      ProvisioningStatus.PROVISIONING)),
              List.of(
                  DataPlatformObjectStatus.of(
                      DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING,
                      DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                      ProvisioningStatus.COMPLETED)));

      var dpoStatuses = new ArrayList<>(dbDpoStatuses);
      dpoStatuses.addAll(pipelineChannelDpoStatuses);
      return dpoStatuses;
    }

    public static List<RequestJobStatus> createExpectedRequestJobStatuses() {
      return List.of(
          RequestJobStatus.PROVISIONING, RequestJobStatus.PROVISIONING, RequestJobStatus.COMPLETED);
    }

    public static List<ConfigurationStatus> createExpectedConfigurationStatuses() {
      return List.of(
          ConfigurationStatus.IN_PROVISIONING,
          ConfigurationStatus.IN_PROVISIONING,
          ConfigurationStatus.ACTIVE);
    }
  }
}
