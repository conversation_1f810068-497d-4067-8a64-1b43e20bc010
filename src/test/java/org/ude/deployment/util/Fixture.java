package org.ude.deployment.util;

import io.quarkus.deployment.util.FileUtil;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import wiremock.com.github.jknack.handlebars.Handlebars;

@UtilityClass
public class Fixture { // do not add non resource-fixtures-related functionality here
  @SneakyThrows(IOException.class)
  public byte[] asBytes(final String name) {
    return FileUtil.readFileContents(
        Objects.requireNonNull(
            Fixture.class.getClassLoader().getResourceAsStream("/fixtures/%s".formatted(name))));
  }

  public String asString(final String name) {
    return new String(asBytes(name), StandardCharsets.UTF_8);
  }

  @SneakyThrows(IOException.class)
  public String asString(final String name, final Map<String, String> params) {
    return new Handlebars().compileInline(new String(asBytes(name))).apply(params);
  }
}
