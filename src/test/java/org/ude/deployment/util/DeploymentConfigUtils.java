package org.ude.deployment.util;

import java.util.Map;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.azureblobstorage.RetentionPeriod;
import org.ude.deployment.flink.dto.DeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.dto.GdcFlinkDeploymentConfig;
import org.ude.deployment.flink.dto.PassthroughDeploymentConfig;

// just a trash class to wrap a copy-paste

@UtilityClass
public class DeploymentConfigUtils {
  public Map<String, String> deploymentConfig(final String dataOrderId) {
    return deploymentConfig(DeploymentType.GDC_PIPELINE, dataOrderId, Map.of());
  }

  @SneakyThrows
  public Map<String, String> deploymentConfig(
      final DeploymentType deploymentType,
      final String dataOrderId,
      final Map<String, String> extra) {
    final var flinkEnvVariableConfig = new FlinkEnvVariableConfig();
    final var defaults =
        new java.util.HashMap<>(
            Map.of(
                "namespace", "test-namespace",
                "flinkGdcJobName", getDeploymentName(DeploymentType.GDC_PIPELINE, dataOrderId),
                "flinkPassthroughJobName", "passthrough-job-name",
                "schemaRegistry", "localhost:8081",
                "bootstrapServer", "localhost:8082",
                "databaseUrl", "localhost:8081",
                "databaseUserName", "user",
                "claimsDatabaseName", "dbName",
                "claimsTableName", "tableName"));

    defaults.putAll(extra);

    for (final var e : defaults.entrySet()) {
      FlinkEnvVariableConfig.class.getField(e.getKey()).set(flinkEnvVariableConfig, e.getValue());
    }

    final var deploymentConfig =
        new DeploymentConfig(
            DeploymentType.GDC_PIPELINE,
            dataOrderId,
            "streaming-pipeline",
            "my-project-id",
            RetentionPeriod.ofDays(1),
            deploymentType.equals(DeploymentType.GDC_PIPELINE)
                ? createGdcDeploymentConfig(dataOrderId)
                : createPassthroughDeploymentConfig(dataOrderId));

    return deploymentConfig
        .flinkDeploymentConfig()
        .getPropertiesMap(flinkEnvVariableConfig, deploymentConfig);
  }

  private static GdcFlinkDeploymentConfig createGdcDeploymentConfig(final String dataOrderId) {
    return new GdcFlinkDeploymentConfig(
        getDeploymentName(DeploymentType.GDC_PIPELINE, dataOrderId),
        "flink-app",
        "busybox",
        "input-topic",
        "output-topic",
        "vms-topic",
        "signals",
        "normalization_rule",
        "non-retryable-error-topic",
        "storage-schema-uuid-1",
        "[]",
        "[]",
        false);
  }

  private static PassthroughDeploymentConfig createPassthroughDeploymentConfig(
      final String dataOrderId) {
    return new PassthroughDeploymentConfig(
        getDeploymentName(DeploymentType.PASSTHROUGH_PIPELINE, dataOrderId),
        "passthrough-job",
        "input-topic",
        "output-topic",
        "non-retryable-error-topic");
  }

  public static @NotNull String getDeploymentName(
      DeploymentType deploymentType, String dataOrderId) {
    return "%s-%s".formatted(deploymentType.getPrefix(), dataOrderId);
  }
}
