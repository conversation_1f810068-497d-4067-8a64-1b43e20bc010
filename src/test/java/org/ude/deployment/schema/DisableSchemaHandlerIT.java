package org.ude.deployment.schema;

import static com.github.tomakehurst.wiremock.client.WireMock.delete;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.notFound;
import static com.github.tomakehurst.wiremock.client.WireMock.ok;
import static com.github.tomakehurst.wiremock.client.WireMock.okJson;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathTemplate;
import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusReportedError;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.extractResultOfType;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import io.confluent.kafka.schemaregistry.client.rest.entities.ErrorMessage;
import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.common.wiremock.InjectWireMock;
import org.ude.deployment.common.wiremock.WiremockResource;
import org.ude.deployment.util.Fixture;
import org.ude.deployment.util.KafkaUtils;

@QuarkusTest
@QuarkusTestResource(WiremockResource.class)
class DisableSchemaHandlerIT extends BaseHandlerIntegrationTest {
  @InjectWireMock private WireMockServer wiremock;

  @Inject private ObjectMapper objectMapper;

  @BeforeEach
  void beforeEach() {
    this.wiremock.resetAll();
  }

  @Nested
  class SchemaNotFound {
    @BeforeEach
    void beforeEach() throws JsonProcessingException {
      wiremock.stubFor(
          get(urlPathTemplate("/subjects/{subject}/versions"))
              .willReturn(
                  notFound()
                      .withBody(
                          objectMapper.writeValueAsString(
                              new ErrorMessage(40401, "Subject not found")))));
    }

    @Test
    void handleNonExistentSchema() throws IOException {
      final var cmd =
          objectMapper.readValue(
              Fixture.asString(
                  "schema/update/command-valid-schema-disabled-template.json",
                  Map.of("objectId", UUID.randomUUID().toString())),
              Command.class);
      final var statusRecords = awaitCommandResults(cmd, 2);
      final var statusRecord = statusRecords.getLast();
      var expectedOrderOfDpoStatusTypes =
          createExpectedDpoStatusesForDeletion(ProvisioningStatus.FAILED);
      var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
      assertDpoAndConfigStatusesMatchExpected(
          statusRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
      assertThat(statusRecord.value())
          .satisfies(
              status -> {
                assertThat(status.type()).isEqualTo(ConfigurationType.SCHEMA);
                assertThat(status.commandType()).isEqualTo(CommandType.UPDATE);
                assertThat(status.status()).isEqualTo(RequestJobStatus.FAILED);
                assertDpoStatusReportedError(
                    extractDataPlatformObjectStatuses(status.properties()),
                    DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                    "Data platform object provisioning failed.\n Subject not found; error code: 40401");
              });
    }
  }

  private List<ConfigurationStatus> createExpectedConfigurationStatuses() {
    return List.of(ConfigurationStatus.IN_PROVISIONING, ConfigurationStatus.DISABLED);
  }

  private List<List<DataPlatformObjectStatus>> createExpectedDpoStatusesForDeletion(
      ProvisioningStatus finalProvisioningStatus) {
    return List.of(
        List.of(
            DataPlatformObjectStatus.of(
                DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                DataPlatformObjectStatus.ProvisioningStepType.SCHEMA_REGISTRY,
                ProvisioningStatus.DELETING),
            DataPlatformObjectStatus.of(
                DataPlatformObjectStatus.ProvisioningStep.OBJECT_REGISTRATION,
                DataPlatformObjectStatus.ProvisioningStepType.POSTGRES_DB_OBJECT,
                ProvisioningStatus.COMPLETED)),
        List.of(
            DataPlatformObjectStatus.of(
                DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                DataPlatformObjectStatus.ProvisioningStepType.SCHEMA_REGISTRY,
                finalProvisioningStatus)));
  }

  @Nested
  class UnableToDeleteSchemaVersion {
    @BeforeEach
    void beforeEach() throws JsonProcessingException {
      wiremock.stubFor(
          get(urlPathTemplate("/subjects/{subject}/versions")).willReturn(okJson("[1, 2]")));
      wiremock.stubFor(
          delete(urlPathTemplate("/subjects/{subject}/versions/{version}"))
              .willReturn(
                  notFound()
                      .withBody(
                          objectMapper.writeValueAsString(
                              new ErrorMessage(40402, "Version not found")))));
    }

    @Test
    void handleExceptionInVersionDeletion() throws IOException {
      final var cmd =
          objectMapper.readValue(
              Fixture.asString(
                  "schema/update/command-valid-schema-disabled-template.json",
                  Map.of("objectId", UUID.randomUUID().toString())),
              Command.class);
      final var statusRecords = awaitCommandResults(cmd, 2);
      final var statusRecord = statusRecords.getLast();
      var expectedOrderOfDpoStatusTypes =
          createExpectedDpoStatusesForDeletion(ProvisioningStatus.FAILED);
      var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
      assertDpoAndConfigStatusesMatchExpected(
          statusRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
      assertThat(statusRecord.value())
          .satisfies(
              status -> {
                assertThat(status.status()).isEqualTo(RequestJobStatus.FAILED);
                assertDpoStatusReportedError(
                    extractDataPlatformObjectStatuses(status.properties()),
                    DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                    """
                                Multiple exceptions caught:
                                \t[Exception 0] org.ude.deployment.common.infrastructure.exception.DataPlatformObjectProvisionException: Data platform object provisioning failed.
                                 Version not found; error code: 40402
                                \t[Exception 1] org.ude.deployment.common.infrastructure.exception.DataPlatformObjectProvisionException: Data platform object provisioning failed.
                                 Version not found; error code: 40402""");
              });
    }
  }

  @Nested
  class CommandSucceed {
    @BeforeEach
    void beforeEach() {
      wiremock.stubFor(
          get(urlPathTemplate("/subjects/{subject}/versions")).willReturn(okJson("[1, 2]")));
      wiremock.stubFor(
          delete(urlPathTemplate("/subjects/{subject}/versions/{version}"))
              .willReturn(
                  ok().withBody("{{request.path.version}}").withTransformers("response-template")));
    }

    @Test
    void handleVersionsDeletion() throws IOException {
      final var cmd =
          objectMapper.readValue(
              Fixture.asString(
                  "schema/update/command-valid-schema-disabled-template.json",
                  Map.of("objectId", UUID.randomUUID().toString())),
              Command.class);
      final var statusRecords = awaitCommandResults(cmd, 2);
      final var statusRecord = statusRecords.getLast();
      var expectedOrderOfDpoStatusTypes =
          createExpectedDpoStatusesForDeletion(ProvisioningStatus.DELETED);
      var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
      assertDpoAndConfigStatusesMatchExpected(
          statusRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
      assertThat(statusRecord.value())
          .satisfies(
              status -> {
                assertThat(status.status()).isEqualTo(RequestJobStatus.COMPLETED);
                var dataPlatformObjectStatuses =
                    extractDataPlatformObjectStatuses(status.properties());
                assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
                var result = extractResultOfType(dataPlatformObjectStatuses, Map.class);

                var softDeletedVersions = new ArrayList<Integer>();
                if (result.get("softDeletedVersions") instanceof ArrayList) {
                  softDeletedVersions = (ArrayList<Integer>) result.get("softDeletedVersions");
                }

                assertThat(softDeletedVersions)
                    .isNotNull()
                    .isInstanceOf(ArrayList.class)
                    .contains(1, 2);
              });
    }
  }

  @ParameterizedTest
  @MethodSource("nonSupportedCommands")
  void handleNonSupportedCommand(final Command cmd) {
    final var statusRecord = this.awaitCommandResult(cmd);
    var expectedOrderOfDpoStatusTypes =
        List.of(createExpectedDpoStatusesForDeletion(ProvisioningStatus.FAILED).getLast());
    var orderOfConfigurationStatuses = List.of(createExpectedConfigurationStatuses().getLast());
    assertDpoAndConfigStatusesMatchExpected(
        List.of(statusRecord), expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
    assertThat(statusRecord.value())
        .satisfies(
            status -> {
              assertThat(status.status()).isEqualTo(RequestJobStatus.FAILED);
              var dataPlatformObjectStatuses =
                  extractDataPlatformObjectStatuses(status.properties());
              assertDpoStatusReportedError(
                  dataPlatformObjectStatuses,
                  DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                  "Data platform object validation failed.\n Unsupported command provided");
            });
  }

  @Test
  void handleNonSupportedConfigurationStatus() throws JsonProcessingException {
    final var cmd =
        this.objectMapper.readValue(
            Fixture.asString(
                "schema/update/command-valid-schema-disabled-template.json",
                Map.of("objectId", UUID.randomUUID().toString(), "configurationStatus", "Invalid")),
            Command.class);
    final var statusRecord = this.awaitCommandResult(cmd);
    var expectedOrderOfDpoStatusTypes =
        List.of(createExpectedDpoStatusesForDeletion(ProvisioningStatus.FAILED).getLast());
    var orderOfConfigurationStatuses = List.of(createExpectedConfigurationStatuses().getLast());
    assertDpoAndConfigStatusesMatchExpected(
        List.of(statusRecord), expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
    assertThat(statusRecord.value())
        .satisfies(
            status -> {
              assertThat(status.status()).isEqualTo(RequestJobStatus.FAILED);
              var dataPlatformObjectStatuses =
                  extractDataPlatformObjectStatuses(status.properties());
              assertDpoStatusReportedError(
                  dataPlatformObjectStatuses,
                  DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                  "Data platform object validation failed.\n Configuration status not supported");
            });
  }

  private static List<Command> nonSupportedCommands() {
    final var cmd =
        Command.builder()
            .commandId(UUID.randomUUID().toString())
            .commandType(CommandType.UPDATE)
            .type(ConfigurationType.SCHEMA)
            .objectId(UUID.randomUUID());
    return List.of(
        cmd.properties(JsonObject.of()).build(),
        cmd.properties(JsonObject.of("configurationStatus", JsonObject.of())).build(),
        cmd.properties(JsonObject.of("schema", JsonObject.of())).build());
  }

  private ConsumerRecord<String, Status> awaitCommandResult(final Command cmd) {
    return awaitCommandResults(cmd, 1).getFirst();
  }

  private List<ConsumerRecord<String, Status>> awaitCommandResults(
      final Command cmd, final int numberOfStatuses) {
    this.kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(this.commandsTopic, cmd.commandId(), cmd))
        .awaitCompletion();
    return KafkaUtils.getLastNStatusMessages(kafkaCompanion, statusTopic, numberOfStatuses);
  }
}
