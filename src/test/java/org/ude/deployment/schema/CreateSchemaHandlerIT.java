package org.ude.deployment.schema;

import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.okJson;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathTemplate;
import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.common.infrastructure.kafka.util.Constants.BEAN_VALIDATION_FAILURE;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesDoNotContainResults;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedError;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.extractResultOfType;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import com.github.tomakehurst.wiremock.WireMockServer;
import io.confluent.kafka.schemaregistry.client.rest.entities.requests.RegisterSchemaResponse;
import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.common.wiremock.InjectWireMock;
import org.ude.deployment.common.wiremock.WiremockResource;
import org.ude.deployment.util.Fixture;
import org.ude.deployment.util.KafkaUtils;

@QuarkusTest
@QuarkusTestResource(WiremockResource.class)
class CreateSchemaHandlerIT extends BaseHandlerIntegrationTest {
  @InjectWireMock WireMockServer wiremock;
  private final String stubSchema =
      """
                  {
                      "id": 333,
                      "version": 1,
                      "schemaType": "JSON",
                      "schema": "{}"
                  }""";

  @BeforeAll
  public void init() {
    initSchemaRegistryWiremock();
  }

  @DisplayName("Given a command for a schema creation, when it is consumed, then it is routed.")
  @Test
  void givenACommand_whenConsume_thenRoute() {
    // Arrange
    var commandProperties =
        new JsonObject(Fixture.asString("schema/create/SchemaCommandProperties.json"));

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            UUID.randomUUID(),
            ConfigurationType.SCHEMA,
            commandProperties);

    kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
        .awaitCompletion();

    // Act
    var consumerRecords = KafkaUtils.getLastNStatusMessages(kafkaCompanion, statusTopic, 3);
    var consumerRecord = consumerRecords.getLast();

    // Assert
    assertThat(consumerRecord).isNotNull();
    var expectedOrderOfDpoStatusTypes =
        createExpectedDpoStatusesForCreation(ProvisioningStatus.COMPLETED);
    var orderOfConfigurationStatuses =
        createExpectedConfigurationStatusesFor(ConfigurationStatus.ACTIVE);
    assertDpoAndConfigStatusesMatchExpected(
        consumerRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    var actualResult =
        extractResultOfType(dataPlatformObjectStatuses, RegisterSchemaResponse.class);
    var expectedResult = new JsonObject(stubSchema).mapTo(RegisterSchemaResponse.class);
    assertThat(actualResult).isEqualTo(expectedResult);
    removeDpoAndConfigurationStatus(consumerRecord.value().properties());
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());
    assertThat(consumerRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                RequestJobStatus.COMPLETED,
                command.objectId(),
                command.type(),
                command.properties(),
                new JsonObject(stubSchema),
                List.of()));
  }

  @DisplayName(
      "Given a command for a schema creation with wrong properties, when it is consumed, then"
          + " it is routed with errors info.")
  @Test
  void givenAWrongCommand_whenConsume_thenRouteWithErrors() throws InterruptedException {
    // Arrange
    var commandProperties =
        new JsonObject(Fixture.asString("schema/create/SchemaCommandProperties.json"));
    commandProperties.remove("receptorSchema");

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            UUID.randomUUID(),
            ConfigurationType.SCHEMA,
            commandProperties);

    kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
        .awaitCompletion();

    TimeUnit.SECONDS.sleep(1);

    // Act
    var consumerRecords = KafkaUtils.getLastNStatusMessages(kafkaCompanion, statusTopic, 2);
    var consumerRecord = consumerRecords.getLast();

    // Assert
    assertThat(consumerRecord).isNotNull();
    var expectedOrderOfDpoStatusTypes =
        createExpectedDpoStatusesForFailedCreation(ProvisioningStatus.FAILED);
    var orderOfConfigurationStatuses = createExpectedConfigurationStatusesForFailedValidation();
    assertDpoAndConfigStatusesMatchExpected(
        consumerRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    assertDpoStatusesReportedError(
        dataPlatformObjectStatuses,
        "Data platform object validation failed.\n 'receptorSchema' is missing in the command properties");
    assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);
    removeDpoAndConfigurationStatus(consumerRecord.value().properties());
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());
    assertThat(consumerRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                RequestJobStatus.FAILED,
                command.objectId(),
                command.type(),
                command.properties(),
                null,
                List.of(
                    "Data platform object validation failed.\n 'receptorSchema' is missing in the command properties")));
  }

  private static @NotNull List<List<DataPlatformObjectStatus>>
      createExpectedDpoStatusesForFailedCreation(ProvisioningStatus provisioningStatus) {
    return List.of(
        List.of(
            DataPlatformObjectStatus.of(
                DataPlatformObjectStatus.ProvisioningStep.OBJECT_REGISTRATION,
                DataPlatformObjectStatus.ProvisioningStepType.POSTGRES_DB_OBJECT,
                ProvisioningStatus.COMPLETED),
            DataPlatformObjectStatus.of(
                DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                DataPlatformObjectStatus.ProvisioningStepType.SCHEMA_REGISTRY,
                ProvisioningStatus.NOT_STARTED)),
        List.of(
            DataPlatformObjectStatus.of(
                DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                DataPlatformObjectStatus.ProvisioningStepType.SCHEMA_REGISTRY,
                provisioningStatus)));
  }

  private static @NotNull List<List<DataPlatformObjectStatus>> createExpectedDpoStatusesForCreation(
      ProvisioningStatus provisioningStatus) {
    List<List<DataPlatformObjectStatus>> orderOfStatuses =
        new ArrayList<>(
            List.of(
                List.of(
                    DataPlatformObjectStatus.of(
                        DataPlatformObjectStatus.ProvisioningStep.OBJECT_REGISTRATION,
                        DataPlatformObjectStatus.ProvisioningStepType.POSTGRES_DB_OBJECT,
                        ProvisioningStatus.COMPLETED),
                    DataPlatformObjectStatus.of(
                        DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                        DataPlatformObjectStatus.ProvisioningStepType.SCHEMA_REGISTRY,
                        ProvisioningStatus.NOT_STARTED)),
                List.of(
                    DataPlatformObjectStatus.of(
                        DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                        DataPlatformObjectStatus.ProvisioningStepType.SCHEMA_REGISTRY,
                        ProvisioningStatus.PROVISIONING)),
                List.of(
                    DataPlatformObjectStatus.of(
                        DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                        DataPlatformObjectStatus.ProvisioningStepType.SCHEMA_REGISTRY,
                        provisioningStatus))));
    if (provisioningStatus.equals(ProvisioningStatus.FAILED)) {
      orderOfStatuses.remove(1);
    }
    return orderOfStatuses;
  }

  public static List<ConfigurationStatus> createExpectedConfigurationStatusesFor(
      ConfigurationStatus configurationStatus) {
    return List.of(
        ConfigurationStatus.IN_PROVISIONING,
        ConfigurationStatus.IN_PROVISIONING,
        configurationStatus);
  }

  public static List<ConfigurationStatus> createExpectedConfigurationStatusesForFailedValidation() {
    var expectedConfigurationStatuses =
        new ArrayList<>(createExpectedConfigurationStatusesFor(ConfigurationStatus.DISABLED));
    expectedConfigurationStatuses.remove(1);
    return expectedConfigurationStatuses;
  }

  @DisplayName(
      "Given commands with invalid data, when they are consumed, then they are not routed, no"
          + " status is sent, but a dead letter message is emitted for each.")
  @Test
  void givenCommandsWithInvalidKeyValues_whenConsume_thenNoRouteNoStatusSentButDeadLetterEmitted() {
    // Arrange
    var commands =
        List.of(
            new Command(
                UUID.randomUUID().toString(),
                CommandType.CREATE,
                UUID.randomUUID(),
                ConfigurationType.SCHEMA,
                null),
            new Command(
                UUID.randomUUID().toString(),
                null,
                UUID.randomUUID(),
                ConfigurationType.SCHEMA,
                new JsonObject().put("body", "<Resource Body>")),
            new Command(
                UUID.randomUUID().toString(),
                CommandType.CREATE,
                UUID.randomUUID(),
                null,
                new JsonObject().put("body", "<Resource Body>")));

    commands.forEach(
        command ->
            kafkaCompanion
                .produce(Command.class)
                .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
                .awaitCompletion());

    kafkaCompanion.topics().waitForTopic(deadLetterTopic).await().indefinitely();
    assertThat(kafkaCompanion.topics().list()).contains(deadLetterTopic);

    // Act
    var statusConsumerTask =
        kafkaCompanion
            .consume(Status.class)
            .withOffsetReset(OffsetResetStrategy.EARLIEST)
            .fromTopics(statusTopic)
            .awaitNoRecords(Duration.ofSeconds(3));

    var deadLetterConsumerTask =
        kafkaCompanion
            .consume(byte[].class)
            .withOffsetReset(OffsetResetStrategy.EARLIEST)
            .fromTopics(deadLetterTopic, 3);

    // Assert
    var statusRecords = statusConsumerTask.getRecords();
    assertThat(statusRecords).isEmpty();

    var deadLetterRecords = deadLetterConsumerTask.awaitCompletion().getRecords();
    assertThat(deadLetterRecords).hasSize(commands.size());
    deadLetterRecords.forEach(consumerRecord -> assertThat(consumerRecord).isNotNull());

    var expectedHeaders =
        List.of(
            new RecordHeaders()
                .add("topic", commandsTopic.getBytes())
                .add("isKey", "false".getBytes())
                .add("failureType", BEAN_VALIDATION_FAILURE.getBytes())
                .add("errorDetails", "['properties': properties is mandatory]".getBytes()),
            new RecordHeaders()
                .add("topic", commandsTopic.getBytes())
                .add("isKey", "false".getBytes())
                .add("failureType", BEAN_VALIDATION_FAILURE.getBytes())
                .add("errorDetails", "['commandType': commandType is mandatory]".getBytes()),
            new RecordHeaders()
                .add("topic", commandsTopic.getBytes())
                .add("isKey", "false".getBytes())
                .add("failureType", BEAN_VALIDATION_FAILURE.getBytes())
                .add("errorDetails", "['type': type is mandatory]".getBytes()));

    IntStream.range(0, deadLetterRecords.size())
        .forEach(
            i -> {
              var actualHeaders = deadLetterRecords.get(i).headers();
              var expHeaders = expectedHeaders.get(i);
              assertThat(actualHeaders).containsAll(expHeaders);
            });
  }

  private void initSchemaRegistryWiremock() {
    wiremock.stubFor(
        get(urlPathTemplate("/subjects/{subject}/versions/latest"))
            .atPriority(1)
            .willReturn(okJson(stubSchema)));
    wiremock.stubFor(
        get(urlPathTemplate("/subjects/{subject}/versions"))
            .atPriority(3)
            .willReturn(okJson(stubSchema)));

    wiremock.stubFor(
        post(urlPathTemplate("/subjects/{subject}")).atPriority(1).willReturn(okJson(stubSchema)));

    wiremock.stubFor(
        post(urlPathTemplate("/subjects/{subject}/versions"))
            .atPriority(1)
            .willReturn(okJson(stubSchema)));
  }
}
