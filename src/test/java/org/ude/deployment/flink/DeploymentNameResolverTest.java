package org.ude.deployment.flink;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.Test;

class DeploymentNameResolverTest {

  @Test
  void givenExistingTemplate_whenResolve_thenTemplateIsReplacedCorrectly() {
    // Arrange
    var deploymentNameTemplate = "{deploymentType}-{dataOrderId}";
    var jobPrefix = "gdc";
    var dataOrderId = "54871773-5fcb-4602-a7e9-3d52307546eb";
    var resolver = new DeploymentNameResolver(deploymentNameTemplate);

    // Act
    var result = resolver.resolve(jobPrefix, dataOrderId);

    // Assert
    assertThat(result).isEqualTo("gdc-54871773-5fcb-4602-a7e9-3d52307546eb");
  }

  @Test
  void testResolve_DeploymentNameTemplateIsNotSet_ThrowsNullPointerException() {
    // Arrange
    var resolver = new DeploymentNameResolver(null);

    // Act
    // Assert
    assertThrows(NullPointerException.class, () -> resolver.resolve("jobPrefix", "dataOrderId"));
  }

  @Test
  void testResolve_JobPrefixIsNull_TemplateIsReplacedCorrectly() {
    // Arrange
    var deploymentNameTemplate = "{deploymentType}-{dataOrderId}";
    var dataOrderId = "54871773-5fcb-4602-a7e9-3d52307546eb";
    var resolver = new DeploymentNameResolver(deploymentNameTemplate);

    // Act
    // Assert
    assertThrows(NullPointerException.class, () -> resolver.resolve(null, dataOrderId));
  }

  @Test
  void testResolve_DataOrderIdIsNull_TemplateIsReplacedCorrectly() {
    // Arrange
    var deploymentNameTemplate = "{deploymentType}-{dataOrderId}";
    var jobPrefix = "gdc";
    var resolver = new DeploymentNameResolver(deploymentNameTemplate);

    // Act
    // Assert
    assertThrows(NullPointerException.class, () -> resolver.resolve(jobPrefix, null));
  }

  @Test
  void testResolve_DeploymentNameTemplateDoesNotContainPlaceholders_TemplateIsReturnedAsIs() {
    // Arrange
    var deploymentNameTemplate = "my-template";
    var jobPrefix = "gdc";
    var dataOrderId = "54871773-5fcb-4602-a7e9-3d52307546eb";
    var resolver = new DeploymentNameResolver(deploymentNameTemplate);

    // Act
    var result = resolver.resolve(jobPrefix, dataOrderId);

    // Assert
    assertThat(result).isEqualTo("my-template");
  }
}
