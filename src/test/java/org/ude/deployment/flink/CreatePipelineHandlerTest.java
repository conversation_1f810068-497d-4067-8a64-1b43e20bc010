package org.ude.deployment.flink;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.ude.deployment.common.command.CommandUtils.mapToType;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.fabric8.kubernetes.client.KubernetesClientException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.flink.dto.BaseDeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.service.FlinkDeploymentService;

class CreatePipelineHandlerTest {

  @Mock private ObjectMapper objectMapper;

  @Mock private FlinkDeploymentService flinkDeploymentService;

  @Mock private StatusUpdateService statusUpdateService;

  @Captor private ArgumentCaptor<List<ProvisioningStep>> provisioningStepCaptor;

  @Mock FlinkEnvVariableConfig flinkEnvVariableConfig;

  @InjectMocks private CreatePipelineHandler createPipelineHandler;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @DisplayName("Given a valid command, when handling it, then correct status message is sent")
  @ParameterizedTest(name = "Deployment type: {0}, Deployment manifest name: {1}")
  @MethodSource("successfulDeploymentArguments")
  void handleSuccessfullyCreatesDeployment(
      DeploymentType deploymentType, String deploymentManifestName) {
    // Arrange
    var command = mock(Command.class);
    var deploymentConfig = mock(DeploymentConfig.class);
    var baseDeploymentConfig = mock(BaseDeploymentConfig.class);
    when(objectMapper.convertValue(any(), eq(DeploymentConfig.class))).thenReturn(deploymentConfig);
    when(deploymentConfig.deploymentType()).thenReturn(deploymentType);
    when(deploymentConfig.flinkDeploymentConfig()).thenReturn(baseDeploymentConfig);
    when(baseDeploymentConfig.getPropertiesMap(flinkEnvVariableConfig, deploymentConfig))
        .thenReturn(Map.of());
    when(flinkDeploymentService.createIfNotExists(anyString(), anyMap()))
        .thenReturn(Optional.empty());
    when(statusUpdateService.initializeWithCompletedSteps(
            any(Command.class), eq(CommandType.CREATE), anyList(), anyList()))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.provisioning(
            any(Command.class), eq(CommandType.CREATE), any(ProvisioningStep.class)))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.completed(
            any(Command.class), eq(CommandType.CREATE), any(ProvisioningStep.class), eq(null)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    createPipelineHandler.handle(command).toCompletableFuture().join();

    // Assert
    verify(flinkDeploymentService).createIfNotExists(deploymentManifestName, Map.of());
    ArgumentCaptor<List<ProvisioningStep>> provisioningStepArgumentCaptor =
        ArgumentCaptor.forClass(List.class);
    verify(statusUpdateService)
        .initializeWithCompletedSteps(
            eq(mapToType(command, ConfigurationType.STREAMING_PIPELINE)),
            eq(CommandType.CREATE),
            provisioningStepCaptor.capture(),
            provisioningStepArgumentCaptor.capture());
    assertThat(provisioningStepCaptor.getValue())
        .hasSize(2)
        .isEqualTo(
            List.of(
                ProvisioningStep.OBJECT_REGISTRATION,
                ProvisioningStep.UPDATE_PIPELINES_MAPPING_LIST));
    assertThat(provisioningStepArgumentCaptor.getValue())
        .hasSize(1)
        .isEqualTo(List.of(ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
    verify(statusUpdateService)
        .provisioning(
            eq(mapToType(command, ConfigurationType.STREAMING_PIPELINE)),
            eq(CommandType.CREATE),
            eq(ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
    verify(statusUpdateService)
        .completed(
            eq(mapToType(command, ConfigurationType.STREAMING_PIPELINE)),
            eq(CommandType.CREATE),
            eq(ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING),
            eq(null));
  }

  private static Stream<Arguments> successfulDeploymentArguments() {
    return Stream.of(
        Arguments.of(DeploymentType.GDC_PIPELINE, "gdc-flink-deployment.yaml"),
        Arguments.of(DeploymentType.PASSTHROUGH_PIPELINE, "passthrough-flink-deployment.yaml"));
  }

  @Test
  @DisplayName("When an exception, correct status message is sent")
  void handleFailsToCreateDeployment() {
    var command = mock(Command.class);
    var deploymentConfig = mock(DeploymentConfig.class);
    var baseDeploymentConfig = mock(BaseDeploymentConfig.class);
    when(objectMapper.convertValue(any(), eq(DeploymentConfig.class))).thenReturn(deploymentConfig);
    when(deploymentConfig.deploymentType()).thenReturn(DeploymentType.GDC_PIPELINE);
    when(deploymentConfig.flinkDeploymentConfig()).thenReturn(baseDeploymentConfig);
    when(baseDeploymentConfig.getPropertiesMap(flinkEnvVariableConfig, deploymentConfig))
        .thenReturn(Map.of());
    when(flinkDeploymentService.createIfNotExists(anyString(), anyMap()))
        .thenThrow(new KubernetesClientException("Deployment error"));
    when(statusUpdateService.initializeWithCompletedSteps(
            any(Command.class), eq(CommandType.CREATE), anyList(), anyList()))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.provisioning(
            any(Command.class), eq(CommandType.CREATE), any(ProvisioningStep.class)))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.failed(
            any(Command.class),
            eq(CommandType.CREATE),
            eq(ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING),
            any(Throwable.class)))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.updateProvisioningStatus(any(), eq(RequestJobStatus.FAILED)))
        .thenReturn(CompletableFuture.completedFuture(null));

    createPipelineHandler.handle(command).toCompletableFuture().join();

    ArgumentCaptor<List<ProvisioningStep>> provisioningStepArgumentCaptor =
        ArgumentCaptor.forClass(List.class);
    verify(statusUpdateService)
        .initializeWithCompletedSteps(
            eq(mapToType(command, ConfigurationType.STREAMING_PIPELINE)),
            eq(CommandType.CREATE),
            provisioningStepCaptor.capture(),
            provisioningStepArgumentCaptor.capture());
    assertThat(provisioningStepCaptor.getValue())
        .hasSize(2)
        .isEqualTo(
            List.of(
                ProvisioningStep.OBJECT_REGISTRATION,
                ProvisioningStep.UPDATE_PIPELINES_MAPPING_LIST));
    assertThat(provisioningStepArgumentCaptor.getValue())
        .hasSize(1)
        .isEqualTo(List.of(ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
    var captor = ArgumentCaptor.forClass(Throwable.class);
    verify(flinkDeploymentService).createIfNotExists("gdc-flink-deployment.yaml", Map.of());
    verify(statusUpdateService)
        .failed(
            eq(mapToType(command, ConfigurationType.STREAMING_PIPELINE)),
            eq(CommandType.CREATE),
            eq(ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING),
            captor.capture());
    assertThat(captor.getValue()).hasMessageContaining("Deployment error");
  }
}
