package org.ude.deployment.flink;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.kubernetes.client.WithKubernetesTestServer;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.flink.dto.DeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.dto.GdcFlinkDeploymentConfig;
import org.ude.deployment.flink.dto.RetentionPeriod;
import org.ude.deployment.flink.service.FlinkDeploymentService;
import org.ude.deployment.util.DeploymentConfigUtils;
import org.ude.deployment.util.KubernetesUtils;

@QuarkusTest
@WithKubernetesTestServer
public class CreatePipelineHandlerGdcIT extends BaseHandlerIntegrationTest {
  @Inject FlinkDeploymentService flinkDeploymentService;

  @Inject KubernetesClient kubernetesClient;

  @Inject ObjectMapper objectMapper;

  @Inject FlinkEnvVariableConfig flinkEnvVariableConfig;

  private DeploymentConfig deploymentConfig;
  private String orderId;

  @BeforeAll
  void beforeAll() {
    KubernetesUtils.loadCustomResourceDefinition(this.kubernetesClient);
  }

  @BeforeEach
  void beforeEach() {
    this.orderId = UUID.randomUUID().toString();
    this.deploymentConfig = DeploymentConfigUtils.deploymentConfig(this.orderId);
  }

  @Test
  @SneakyThrows
  void createGdcJobWithoutSignalPackageFeatureDefined() {
    this.executeCommand((GdcFlinkDeploymentConfig) this.deploymentConfig.flinkDeploymentConfig());
    assertThat(this.containerEnvs())
        .doesNotContainKey("FLINK_TOPOLOGY_FEATURE_SIGNAL_PACKAGING_ENABLED");
  }

  @Test
  @SneakyThrows
  void createGdcJobWithSignalPackageFeatureDisabled() {
    final var flinkDeploymentConfig =
        ((GdcFlinkDeploymentConfig) this.deploymentConfig.flinkDeploymentConfig())
            .toBuilder()
                .featureSignalPackaging(
                    Optional.of(
                        GdcFlinkDeploymentConfig.TopologyFeatureSignalPackaging.builder()
                            .isEnabled(false)
                            .build()))
                .build();
    this.executeCommand(flinkDeploymentConfig);
    assertThat(this.containerEnvs().get("FLINK_TOPOLOGY_FEATURE_SIGNAL_PACKAGING_ENABLED"))
        .isEqualTo("false");
  }

  @Test
  @SneakyThrows
  void createGdcJobWithSignalPackageFeatureEnabled() {
    final var flinkDeploymentConfig =
        ((GdcFlinkDeploymentConfig) this.deploymentConfig.flinkDeploymentConfig())
            .toBuilder()
                .featureSignalPackaging(
                    Optional.of(
                        GdcFlinkDeploymentConfig.TopologyFeatureSignalPackaging.builder()
                            .isEnabled(true)
                            .build()))
                .build();

    this.executeCommand(flinkDeploymentConfig);

    assertThat(this.containerEnvs().get("FLINK_TOPOLOGY_FEATURE_SIGNAL_PACKAGING_ENABLED"))
        .isEqualTo("true");
  }

  @Test
  @SneakyThrows
  void createGdcJobWithoutColdStorageFeatureDefined() {
    this.executeCommand((GdcFlinkDeploymentConfig) this.deploymentConfig.flinkDeploymentConfig());
    assertThat(this.containerEnvs())
        .doesNotContainKey("FLINK_TOPOLOGY_FEATURE_SINK_COLD_STORAGE_ENABLED");
    assertThat(this.containerEnvs().get("FLINK_TOPOLOGY_FEATURE_SINK_APACHE_KAFKA_ENABLED"))
        .isEqualTo("true");
  }

  @Test
  @SneakyThrows
  void createGdcJobWithColdStorageFeatureEnabled() {
    final var featureSinkColdStorage =
        GdcFlinkDeploymentConfig.TopologyFeatureSinkColdStorage.builder()
            .aggregationTimeoutS((short) 6)
            .containerNameTpl(Optional.of("containerNameTpl"))
            .isEnabled(true)
            .maxBatchSize(7)
            .retentionPeriod(RetentionPeriod.ofDays(5))
            .build();
    final var flinkDeploymentConfig =
        ((GdcFlinkDeploymentConfig) this.deploymentConfig.flinkDeploymentConfig())
            .toBuilder().featureSinkColdStorage(Optional.of(featureSinkColdStorage)).build();

    this.executeCommand(flinkDeploymentConfig);

    assertThat(this.containerEnvs())
        .satisfies(
            mp -> {
              assertThat(mp.get("FLINK_TOPOLOGY_FEATURE_SINK_APACHE_KAFKA_ENABLED"))
                  .isEqualTo("false");
              assertThat(mp.get("FLINK_TOPOLOGY_FEATURE_SINK_COLD_STORAGE_AGGREGATION_TIMEOUT"))
                  .isEqualTo(String.valueOf(featureSinkColdStorage.aggregationTimeoutS()));
              assertThat(mp.get("FLINK_TOPOLOGY_FEATURE_SINK_COLD_STORAGE_BASE_PATH"))
                  .isEqualTo("abfss://<EMAIL>");
              assertThat(mp.get("FLINK_TOPOLOGY_FEATURE_SINK_COLD_STORAGE_CONTAINER_NAME_TPL"))
                  .isEqualTo(featureSinkColdStorage.containerNameTpl().orElseThrow());
              assertThat(mp.get("FLINK_TOPOLOGY_FEATURE_SINK_COLD_STORAGE_ENABLED"))
                  .isEqualTo("true");
              assertThat(mp.get("FLINK_TOPOLOGY_FEATURE_SINK_COLD_STORAGE_MAX_BATCH_SIZE"))
                  .isEqualTo(String.valueOf(featureSinkColdStorage.maxBatchSize()));
              assertThat(mp.get("FLINK_TOPOLOGY_FEATURE_SINK_COLD_STORAGE_RETENTION_DAYS"))
                  .isEqualTo(String.valueOf(featureSinkColdStorage.retentionPeriod().toDays()));
            });
  }

  private Map<String, String> containerEnvs() {
    return this.flinkDeploymentService
        .findByOrderId(
            this.flinkEnvVariableConfig.namespace, DeploymentType.GDC_PIPELINE, this.orderId)
        .orElseThrow()
        .getSpec()
        .getPodTemplate()
        .getSpec()
        .getContainers()
        .getFirst()
        .getEnv()
        .stream()
        .collect(HashMap::new, (hm, env) -> hm.put(env.getName(), env.getValue()), HashMap::putAll);
  }

  @SneakyThrows
  private void executeCommand(final GdcFlinkDeploymentConfig flinkDeploymentConfig) {
    this.commandExecuteAndAwaitNStatuses(
            new Command(
                UUID.randomUUID().toString(),
                CommandType.CREATE,
                UUID.randomUUID(),
                ConfigurationType.FLINK_JOB_PIPELINE,
                new JsonObject(
                    this.objectMapper.writeValueAsString(
                        this.deploymentConfig.toBuilder()
                            .flinkDeploymentConfig(flinkDeploymentConfig)
                            .build()))),
            3)
        .get();
  }
}
