package org.ude.deployment.flink;

import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.noContent;
import static com.github.tomakehurst.wiremock.client.WireMock.okJson;
import static com.github.tomakehurst.wiremock.client.WireMock.patch;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.util.DeploymentConfigUtils.getDeploymentName;
import static org.ude.deployment.util.StatusUtils.PipelineUtils.createExpectedDpoStatusesForCompletedCreation;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesDoNotContainResults;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedError;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import io.fabric8.kubernetes.api.model.ServiceBuilder;
import io.fabric8.kubernetes.api.model.ServicePortBuilder;
import io.fabric8.kubernetes.api.model.ServiceSpecBuilder;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.kubernetes.client.WithKubernetesTestServer;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.common.wiremock.InjectWireMock;
import org.ude.deployment.common.wiremock.WiremockResource;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.service.FlinkDeploymentService;
import org.ude.deployment.util.DeploymentConfigUtils;
import org.ude.deployment.util.Fixture;
import org.ude.deployment.util.KubernetesUtils;

@QuarkusTest
@WithKubernetesTestServer
@QuarkusTestResource(WiremockResource.class)
class UpdatePipelineHandlerIT extends BaseHandlerIntegrationTest {
  @InjectWireMock WireMockServer wiremock;

  @Inject ObjectMapper objectMapper;

  @Inject KubernetesClient kubernetesClient;

  @Inject FlinkDeploymentService flinkDeploymentService;

  @Inject FlinkEnvVariableConfig flinkEnvVariableConfig;

  @BeforeAll
  void beforeAll() {
    KubernetesUtils.loadCustomResourceDefinition(this.kubernetesClient);
  }

  @Test
  @SneakyThrows
  void handleEmptyUpdate() {
    final var cmd =
        this.objectMapper.readValue(
            Fixture.asBytes("flink/pipeline/update/without-payload.json"), Command.class);

    kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(commandsTopic, cmd.commandId(), cmd))
        .awaitCompletion();

    kafkaCompanion
        .consume(Status.class)
        .withOffsetReset(OffsetResetStrategy.EARLIEST)
        .fromTopics(statusTopic)
        .awaitNoRecords(Duration.ofSeconds(3));
  }

  @Test
  @SneakyThrows
  void handleNonExistentDeployment() {
    final var uuid = UUID.randomUUID().toString();
    final var cmd =
        this.objectMapper.readValue(
            Fixture.asString(
                "flink/pipeline/update/valid-update-template.json",
                Map.of(
                    "dataOrderId",
                    uuid,
                    "pipelineId",
                    uuid,
                    "processingSchemaId",
                    uuid,
                    "storageSchemaId",
                    uuid)),
            Command.class);

    var consumerRecords = this.commandExecuteAndAwaitNStatuses(cmd, 2).get();
    assertThat(consumerRecords.getLast().value())
        .satisfies(
            status -> {
              assertThat(status.status()).isEqualTo(RequestJobStatus.FAILED);
              var dataPlatformObjectStatuses =
                  extractDataPlatformObjectStatuses(status.properties());
              assertDpoStatusesReportedError(
                  dataPlatformObjectStatuses,
                  "Data platform object provisioning failed.\n Unable to find the rest service");
              assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);
              var disabledStatus = ConfigurationStatus.DISABLED;
              assertDpoAndConfigStatusesMatchExpected(
                  consumerRecords,
                  createExpectedDpoStatusesFor(disabledStatus),
                  createExpectedConfigurationStatusesFor(disabledStatus));
            });
  }

  @Test
  @SneakyThrows
  void handleUpdate() {
    final var pipelineId = UUID.randomUUID().toString();
    final var dataOrderId = UUID.randomUUID().toString();
    final var processingSchemaId = UUID.randomUUID().toString();
    final var storageSchemaId = UUID.randomUUID().toString();
    final var deploymentConfig =
        DeploymentConfigUtils.deploymentConfig(
            dataOrderId, Map.of("namespace", this.flinkEnvVariableConfig.namespace));
    final var namespace = deploymentConfig.get("NAMESPACE");

    final var cmd =
        this.objectMapper.readValue(
            Fixture.asString(
                "flink/pipeline/update/valid-update-template.json",
                Map.of(
                    "dataOrderId",
                    dataOrderId,
                    "pipelineId",
                    pipelineId,
                    "processingSchemaId",
                    processingSchemaId,
                    "storageSchemaId",
                    storageSchemaId)),
            Command.class);

    final var deploymentFirst =
        this.flinkDeploymentService
            .createIfNotExists("gdc-flink-deployment.yaml", deploymentConfig)
            .orElseThrow();

    this.stubRestApi(dataOrderId, namespace);

    final var consumerRecords = this.commandExecuteAndAwaitNStatuses(cmd, 4).get();
    final var finalStatus = consumerRecords.getLast().value();
    assertThat(finalStatus)
        .satisfies(status -> assertThat(status.status()).isEqualTo(RequestJobStatus.COMPLETED));
    var dataPlatformObjectStatuses = extractDataPlatformObjectStatuses(finalStatus.properties());
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);
    var activeStatus = ConfigurationStatus.ACTIVE;
    assertDpoAndConfigStatusesMatchExpected(
        consumerRecords,
        createExpectedDpoStatusesFor(activeStatus),
        createExpectedConfigurationStatusesFor(activeStatus));

    final var deploymentSecond =
        this.flinkDeploymentService
            .findByOrderId(namespace, DeploymentType.GDC_PIPELINE, dataOrderId)
            .orElseThrow();

    assertThat(Integer.parseInt(deploymentSecond.getMetadata().getResourceVersion()))
        .isGreaterThan(Integer.parseInt(deploymentFirst.getMetadata().getResourceVersion()));
  }

  private void stubRestApi(final String dataOrderId, final String namespace) {
    this.wiremock.stubFor(
        get(urlPathEqualTo("/jobs"))
            .willReturn(okJson(Fixture.asString("flink/rest-api/jobs-template.json"))));

    this.wiremock.stubFor(
        patch(urlPathEqualTo("/jobs/634022eed1842d0b33c6896463fb5e62")).willReturn(noContent()));

    this.wiremock.stubFor(
        get(urlPathEqualTo("/jobmanager/config"))
            .willReturn(
                okJson(
                    Fixture.asString(
                        "flink/rest-api/jobmanager-config-template.json",
                        Map.of("dataOrderId", dataOrderId)))));

    this.kubernetesClient
        .resource(
            new ServiceBuilder()
                .withNewMetadata()
                .withName(
                    "%s-rest"
                        .formatted(getDeploymentName(DeploymentType.GDC_PIPELINE, dataOrderId)))
                .withNamespace(namespace)
                .and()
                .withSpec(
                    new ServiceSpecBuilder()
                        .withPorts(new ServicePortBuilder().withPort(this.wiremock.port()).build())
                        .withClusterIP(this.wiremock.getOptions().bindAddress())
                        .build())
                .build())
        .create();
  }

  private static @NotNull List<List<DataPlatformObjectStatus>> createExpectedDpoStatusesFor(
      ConfigurationStatus configurationStatus) {
    var deletingStatus =
        List.of(
            List.of(
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING,
                    DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                    ProvisioningStatus.DELETING)));
    var dpoStatuses = new ArrayList<>(deletingStatus);
    if (configurationStatus.equals(ConfigurationStatus.ACTIVE)) {
      dpoStatuses.addAll(createExpectedDpoStatusesForCompletedCreation());
    } else {
      dpoStatuses.add(
          List.of(
              DataPlatformObjectStatus.of(
                  DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING,
                  DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                  ProvisioningStatus.FAILED)));
    }
    return Collections.unmodifiableList(dpoStatuses);
  }

  private static @NotNull List<ConfigurationStatus> createExpectedConfigurationStatusesFor(
      ConfigurationStatus configurationStatus) {
    return configurationStatus.equals(ConfigurationStatus.ACTIVE)
        ? List.of(
            ConfigurationStatus.IN_PROVISIONING,
            ConfigurationStatus.IN_PROVISIONING,
            ConfigurationStatus.IN_PROVISIONING,
            ConfigurationStatus.ACTIVE)
        : List.of(ConfigurationStatus.IN_PROVISIONING, ConfigurationStatus.DISABLED);
  }
}
