package org.ude.deployment.flink;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.ude.deployment.dataorder.DataOrderUtils.createStubDataOrder;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.fabric8.kubernetes.client.KubernetesClientException;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.CommandProducer;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.dataorder.dto.ModifyDataOrderCommand;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.service.FlinkDeploymentService;
import org.ude.deployment.flink.service.FlinkRestApiService;
import org.ude.deployment.flink.service.dto.spec.FlinkJobList;
import org.ude.deployment.streamingpipeline.dto.DeleteStreamingPipelineProperties;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineCategory;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineRequest;

@QuarkusTest
class DeletePipelineHandlerTest {

  @Inject DeletePipelineHandler deletePipelineHandler;

  @Inject ObjectMapper objectMapper;

  FlinkDeploymentService flinkDeploymentService;

  FlinkRestApi flinkRestApi;

  @InjectMock StatusUpdateService statusUpdateService;

  @Inject FlinkEnvVariableConfig flinkEnvVariableConfig;

  @InjectMock CommandProducer commandProducer;

  @BeforeEach
  void setUp() {
    flinkDeploymentService = mock(FlinkDeploymentService.class);
    flinkRestApi = mock(FlinkRestApi.class);
    deletePipelineHandler =
        new DeletePipelineHandler(
            objectMapper,
            flinkDeploymentService,
            flinkRestApi,
            commandProducer,
            statusUpdateService,
            flinkEnvVariableConfig);
  }

  @Test
  @DisplayName("Given valid command, when handle, then deletes Flink deployment and updates status")
  void handle_validCommand_deletesFlinkDeploymentAndUpdatesStatus() throws Exception {
    // Arrange
    var dataOrderId = UUID.randomUUID();
    var deleteStreamingPipelineProperties =
        new DeleteStreamingPipelineProperties(
            new StreamingPipelineRequest(
                UUID.randomUUID(), StreamingPipelineCategory.CUSTOM, List.of(dataOrderId)),
            false,
            null);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            UUID.randomUUID(),
            ConfigurationType.FLINK_JOB_PIPELINE,
            JsonObject.mapFrom(deleteStreamingPipelineProperties));

    var flinkRestApiService = mock(FlinkRestApiService.class);
    when(flinkRestApiService.jobListAll()).thenReturn(new FlinkJobList(List.of()));
    when(flinkRestApi.service(
            eq(flinkEnvVariableConfig.namespace),
            any(DeploymentType.class),
            eq(dataOrderId.toString())))
        .thenReturn(Optional.of(flinkRestApiService));
    when(flinkDeploymentService.delete(anyString(), any(DeploymentType.class), anyString()))
        .thenReturn(List.of());
    when(statusUpdateService.deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING)))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.deleted(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    deletePipelineHandler.handle(command).toCompletableFuture().get();

    // Assert
    verify(flinkRestApiService).jobListAll();
    verify(flinkRestApiService, times(0)).jobCancelById(anyString());
    verify(flinkDeploymentService)
        .delete(
            eq(flinkEnvVariableConfig.namespace),
            eq(DeploymentType.GDC_PIPELINE),
            eq(dataOrderId.toString()));
    verify(statusUpdateService)
        .deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
    verify(statusUpdateService)
        .deleted(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
  }

  @Test
  @DisplayName(
      "Given command with deleteDataOrder set to true, when handle, then sends DataOrder deletion command after Flink deployment deletion")
  void handle_commandWithDeleteDataOrder_sendsDataOrderDeletionCommand() throws Exception {
    // Arrange
    var dataOrderId = UUID.randomUUID();
    var deleteStreamingPipelineProperties =
        new DeleteStreamingPipelineProperties(
            new StreamingPipelineRequest(
                UUID.randomUUID(), StreamingPipelineCategory.CUSTOM, List.of(dataOrderId)),
            true,
            new ModifyDataOrderCommand(
                createStubDataOrder("schemaId", 1, 1),
                null,
                new StreamingPipelineRequest(
                    UUID.randomUUID(), StreamingPipelineCategory.CUSTOM, List.of(dataOrderId))));

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            UUID.randomUUID(),
            ConfigurationType.FLINK_JOB_PIPELINE,
            JsonObject.mapFrom(deleteStreamingPipelineProperties));

    var flinkRestApiService = mock(FlinkRestApiService.class);
    when(flinkRestApiService.jobListAll()).thenReturn(new FlinkJobList(List.of()));
    when(flinkRestApi.service(
            eq(flinkEnvVariableConfig.namespace),
            any(DeploymentType.class),
            eq(dataOrderId.toString())))
        .thenReturn(Optional.of(flinkRestApiService));
    when(flinkDeploymentService.delete(anyString(), any(DeploymentType.class), anyString()))
        .thenReturn(List.of());
    when(statusUpdateService.deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING)))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.deleted(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING)))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(commandProducer.sendCommand(any())).thenReturn(CompletableFuture.completedFuture(null));

    // Act
    deletePipelineHandler.handle(command).toCompletableFuture().get();

    // Assert
    var argumentCaptor = ArgumentCaptor.forClass(Command.class);
    verify(commandProducer).sendCommand(argumentCaptor.capture());
    verify(statusUpdateService)
        .deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
    verify(statusUpdateService)
        .deleted(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
    var capturedCommand = argumentCaptor.getValue();
    assertThat(capturedCommand.commandType()).isEqualTo(CommandType.DELETE);
    assertThat(capturedCommand.type()).isEqualTo(ConfigurationType.DATA_ORDER);
    assertThat(capturedCommand.objectId()).isEqualTo(dataOrderId);
  }

  @Test
  @DisplayName(
      "Given no service for Flink job, when handle, then skips job cancellation and successfully deletes deployment")
  void handle_noFlinkService_skipsJobCancellation() throws Exception {
    // Arrange
    var dataOrderId = UUID.randomUUID();
    var deleteStreamingPipelineProperties =
        new DeleteStreamingPipelineProperties(
            new StreamingPipelineRequest(
                UUID.randomUUID(), StreamingPipelineCategory.CUSTOM, List.of(dataOrderId)),
            false,
            null);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            UUID.randomUUID(),
            ConfigurationType.FLINK_JOB_PIPELINE,
            JsonObject.mapFrom(deleteStreamingPipelineProperties));

    when(flinkRestApi.service(
            eq(flinkEnvVariableConfig.namespace),
            any(DeploymentType.class),
            eq(dataOrderId.toString())))
        .thenReturn(Optional.empty());
    when(flinkDeploymentService.delete(
            eq(flinkEnvVariableConfig.namespace),
            any(DeploymentType.class),
            eq(dataOrderId.toString())))
        .thenReturn(List.of());
    when(statusUpdateService.deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING)))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.deleted(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    deletePipelineHandler.handle(command).toCompletableFuture().get();

    // Assert
    verify(flinkRestApi)
        .service(
            eq(flinkEnvVariableConfig.namespace),
            eq(DeploymentType.GDC_PIPELINE),
            eq(dataOrderId.toString()));
    verify(flinkDeploymentService)
        .delete(
            eq(flinkEnvVariableConfig.namespace),
            eq(DeploymentType.GDC_PIPELINE),
            eq(dataOrderId.toString()));
    verify(statusUpdateService)
        .deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
    verify(statusUpdateService)
        .deleted(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
  }

  @Test
  @DisplayName(
      "Given no service for Flink job, when Flink deployment deletion fails, then updates status to failed")
  void handle_noFlinkService_failingDeploymentDeletionUpdatesStatusToFailed() throws Exception {
    // Arrange
    var dataOrderId = UUID.randomUUID();
    var deleteStreamingPipelineProperties =
        new DeleteStreamingPipelineProperties(
            new StreamingPipelineRequest(
                UUID.randomUUID(), StreamingPipelineCategory.CUSTOM, List.of(dataOrderId)),
            false,
            null);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            UUID.randomUUID(),
            ConfigurationType.FLINK_JOB_PIPELINE,
            JsonObject.mapFrom(deleteStreamingPipelineProperties));

    when(flinkRestApi.service(
            eq(flinkEnvVariableConfig.namespace),
            any(DeploymentType.class),
            eq(dataOrderId.toString())))
        .thenReturn(Optional.empty());
    when(flinkDeploymentService.delete(
            eq(flinkEnvVariableConfig.namespace),
            any(DeploymentType.class),
            eq(dataOrderId.toString())))
        .thenThrow(new RuntimeException("Flink deployment delete failed"));
    when(statusUpdateService.deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING)))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.failed(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING),
            any(Throwable.class)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    deletePipelineHandler.handle(command).toCompletableFuture().get();

    // Assert
    verify(statusUpdateService)
        .deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
    var argumentCaptor = ArgumentCaptor.forClass(Throwable.class);
    verify(statusUpdateService)
        .failed(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING),
            argumentCaptor.capture());
    assertThat(argumentCaptor.getValue().getMessage())
        .isEqualTo("java.lang.RuntimeException: Flink deployment delete failed");
  }

  @Test
  @DisplayName(
      "Given valid command, when Flink deployment delete fails, then update status to failed")
  void handle_flinkDeploymentDeleteFails_updatesStatusToFailed() throws Exception {
    // Arrange
    var dataOrderId = UUID.randomUUID();
    var deleteStreamingPipelineProperties =
        new DeleteStreamingPipelineProperties(
            new StreamingPipelineRequest(
                UUID.randomUUID(), StreamingPipelineCategory.CUSTOM, List.of(dataOrderId)),
            false,
            null);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            UUID.randomUUID(),
            ConfigurationType.FLINK_JOB_PIPELINE,
            JsonObject.mapFrom(deleteStreamingPipelineProperties));

    var flinkRestApiService = mock(FlinkRestApiService.class);
    when(flinkRestApiService.jobListAll()).thenReturn(new FlinkJobList(List.of()));
    when(flinkRestApi.service(
            eq(flinkEnvVariableConfig.namespace),
            any(DeploymentType.class),
            eq(dataOrderId.toString())))
        .thenReturn(Optional.of(flinkRestApiService));
    when(flinkDeploymentService.delete(anyString(), any(DeploymentType.class), anyString()))
        .thenThrow(new RuntimeException("Flink deployment delete failed"));
    when(statusUpdateService.deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING)))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.failed(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING),
            any(Throwable.class)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    deletePipelineHandler.handle(command).toCompletableFuture().get();

    // Assert
    verify(statusUpdateService)
        .deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
    var argumentCaptor = ArgumentCaptor.forClass(Throwable.class);
    verify(statusUpdateService)
        .failed(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING),
            argumentCaptor.capture());
    assertThat(argumentCaptor.getValue().getMessage())
        .isEqualTo("java.lang.RuntimeException: Flink deployment delete failed");
  }

  @Test
  @DisplayName(
      "Given command with KubernetesClientException, when handle, then updates status to failed")
  void handle_kubernetesClientException_updatesStatusToFailed() throws Exception {
    // Arrange
    var dataOrderId = UUID.randomUUID();
    var deleteStreamingPipelineProperties =
        new DeleteStreamingPipelineProperties(
            new StreamingPipelineRequest(
                UUID.randomUUID(), StreamingPipelineCategory.CUSTOM, List.of(dataOrderId)),
            false,
            null);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            UUID.randomUUID(),
            ConfigurationType.FLINK_JOB_PIPELINE,
            JsonObject.mapFrom(deleteStreamingPipelineProperties));

    var flinkRestApiService = mock(FlinkRestApiService.class);
    when(flinkRestApiService.jobListAll()).thenReturn(new FlinkJobList(List.of()));
    when(flinkRestApi.service(
            eq(flinkEnvVariableConfig.namespace),
            any(DeploymentType.class),
            eq(dataOrderId.toString())))
        .thenReturn(Optional.of(flinkRestApiService));
    when(flinkDeploymentService.delete(anyString(), any(DeploymentType.class), anyString()))
        .thenThrow(new KubernetesClientException("Kubernetes error"));
    when(statusUpdateService.deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING)))
        .thenReturn(CompletableFuture.completedFuture(null));
    when(statusUpdateService.failed(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING),
            any(Throwable.class)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    deletePipelineHandler.handle(command).toCompletableFuture().get();

    // Assert
    verify(statusUpdateService)
        .deleting(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING));
    var argumentCaptor = ArgumentCaptor.forClass(Throwable.class);
    verify(statusUpdateService)
        .failed(
            any(Command.class),
            eq(CommandType.DELETE),
            eq(DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING),
            argumentCaptor.capture());
    assertThat(argumentCaptor.getValue().getMessage())
        .isEqualTo("io.fabric8.kubernetes.client.KubernetesClientException: Kubernetes error");
  }
}
