package org.ude.deployment.flink;

import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.flink.dto.DeploymentType.GDC_PIPELINE;
import static org.ude.deployment.flink.dto.DeploymentType.PASSTHROUGH_PIPELINE;
import static org.ude.deployment.util.DeploymentConfigUtils.getDeploymentName;
import static org.ude.deployment.util.StatusUtils.PipelineUtils.createExpectedConfigurationStatuses;
import static org.ude.deployment.util.StatusUtils.PipelineUtils.createExpectedDpoStatusesForCompletedCreation;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesDoNotContainResults;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.fabric8.kubernetes.api.model.GenericKubernetesResource;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.dsl.base.CustomResourceDefinitionContext;
import io.quarkus.logging.Log;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.kubernetes.client.WithKubernetesTestServer;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.dto.GdcFlinkDeploymentConfig;
import org.ude.deployment.util.KafkaUtils;

@WithKubernetesTestServer
@QuarkusTest
class CreatePipelineHandlerIT extends BaseHandlerIntegrationTest
    implements KubernetesIntegrationTest {

  @Inject FlinkEnvVariableConfig flinkEnvVariableConfig;

  @Inject KubernetesClient client;

  @Inject ObjectMapper objectMapper;

  CustomResourceDefinitionContext flinkDeploymentContext;

  @BeforeAll
  public void beforeAll() {
    try {
      // Load the CRD YAML
      var crdYaml =
          new String(
              Files.readAllBytes(
                  Paths.get(
                      Objects.requireNonNull(
                              getClass()
                                  .getResource(
                                      "/kubernetes/flinkdeployments.flink.apache.org-v1.yml"))
                          .toURI())));

      // Convert the CRD YAML to InputStream
      InputStream crdInputStream =
          new ByteArrayInputStream(crdYaml.getBytes(StandardCharsets.UTF_8));

      // Apply the CRD to the cluster
      client.load(crdInputStream).create();

      // Define the CRD context for the custom resource
      flinkDeploymentContext =
          new CustomResourceDefinitionContext.Builder()
              .withGroup("flink.apache.org") // CRD group
              .withVersion("v1beta1") // CRD version
              .withScope("Namespaced") // CRD scope
              .withPlural("flinkdeployments") // CRD plural (e.g., flinkdeployments)
              .build();

      Log.info("CRD registered successfully.");
    } catch (Exception e) {
      throw new RuntimeException("Failed to register the CRD: " + e.getMessage());
    }
  }

  @Test
  @DisplayName(
      "Given valid configs, when a CREATE command is issued for flink gdc deployment, then the deployment is successfully created.")
  void gdcJobSuccessfullyCreated() {
    var dataOrderId = UUID.randomUUID().toString();
    // Arrange
    var deploymentConfig = getDeploymentConfig(GDC_PIPELINE, dataOrderId);
    Command command;
    try {
      command =
          new Command(
              UUID.randomUUID().toString(),
              CommandType.CREATE,
              UUID.randomUUID(),
              ConfigurationType.FLINK_JOB_PIPELINE,
              new JsonObject(objectMapper.writeValueAsString(deploymentConfig)));
      // Produce the command message
      kafkaCompanion
          .produce(Command.class)
          .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
          .awaitCompletion();
      // Act: read the status from the status topic
      var consumerRecords = KafkaUtils.getLastNStatusMessages(kafkaCompanion, statusTopic, 3);
      var consumerRecord = consumerRecords.getLast();

      // Assert: the pipeline deployment completed
      assertThat(consumerRecord).isNotNull();
      var expectedOrderOfDpoStatusTypes = createExpectedDpoStatusesForCompletedCreation();
      var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
      assertDpoAndConfigStatusesMatchExpected(
          consumerRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
      var dataPlatformObjectStatuses =
          extractDataPlatformObjectStatuses(consumerRecord.value().properties());
      assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
      assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);
      removeDpoAndConfigurationStatus(consumerRecord.value().properties());
      assertThat(consumerRecord.value())
          .isEqualTo(
              new Status(
                  command.commandId(),
                  command.commandType(),
                  RequestJobStatus.COMPLETED,
                  command.objectId(),
                  ConfigurationType.STREAMING_PIPELINE,
                  command.properties(),
                  null,
                  List.of()));
      if (deploymentConfig.flinkDeploymentConfig() instanceof GdcFlinkDeploymentConfig) {
        GenericKubernetesResource resource =
            client
                .genericKubernetesResources(flinkDeploymentContext)
                .inNamespace(flinkEnvVariableConfig.namespace)
                .withName(getDeploymentName(GDC_PIPELINE, deploymentConfig.dataOrderId()))
                .get();

        assertThat(resource).isNotNull();
        assertThat(resource.getMetadata().getName())
            .isEqualTo(getDeploymentName(GDC_PIPELINE, deploymentConfig.dataOrderId()));
        var jobType = resource.get("spec", "podTemplate", "metadata", "labels", "jobType");
        assertThat(jobType).isEqualTo("gdc-job");
      }

    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  @Test
  @DisplayName(
      "Given valid configs, when a CREATE command is issued for flink passthrough, then the deployment is successfully created.")
  void passthroughJobSuccessfullyCreated() {
    // Arrange
    var dataOrderId = UUID.randomUUID().toString();
    var deploymentConfig = getDeploymentConfig(PASSTHROUGH_PIPELINE, dataOrderId);
    Command command;
    try {
      command =
          new Command(
              UUID.randomUUID().toString(),
              CommandType.CREATE,
              UUID.randomUUID(),
              ConfigurationType.FLINK_JOB_PIPELINE,
              new JsonObject(objectMapper.writeValueAsString(deploymentConfig)));
      // Act: Produce the command message to trigger the handler
      kafkaCompanion
          .produce(Command.class)
          .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
          .awaitCompletion();
      // Act: read the status from the status topic
      var consumerRecords = KafkaUtils.getLastNStatusMessages(kafkaCompanion, statusTopic, 3);
      var consumerRecord = consumerRecords.getLast();
      assertThat(consumerRecord).isNotNull();
      // Assert: the pipeline deployment completed
      var expectedOrderOfDpoStatusTypes = createExpectedDpoStatusesForCompletedCreation();
      var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
      assertDpoAndConfigStatusesMatchExpected(
          consumerRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
      var dataPlatformObjectStatuses =
          extractDataPlatformObjectStatuses(consumerRecord.value().properties());
      assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
      assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);
      removeDpoAndConfigurationStatus(consumerRecord.value().properties());
      assertThat(consumerRecord.value())
          .isEqualTo(
              new Status(
                  command.commandId(),
                  command.commandType(),
                  RequestJobStatus.COMPLETED,
                  command.objectId(),
                  ConfigurationType.STREAMING_PIPELINE,
                  command.properties(),
                  null,
                  List.of()));
      if (deploymentConfig.flinkDeploymentConfig() instanceof GdcFlinkDeploymentConfig) {
        var deploymentName = getDeploymentName(deploymentConfig.deploymentType(), dataOrderId);
        GenericKubernetesResource resource =
            client
                .genericKubernetesResources(flinkDeploymentContext)
                .inNamespace(flinkEnvVariableConfig.namespace)
                .withName(deploymentName)
                .get();

        assertThat(resource).isNotNull();
        assertThat(resource.getMetadata().getName()).isEqualTo(deploymentName);
        var jobType = resource.get("spec", "podTemplate", "metadata", "labels", "jobType");
        assertThat(jobType).isEqualTo("passthrough-job");
      }

    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }
}
