package org.ude.deployment.flink;

import static org.assertj.core.api.Assertions.assertThat;

import com.github.jknack.handlebars.HandlebarsException;
import io.fabric8.kubernetes.api.model.HasMetadata;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.kubernetes.client.WithKubernetesTestServer;
import jakarta.inject.Inject;
import java.util.Map;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.service.FlinkDeploymentService;
import org.ude.deployment.util.DeploymentConfigUtils;
import org.ude.deployment.util.KubernetesUtils;

@QuarkusTest
@WithKubernetesTestServer
class FlinkDeploymentServiceTestIT extends BaseHandlerIntegrationTest {
  @Inject FlinkDeploymentService flinkDeploymentService;

  @Inject KubernetesClient kubernetesClient;

  private Map<String, String> deploymentConfig;

  @BeforeAll
  void beforeAll() {
    KubernetesUtils.loadCustomResourceDefinition(this.kubernetesClient);
  }

  @BeforeEach
  void beforeEach() {
    this.deploymentConfig = DeploymentConfigUtils.deploymentConfig(UUID.randomUUID().toString());
  }

  @Test
  void handleMissingParamsInTemplate() {
    Assertions.assertThatThrownBy(
            () ->
                this.flinkDeploymentService.createIfNotExists(
                    "gdc-flink-deployment.yaml", Map.of()))
        .isInstanceOf(HandlebarsException.class)
        .hasMessageContaining(
            "java.lang.IllegalArgumentException: No value found for 'FLINK_APPLICATION_NAME'");
  }

  @Test
  void createDeploymentIfDoesNotExist() {
    final var valMaybe =
        this.flinkDeploymentService.createIfNotExists(
            "gdc-flink-deployment.yaml", this.deploymentConfig);

    assertThat(valMaybe).containsInstanceOf(HasMetadata.class);
  }

  @Test
  void ignoreCreateDeploymentIfPresent() {
    this.flinkDeploymentService.createIfNotExists(
        "gdc-flink-deployment.yaml", this.deploymentConfig);

    final var valMaybe =
        this.flinkDeploymentService.createIfNotExists(
            "gdc-flink-deployment.yaml", this.deploymentConfig);

    assertThat(valMaybe).isEmpty();
  }

  @Test
  void deleteDeployment() {
    this.flinkDeploymentService.createIfNotExists(
        "gdc-flink-deployment.yaml", this.deploymentConfig);

    final var valMaybe =
        this.flinkDeploymentService.delete(
            this.deploymentConfig.get("NAMESPACE"),
            DeploymentType.GDC_PIPELINE,
            this.deploymentConfig.get("DATA_ORDER_ID"));

    assertThat(valMaybe)
        .satisfies(
            statusDetails -> {
              assertThat(statusDetails).hasSize(1);
              assertThat(statusDetails.getFirst().getName())
                  .isEqualTo(this.deploymentConfig.get("FLINK_APPLICATION_NAME"));
            });
  }
}
