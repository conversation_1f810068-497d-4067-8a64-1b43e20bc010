package org.ude.deployment.flink;

import static org.ude.deployment.flink.dto.DeploymentType.PASSTHROUGH_PIPELINE;
import static org.ude.deployment.util.DeploymentConfigUtils.getDeploymentName;

import java.util.Optional;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.flink.dto.BaseDeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.GdcFlinkDeploymentConfig;
import org.ude.deployment.flink.dto.PassthroughDeploymentConfig;
import org.ude.deployment.flink.dto.RetentionPeriod;

public interface KubernetesIntegrationTest {
  default @NotNull DeploymentConfig getDeploymentConfig(
      DeploymentType deploymentType, String dataOrderId) {
    BaseDeploymentConfig deploymentConfig =
        new GdcFlinkDeploymentConfig(
            getDeploymentName(deploymentType, dataOrderId),
            "gdcImageName",
            "busybox",
            "input-topic",
            "output-topic",
            "signals",
            "normalization_rule",
            "non-retryable-error-topic",
            "storage-schema-uuid-1",
            "enrichment-config",
            "fct-rules",
            Optional.empty(),
            Optional.empty());

    if (deploymentType.equals(PASSTHROUGH_PIPELINE)) {
      deploymentConfig =
          new PassthroughDeploymentConfig(
              getDeploymentName(deploymentType, dataOrderId),
              "passthroughImageName",
              "input-topic",
              "output-topic",
              "dlq-topic");
    }

    return new DeploymentConfig(
        deploymentType,
        dataOrderId,
        "streaming-pipeline",
        "my-project-id",
        RetentionPeriod.ofDays(1),
        deploymentConfig);
  }
}
