package org.ude.deployment.flink.service;

import static org.assertj.core.api.Assertions.assertThat;

import com.github.jknack.handlebars.HandlebarsException;
import io.fabric8.kubernetes.api.model.HasMetadata;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.kubernetes.client.WithKubernetesTestServer;
import jakarta.inject.Inject;
import java.util.Map;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.util.DeploymentConfigUtils;
import org.ude.deployment.util.KubernetesUtils;

@QuarkusTest
@WithKubernetesTestServer
class FlinkDeploymentServiceTestIT extends BaseHandlerIntegrationTest {
  @Inject FlinkDeploymentService flinkDeploymentService;

  @Inject KubernetesClient kubernetesClient;

  private Map<String, String> deploymentConfig;

  @BeforeAll
  void beforeAll() {
    KubernetesUtils.loadCustomResourceDefinition(this.kubernetesClient);
  }

  @BeforeEach
  void beforeEach() {
    this.deploymentConfig = DeploymentConfigUtils.deploymentConfig(UUID.randomUUID().toString());
  }

  @Test
  void handleMissingParamsInTemplate() {
    Assertions.assertThatThrownBy(
            () ->
                this.flinkDeploymentService.createIfNotExists(
                    "gdc-flink-deployment.yaml", Map.of()))
        .isInstanceOf(HandlebarsException.class)
        .hasMessageContaining(
            "java.lang.IllegalArgumentException: No value found for 'FLINK_APPLICATION_NAME'");
  }

  @Test
  void createDeploymentIfDoesNotExist() {
    final var valMaybe =
        this.flinkDeploymentService.createIfNotExists(
            "gdc-flink-deployment.yaml", this.deploymentConfig);

    assertThat(valMaybe).containsInstanceOf(HasMetadata.class);
  }

  @DisplayName(
      "Given a deployment template, when the template is rendered, then it contains the required UDE annotations.")
  @ParameterizedTest(name = "Template rendering for '{1}' of type '{0}'")
  @CsvSource({
    "gdc-flink-deployment.yaml, GDC_PIPELINE",
    "passthrough-flink-deployment.yaml, PASSTHROUGH_PIPELINE"
  })
  void addsRequiredUdeAnnotations(String templateName, DeploymentType deploymentType) {
    var dataOrderId = UUID.randomUUID().toString();
    var deploymentConfigs =
        DeploymentConfigUtils.deploymentConfig(deploymentType, dataOrderId, Map.of());

    var deploymentManifest = flinkDeploymentService.template(templateName, deploymentConfigs);

    assertThat(deploymentManifest)
        .contains("_ude.pipeline.dataOrderId: %s".formatted(deploymentConfigs.get("DATA_ORDER_ID")))
        .contains("_ude.pipeline.name: %s".formatted(deploymentConfigs.get("PIPELINE_NAME")))
        .contains("_ude.pipeline.projectId: %s".formatted(deploymentConfigs.get("PROJECT_ID")));
  }

  @Test
  void ignoreCreateDeploymentIfPresent() {
    this.flinkDeploymentService.createIfNotExists(
        "gdc-flink-deployment.yaml", this.deploymentConfig);

    final var valMaybe =
        this.flinkDeploymentService.createIfNotExists(
            "gdc-flink-deployment.yaml", this.deploymentConfig);

    assertThat(valMaybe).isEmpty();
  }

  @Test
  void deleteDeployment() {
    this.flinkDeploymentService.createIfNotExists(
        "gdc-flink-deployment.yaml", this.deploymentConfig);

    final var valMaybe =
        this.flinkDeploymentService.delete(
            this.deploymentConfig.get("NAMESPACE"),
            DeploymentType.GDC_PIPELINE,
            this.deploymentConfig.get("DATA_ORDER_ID"));

    assertThat(valMaybe)
        .satisfies(
            statusDetails -> {
              assertThat(statusDetails).hasSize(1);
              assertThat(statusDetails.getFirst().getName())
                  .isEqualTo(this.deploymentConfig.get("FLINK_APPLICATION_NAME"));
            });
  }
}
