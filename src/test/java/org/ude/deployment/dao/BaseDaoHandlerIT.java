package org.ude.deployment.dao;

import static com.github.tomakehurst.wiremock.client.WireMock.delete;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matchingJsonPath;
import static com.github.tomakehurst.wiremock.client.WireMock.okJson;
import static com.github.tomakehurst.wiremock.client.WireMock.patch;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityPool;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListResponse;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.wiremock.InjectWireMock;
import org.ude.deployment.common.wiremock.WiremockResource;
import org.ude.deployment.dao.dto.DataAccessObject;
import org.ude.deployment.util.KafkaUtils;

@QuarkusTest
@QuarkusTestResource(WiremockResource.class)
class BaseDaoHandlerIT extends BaseHandlerIntegrationTest {

  @InjectWireMock WireMockServer wiremock;

  @Inject ObjectMapper objectMapper;

  protected final UUID daoId = UUID.randomUUID();
  protected final UUID dataOrderManagerId = UUID.randomUUID();
  protected final UUID dataOrderReaderId = UUID.randomUUID();
  protected final UUID dataConsumerId = UUID.randomUUID();
  protected final UUID dataProducerId = UUID.randomUUID();
  protected Map<String, ConfluentIdentityPool> identityPoolsStubs;

  @BeforeAll
  public void init() {
    identityPoolsStubs =
        Map.of(
            "data-order-manager",
            new ConfluentIdentityPool(
                "pool-001",
                "claims.sub",
                "'%s' in claims.groups".formatted(dataOrderManagerId.toString()),
                "dao-%s-data-order-manager".formatted(daoId.toString()),
                "Identity pool for data-order-manager of DAO 'dao.name'"),
            "data-order-reader",
            new ConfluentIdentityPool(
                "pool-002",
                "claims.sub",
                "'%s' in claims.groups".formatted(dataOrderReaderId.toString()),
                "dao-%s-data-order-reader".formatted(daoId.toString()),
                "Identity pool for data-order-reader of DAO 'dao.name'"),
            "data-consumer",
            new ConfluentIdentityPool(
                "pool-003",
                "claims.sub",
                "'%s' in claims.groups".formatted(dataConsumerId.toString()),
                "dao-%s-data-consumer".formatted(daoId.toString()),
                "Identity pool for data-consumer of DAO 'dao.name'"),
            "data-producer",
            new ConfluentIdentityPool(
                "pool-004",
                "claims.sub",
                "'%s' in claims.groups".formatted(dataProducerId.toString()),
                "dao-%s-data-producer".formatted(daoId.toString()),
                "Identity pool for data-producer of DAO 'dao.name'"));

    initConfluentRegistryWiremock();
  }

  @AfterEach
  public void cleanup() {
    KafkaUtils.clearTopic(kafkaCompanion, statusTopic);
  }

  @NotNull protected DataAccessObject getDaoProperties(UUID daoId) {
    var dao =
        new DataAccessObject(
            daoId,
            "dao.name",
            "dao.description",
            dataOrderManagerId,
            dataOrderReaderId,
            dataConsumerId,
            dataProducerId,
            Set.of());

    return dao;
  }

  String stubIdentityProviderId = "idnt-prvdr";
  String stubIdentityProvider =
      """
    {
        "api_version": "iam/v2",
        "kind": "IdentityProvider",
        "id": "idnt-prvdr",
        "display_name": "Test Tenant",
        "description": "Test  Tenant",
        "identity_claim": "claims.sub",
        "state": "ENABLED",
        "issuer": "https://login.microsoftonline.com/c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c/v2.0",
        "jwks_uri": "https://login.microsoftonline.com/common/discovery/v2.0/keys",
        "keys": []
    }
""";

  String stubIdentityPoolCreated =
      """
    {
        "api_version": "iam/v2",
        "kind": "CertificateIdentityPool",
        "metadata": {
            "self": "https://confluent.cloud/api/iam/v2/identity-providers/idnt-prvdr/identity-pools/pool-3nPl",
            "resource_name": "crn://confluent.cloud/organization=9cc94473-3f84-461b-a66b-22badbf7799b/identity-provider=idnt-prvdr/identity-pool=pool-3nPl",
            "created_at": "2024-09-10T13:02:24.021981Z",
            "updated_at": "2024-12-13T08:18:27.002810Z"
        },
        "id": "pool-3nPl",
        "display_name": "dao-{daoId}-{role}",
        "description": "Identity pool for {role} of DAO '{daoName}'",
        "identity_claim": "claims.sub",
        "filter": "'{roleId}' in claims.groups",
        "principal": "pool-3nPl",
        "state": "ENABLED"
    }
""";

  String stubAclDelete =
      """
{
    "data": [
        {
            "kind": "KafkaAcl",
            "metadata": {
                "self": "https://pkc-vr3yjz.westeurope.azure.confluent.cloud/kafka/v3/clusters/clstr/acls"
            },
            "cluster_id": "clstr",
            "resource_type": "TOPIC",
            "resource_name": "output-topic",
            "pattern_type": "PREFIXED",
            "principal": "User:pool-002",
            "host": "*",
            "operation": "READ",
            "permission": "ALLOW"
        }
    ]
}

""";

  @SneakyThrows
  private void initConfluentRegistryWiremock() {
    wiremock.stubFor(
        get(urlPathTemplate("/iam/v2/identity-providers/{providerId}"))
            .atPriority(1)
            .withPathParam("providerId", WireMock.equalTo(stubIdentityProviderId))
            .willReturn(okJson(stubIdentityProvider)));

    var poolsResponse =
        new ConfluentListResponse<ConfluentIdentityPool>(
            "iam/v2",
            "IdentityPoolList",
            null,
            identityPoolsStubs.values().stream().limit(2).toList()); // 2 create, 2 update

    var poolsJson = objectMapper.writeValueAsString(poolsResponse);

    wiremock.stubFor(
        get(urlPathTemplate("/iam/v2/identity-providers/{providerId}/identity-pools"))
            .atPriority(1)
            .withPathParam("providerId", WireMock.equalTo(stubIdentityProviderId))
            .willReturn(okJson(poolsJson)));

    wiremock.stubFor(
        delete(urlPathMatching("/kafka/v3/clusters/clstr/acls.*"))
            .atPriority(1)
            .willReturn(okJson(stubAclDelete)));

    identityPoolsStubs.entrySet().stream()
        .forEach(
            entry -> {
              var role = entry.getKey();
              var pool = entry.getValue();
              String json;
              try {
                json = objectMapper.writeValueAsString(pool);
              } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
              }

              wiremock.stubFor(
                  post(urlPathTemplate("/iam/v2/identity-providers/{providerId}/identity-pools"))
                      .atPriority(1)
                      .withPathParam("providerId", WireMock.equalTo(stubIdentityProviderId))
                      .withRequestBody(
                          matchingJsonPath(
                              "$[?(@.display_name == \"dao-%s-%s\")]"
                                  .formatted(daoId.toString(), role)))
                      .willReturn(okJson(json)));

              wiremock.stubFor(
                  patch(
                          urlPathTemplate(
                              "/iam/v2/identity-providers/{providerId}/identity-pools/{id}"))
                      .atPriority(1)
                      .withPathParam("providerId", WireMock.equalTo(stubIdentityProviderId))
                      .withRequestBody(
                          matchingJsonPath(
                              "$[?(@.display_name == \"dao-%s-%s\")]"
                                  .formatted(daoId.toString(), role)))
                      .willReturn(okJson(json)));
              wiremock.stubFor(
                  delete(
                          urlPathTemplate(
                              "/iam/v2/identity-providers/{providerId}/identity-pools/{id}"))
                      .atPriority(1)
                      .withPathParam("providerId", WireMock.equalTo(stubIdentityProviderId))
                      .willReturn(okJson(json)));
            });
  }

  static @NotNull List<DataPlatformObjectStatus> createExpectedDpoStatusesFor(
      ProvisioningStatus provisioningStatus) {
    return List.of(
        DataPlatformObjectStatus.of(
            DataPlatformObjectStatus.ProvisioningStep.SECURITY_GROUP_PROVISIONING,
            DataPlatformObjectStatus.ProvisioningStepType.SECURITY_GROUP,
            provisioningStatus));
  }
}
