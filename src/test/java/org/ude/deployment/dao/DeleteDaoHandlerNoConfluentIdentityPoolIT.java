package org.ude.deployment.dao;

import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesDoNotContainResults;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import io.vertx.core.json.JsonObject;
import java.util.List;
import java.util.UUID;
import lombok.SneakyThrows;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.util.KafkaUtils;
import org.ude.deployment.util.TestProfileWithConfluentIdentityPoolDisabled;

@QuarkusTest
@TestProfile(TestProfileWithConfluentIdentityPoolDisabled.class)
public class DeleteDaoHandlerNoConfluentIdentityPoolIT extends BaseDaoHandlerIT {

  @Test
  @DisplayName(
      "Given a command for a DAO deletion and Confluent Identity Pools disabled, when it is consumed, then it is submitted for processing by another component.")
  @SneakyThrows
  void givenValidCommand_whenConsumed_thenSubmittedStatusReturned() {
    var dao = getDaoProperties(daoId);
    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            daoId,
            ConfigurationType.DATA_ACCESS_OBJECT,
            JsonObject.mapFrom(dao));

    kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
        .awaitCompletion();

    kafkaCompanion.topics().waitForTopic(statusTopic).await().indefinitely();
    var consumerRecord = KafkaUtils.getLastStatusMessage(kafkaCompanion, statusTopic);

    // Verify status update sent and has success
    assertThat(consumerRecord).isNotNull();
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);
    var expectedOrderOfDpoStatusTypes = createExpectedDpoStatusesForDeletedCommand();
    var orderOfConfigurationStatuses = createExpectedConfigurationStatusesForDeletedCommand();
    assertDpoAndConfigStatusesMatchExpected(
        List.of(consumerRecord), expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
    removeDpoAndConfigurationStatus(consumerRecord.value().properties());
    assertThat(consumerRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                RequestJobStatus.DELETED,
                command.objectId(),
                command.type(),
                command.properties(),
                null,
                List.of()));
  }

  private static List<List<DataPlatformObjectStatus>> createExpectedDpoStatusesForDeletedCommand() {
    return List.of(createExpectedDpoStatusesFor(ProvisioningStatus.DELETED));
  }

  private static List<ConfigurationStatus> createExpectedConfigurationStatusesForDeletedCommand() {
    return List.of(ConfigurationStatus.DELETED);
  }
}
