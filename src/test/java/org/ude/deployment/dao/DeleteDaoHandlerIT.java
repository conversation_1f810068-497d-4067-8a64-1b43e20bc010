package org.ude.deployment.dao;

import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.extractResultOfType;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import com.github.tomakehurst.wiremock.client.WireMock;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.SneakyThrows;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentBaseEntity;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityPool;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListResponse;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.util.KafkaUtils;

@QuarkusTest
class DeleteDaoHandlerIT extends BaseDaoHandlerIT {

  @Test
  @DisplayName("Given a command for a DAO deletion, when it is consumed, then it is processed.")
  @SneakyThrows
  void givenValidCommand_whenConsumed_thenSuccessStatusReturned() {
    var dao = getDaoProperties(daoId);
    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            daoId,
            ConfigurationType.DATA_ACCESS_OBJECT,
            JsonObject.mapFrom(dao));

    var poolsResponse =
        new ConfluentListResponse<ConfluentIdentityPool>(
            "iam/v2", "IdentityPoolList", null, identityPoolsStubs.values().stream().toList());

    var poolsJson = objectMapper.writeValueAsString(poolsResponse);

    wiremock.stubFor(
        WireMock.get(
                WireMock.urlPathTemplate("/iam/v2/identity-providers/{providerId}/identity-pools"))
            .atPriority(1)
            .withPathParam("providerId", WireMock.equalTo(stubIdentityProviderId))
            .willReturn(WireMock.okJson(poolsJson)));

    kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
        .awaitCompletion();

    kafkaCompanion.topics().waitForTopic(statusTopic).await().indefinitely();
    var consumerRecords = KafkaUtils.getLastNStatusMessages(kafkaCompanion, statusTopic, 2);
    var consumerRecord = consumerRecords.getLast();

    // validate that the identity pools are really deleted
    identityPoolsStubs
        .entrySet()
        .forEach(
            entry ->
                wiremock.verify(
                    WireMock.deleteRequestedFor(
                        WireMock.urlEqualTo(
                            "/iam/v2/identity-providers/%s/identity-pools/%s"
                                .formatted(stubIdentityProviderId, entry.getValue().getId())))));

    var pools =
        identityPoolsStubs.values().stream()
            .sorted(Comparator.comparing(ConfluentIdentityPool::getDisplayName))
            .toList();

    var expectedResult =
        JsonObject.mapFrom(
            Map.of(
                "removedPools",
                pools.stream().map(ConfluentBaseEntity::getDisplayName).sorted().toList(),
                "provider",
                objectMapper.readTree(stubIdentityProvider)));

    // Verify status update sent and has success
    assertThat(consumerRecord).isNotNull();
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    var actualResult =
        (Map<String, Object>) extractResultOfType(dataPlatformObjectStatuses, Map.class);
    assertThat(actualResult)
        .satisfies(
            res -> {
              assertThat((List<String>) res.get("removedPools"))
                  .isEqualTo(expectedResult.getJsonArray("removedPools").getList());
              assertThat(res.get("provider"))
                  .isEqualTo(expectedResult.getJsonObject("provider").mapTo(Map.class));
            });
    var expectedOrderOfDpoStatusTypes = createExpectedDpoStatusesForCompletedDeletion();
    var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
    assertDpoAndConfigStatusesMatchExpected(
        consumerRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
    removeDpoAndConfigurationStatus(consumerRecord.value().properties());
    assertThat(consumerRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                RequestJobStatus.DELETED,
                command.objectId(),
                command.type(),
                command.properties(),
                expectedResult,
                List.of()));
  }

  private static List<List<DataPlatformObjectStatus>>
      createExpectedDpoStatusesForCompletedDeletion() {
    return List.of(
        createExpectedDpoStatusesFor(ProvisioningStatus.DELETING),
        createExpectedDpoStatusesFor(ProvisioningStatus.DELETED));
  }

  private static List<ConfigurationStatus> createExpectedConfigurationStatuses() {
    return List.of(ConfigurationStatus.IN_PROVISIONING, ConfigurationStatus.DELETED);
  }
}
