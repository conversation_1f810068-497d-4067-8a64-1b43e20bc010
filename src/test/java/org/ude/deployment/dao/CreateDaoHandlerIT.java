package org.ude.deployment.dao;

import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.extractResultOfType;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.SneakyThrows;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityPool;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.util.KafkaUtils;

@QuarkusTest
class CreateDaoHandlerIT extends BaseDaoHandlerIT {

  @Test
  @DisplayName("Given a command for a DAO creation, when it is consumed, then it is routed.")
  @SneakyThrows
  void givenDaoCreateCommandWithCorrectProperties_whenConsumed_thenSuccessStatusReturned() {
    var dao = getDaoProperties(daoId);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            daoId,
            ConfigurationType.DATA_ACCESS_OBJECT,
            JsonObject.mapFrom(dao));

    kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
        .awaitCompletion();

    kafkaCompanion.topics().waitForTopic(statusTopic).await().indefinitely();
    var consumerRecords = KafkaUtils.getLastNStatusMessages(kafkaCompanion, statusTopic, 2);
    var consumerRecord = consumerRecords.getLast();
    var pools =
        identityPoolsStubs.values().stream()
            .sorted(Comparator.comparing(ConfluentIdentityPool::getDisplayName))
            .toList();
    var result = JsonObject.mapFrom(Map.of("pools", pools));

    // Verify status update sent and has success
    assertThat(consumerRecord).isNotNull();
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    var actualResult = extractResultOfType(dataPlatformObjectStatuses, Map.class);
    assertThat(actualResult.get("pools")).isEqualTo(result.getJsonArray("pools").getList());
    var expectedOrderOfDpoStatusTypes = createExpectedDpoStatusesForCompletedCreation();
    var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
    assertDpoAndConfigStatusesMatchExpected(
        consumerRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
    removeDpoAndConfigurationStatus(consumerRecord.value().properties());
    assertThat(consumerRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                RequestJobStatus.COMPLETED,
                command.objectId(),
                command.type(),
                command.properties(),
                result,
                List.of()));
  }

  private static List<List<DataPlatformObjectStatus>>
      createExpectedDpoStatusesForCompletedCreation() {
    return List.of(
        createExpectedDpoStatusesFor(ProvisioningStatus.PROVISIONING),
        createExpectedDpoStatusesFor(ProvisioningStatus.COMPLETED));
  }

  private static List<ConfigurationStatus> createExpectedConfigurationStatuses() {
    return List.of(ConfigurationStatus.IN_PROVISIONING, ConfigurationStatus.ACTIVE);
  }
}
