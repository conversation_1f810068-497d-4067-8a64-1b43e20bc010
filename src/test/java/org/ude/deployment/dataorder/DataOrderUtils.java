package org.ude.deployment.dataorder;

import java.util.ArrayList;
import org.ude.deployment.dataorder.dto.DataOrder;

public class DataOrderUtils {

  public static DataOrder createStubDataOrder(
      String schemaIdToUse, int partitionCount, int retentionPeriodInDays) {
    var schemaIds = new ArrayList<String>();
    if (schemaIdToUse != null) {
      schemaIds.add(schemaIdToUse);
    }
    var configuration = new DataOrder.DataOrderConfiguration(retentionPeriodInDays, partitionCount);
    return new DataOrder(
        "DataOrder.gsk.xyz",
        "Some Description",
        "15374073-41a9-48dc-916e-c32bf67d36eb",
        true,
        "15374073-41a9-48dc-916e-c32bf67d36eb",
        schemaIds,
        configuration,
        null,
        null);
  }
}
