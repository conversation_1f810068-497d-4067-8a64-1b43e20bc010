package org.ude.deployment.dataorder;

import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.extractResultOfType;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.tomakehurst.wiremock.client.WireMock;
import io.confluent.kafka.schemaregistry.client.rest.entities.requests.RegisterSchemaResponse;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListRequest;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclOperation;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclPatternType;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclResourceType;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentCreateAclRequest;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.dao.dto.DataAccessObject;
import org.ude.deployment.dataorder.dto.CreateDataOrderCommand;
import org.ude.deployment.dataorder.dto.CreateDataOrderCommandResult;

@QuarkusTest
public class CreateDataOrderHandlerBaseIT extends BaseDataOrderHandlerIT {
  public static final int SUCCESSFUL_CREATION_EXPECTED_NUMBER_OF_STATUSES = 3;

  @SneakyThrows
  void testDataOrderCreated(UUID dataOrderId, boolean useConfluentIDP) {
    var dataOrder = createStubDataOrder(schemaId, 2, 10);
    var dao =
        new DataAccessObject(
            daoId,
            "dao.name",
            "dao.description",
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            Set.of());

    var properties = new CreateDataOrderCommand(dataOrder, dao);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            dataOrderId,
            ConfigurationType.DATA_ORDER,
            JsonObject.mapFrom(properties));

    var consumerRecords =
        this.commandExecuteAndAwaitNStatuses(
                command, SUCCESSFUL_CREATION_EXPECTED_NUMBER_OF_STATUSES)
            .get();
    var consumerRecord = consumerRecords.getLast();

    assertThat(consumerRecord).isNotNull();

    var expectedOrderOfDpoStatusTypes = createExpectedDpoStatusesForCompletedCreation();
    var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
    assertDpoAndConfigStatusesMatchExpected(
        consumerRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);

    // Verify status update sent and has success
    var inputTopicName = kafkaConfig.getInputTopicName(dataOrderId.toString());
    var outputTopicName = kafkaConfig.getOutputTopicName(dataOrderId.toString());
    var topicNames =
        List.of(
            inputTopicName,
            outputTopicName,
            kafkaConfig.getNonRetryableDlqTopicName(dataOrderId.toString()));

    assertThat(consumerRecord.key()).isEqualTo(command.commandId());

    var status = consumerRecord.value();

    assertThat(status.commandId()).isEqualTo(command.commandId());
    assertThat(status.commandType()).isEqualTo(command.commandType());
    assertThat(status.status()).isEqualTo(RequestJobStatus.COMPLETED);
    assertThat(status.objectId()).isEqualTo(command.objectId());
    assertThat(status.type()).isEqualTo(command.type());
    var dataPlatformObjectStatuses = extractDataPlatformObjectStatuses(status.properties());
    removeDpoAndConfigurationStatus(status.properties());
    assertThat(status.properties()).isEqualTo(command.properties());
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);

    var result =
        extractResultOfType(dataPlatformObjectStatuses, CreateDataOrderCommandResult.class);
    if (useConfluentIDP) {
      validateAclsCreated(dataOrderId.toString(), topicNames, result);
    } else {
      validateNoAclsCreated();
    }
  }

  static List<List<DataPlatformObjectStatus>> createExpectedDpoStatusesForCompletedCreation() {
    return List.of(
        createExpectedDpoStatusesFor(ProvisioningStatus.NOT_STARTED),
        createExpectedDpoStatusesFor(ProvisioningStatus.PROVISIONING),
        createExpectedDpoStatusesFor(ProvisioningStatus.COMPLETED));
  }

  static @NotNull List<DataPlatformObjectStatus> createExpectedDpoStatusesFor(
      ProvisioningStatus provisioningStatus) {
    return List.of(
        DataPlatformObjectStatus.of(
            DataPlatformObjectStatus.ProvisioningStep.OBJECT_REGISTRATION,
            DataPlatformObjectStatus.ProvisioningStepType.POSTGRES_DB_OBJECT,
            ProvisioningStatus.COMPLETED),
        DataPlatformObjectStatus.of(
            DataPlatformObjectStatus.ProvisioningStep.KAFKA_TOPIC_PROVISIONING,
            DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
            provisioningStatus),
        DataPlatformObjectStatus.of(
            DataPlatformObjectStatus.ProvisioningStep.SECURITY_GROUP_PROVISIONING,
            DataPlatformObjectStatus.ProvisioningStepType.SECURITY_GROUP,
            provisioningStatus),
        DataPlatformObjectStatus.of(
            DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
            DataPlatformObjectStatus.ProvisioningStepType.SCHEMA_REGISTRY,
            provisioningStatus));
  }

  static List<ConfigurationStatus> createExpectedConfigurationStatuses() {
    return List.of(
        ConfigurationStatus.IN_PROVISIONING,
        ConfigurationStatus.IN_PROVISIONING,
        ConfigurationStatus.ACTIVE);
  }

  void validateNoAclsCreated() {
    wiremock.verify(
        0, WireMock.getRequestedFor(WireMock.urlEqualTo("/kafka/v3/clusters/clstr/acls:batch")));
  }

  void validateAclsCreated(
      String dataOrderId, List<String> topicNames, CreateDataOrderCommandResult result)
      throws IOException {

    var inputTopicName = kafkaConfig.getInputTopicName(dataOrderId);
    var outputTopicName = kafkaConfig.getOutputTopicName(dataOrderId);

    assertThat(result.topics()).isEqualTo(topicNames);
    assertThat(result.inputTopicSchema())
        .isEqualTo(new JsonObject(stubSchema).mapTo(RegisterSchemaResponse.class));
    assertThat(result.connectionInfo().getTopic()).isEqualTo(outputTopicName);
    assertThat(result.connectionInfo().getProperties().get("extension.identity_pool_id"))
        .isEqualTo("pool-cnsmr");
    assertThat(result.connectionInfo().getProperties().get("group.id"))
        .isEqualTo("public.<CONSUMER_GROUP_NAME>");

    final var acls =
        wiremock.findAll(
            WireMock.postRequestedFor(WireMock.urlEqualTo("/kafka/v3/clusters/clstr/acls:batch")));
    final var request =
        objectMapper.readValue(
            acls.getLast().getBody(),
            new TypeReference<ConfluentListRequest<ConfluentCreateAclRequest>>() {});

    var inputTopicAcls =
        request.getData().stream()
            .filter(acl -> acl.getResourceName().equals(inputTopicName))
            .toList();
    var outputTopicAcls =
        request.getData().stream()
            .filter(acl -> acl.getResourceName().equals(outputTopicName))
            .toList();

    assertThat(inputTopicAcls).hasSize(5);
    assertThat(outputTopicAcls).hasSize(5);

    final ConfluentCreateAclRequest inputTopicAcl =
        request.getData().stream()
            .filter(
                acl ->
                    acl.getResourceName().equals(inputTopicName)
                        && acl.getPrincipal().equals("User:pool-prdcr")
                        && acl.getResourceType().equals(ConfluentAclResourceType.TOPIC))
            .findFirst()
            .orElse(null);
    assertThat(inputTopicAcl).isNotNull();

    final ConfluentCreateAclRequest outputTopicAcl =
        request.getData().stream()
            .filter(
                acl ->
                    acl.getResourceName().equals(outputTopicName)
                        && acl.getPrincipal().equals("User:pool-cnsmr")
                        && acl.getResourceType().equals(ConfluentAclResourceType.TOPIC))
            .findFirst()
            .orElse(null);
    assertThat(outputTopicAcl).isNotNull();

    final ConfluentCreateAclRequest outputGroupAcl =
        request.getData().stream()
            .filter(
                acl ->
                    acl.getResourceName().equals("public.")
                        && acl.getPrincipal().equals("User:pool-cnsmr")
                        && acl.getPatternType().equals(ConfluentAclPatternType.PREFIXED)
                        && acl.getResourceType().equals(ConfluentAclResourceType.GROUP)
                        && acl.getOperation().equals(ConfluentAclOperation.READ))
            .findFirst()
            .orElse(null);
    assertThat(outputGroupAcl).isNotNull();

    assertThat(inputTopicAcl.getResourceName()).isEqualTo(inputTopicName);
    assertThat(inputTopicAcl.getPrincipal()).isEqualTo("User:pool-prdcr");
    assertThat(inputTopicAcl.getHost()).isEqualTo("*");
    assertThat(inputTopicAcl.getOperation()).isEqualTo(ConfluentAclOperation.WRITE);

    assertThat(outputTopicAcl.getResourceName()).isEqualTo(outputTopicName);
    assertThat(outputTopicAcl.getPrincipal()).isEqualTo("User:pool-cnsmr");
    assertThat(outputTopicAcl.getHost()).isEqualTo("*");
    assertThat(outputTopicAcl.getOperation()).isEqualTo(ConfluentAclOperation.READ);

    assertThat(outputGroupAcl.getResourceName()).isEqualTo("public.");
    assertThat(outputGroupAcl.getPrincipal()).isEqualTo("User:pool-cnsmr");
    assertThat(outputGroupAcl.getHost()).isEqualTo("*");
    assertThat(outputGroupAcl.getOperation()).isEqualTo(ConfluentAclOperation.READ);
  }
}
