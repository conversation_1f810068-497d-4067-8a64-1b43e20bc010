package org.ude.deployment.dataorder;

import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.ude.deployment.util.TestProfileWithConfluentIdentityPoolDisabled;

@QuarkusTest
@TestProfile(TestProfileWithConfluentIdentityPoolDisabled.class)
public class UpdateDataOrderHandlerNoConfluentIdentityPoolIT extends UpdateDataOrderHandlerBaseIT {

  @Test
  @DisplayName(
      value =
          "Given topics exists and Identity Pools are disabled, when valid update command is sent then provisioning status is completed and ACL updates are skipped")
  void
      givenTopicsExistsAndValidateConlfuentIDPsFalse_whenValidUpdateCommandIsSent_thenProvisioningStatusIsCompleted() {
    var dataOrderId = UUID.randomUUID();
    testUpdateDataOrder(dataOrderId, schemaId, 1, 2, 2, false, true);
  }

  @DisplayName(
      "Given a data order exists, when an update command omits metadataProperties, but includes additionalProperties, then the handler processes the command successfully.")
  @Test
  void
      givenDataOrderExists_whenValidUpdateCommandWithAdditionalPropertiesButNoMetadataProperties_thenProvisioningStatusIsCompleted() {
    var dataOrderId = UUID.randomUUID();
    int initialPartitionCount = 1;
    int newPartitionCount = 2;
    int retentionPeriodInDays = 2;
    boolean withAdditionalPropertiesNull = false;

    testUpdateDataOrder(
        dataOrderId,
        schemaId,
        initialPartitionCount,
        newPartitionCount,
        retentionPeriodInDays,
        false,
        withAdditionalPropertiesNull);
  }
}
