package org.ude.deployment.dataorder;

import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.common.infrastructure.kafka.TopicService.RETENTION_MS_KEY;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.extractResultOfType;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.tomakehurst.wiremock.client.WireMock;
import io.vertx.core.json.JsonObject;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.config.ConfigResource;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListRequest;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclOperation;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclPatternType;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclResourceType;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentCreateAclRequest;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateTopicsRequest;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.dao.dto.DataAccessObject;
import org.ude.deployment.dataorder.dto.CreateDataOrderCommand;
import org.ude.deployment.dataorder.dto.CreateDataOrderCommandResult;
import org.ude.deployment.dataorder.dto.ModifyDataOrderCommand;

public class UpdateDataOrderHandlerBaseIT extends BaseDataOrderHandlerIT {
  public static final int SUCCESSFUL_UPDATE_EXPECTED_NUMBER_OF_STATUSES = 5;

  @SneakyThrows
  void createDataOrder(final UUID dataOrderId) {
    final var properties =
        new CreateDataOrderCommand(
            createStubDataOrder(this.schemaId, 1, 1),
            new DataAccessObject(
                this.daoId,
                "dao.name1",
                "dao.description1",
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                Set.of()));
    this.commandExecuteAndAwaitStatus(
            new Command(
                UUID.randomUUID().toString(),
                CommandType.CREATE,
                dataOrderId,
                ConfigurationType.DATA_ORDER,
                JsonObject.mapFrom(properties)))
        .get();
  }

  @SneakyThrows
  void testUpdateDataOrder(
      UUID dataOrderId,
      String schemaId,
      int initialPartitionCount,
      int newPartitionCount,
      int retentionPeriodInDays,
      boolean useConfluentIdP,
      boolean withAdditionalPropertiesNull) {
    var request = new CreateTopicsRequest(dataOrderId.toString(), initialPartitionCount, 1);
    var topics =
        createTopics(request).stream()
            .filter(topicName -> !topicName.contains("dlq")) // dlq is not updated
            .toList();
    var inputTopicName = kafkaConfig.getInputTopicName(dataOrderId.toString());

    var dao =
        new DataAccessObject(
            daoId,
            "dao.name",
            "dao.description",
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            Set.of());

    var stubDataOrder =
        createStubDataOrderWithAdditionalProperties(
            schemaId,
            newPartitionCount,
            retentionPeriodInDays,
            withAdditionalPropertiesNull ? null : new JsonObject().put("scope", "test"));
    var properties = new ModifyDataOrderCommand(stubDataOrder, dao, null);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.UPDATE,
            dataOrderId,
            ConfigurationType.DATA_ORDER,
            JsonObject.mapFrom(properties));

    int successfulUpdateExpectedNumberOfStatuses =
        useConfluentIdP
            ? SUCCESSFUL_UPDATE_EXPECTED_NUMBER_OF_STATUSES
            : SUCCESSFUL_UPDATE_EXPECTED_NUMBER_OF_STATUSES - 2;
    var consumerRecords =
        this.commandExecuteAndAwaitNStatuses(command, successfulUpdateExpectedNumberOfStatuses)
            .get();
    var consumerRecord = consumerRecords.getLast();

    assertThat(consumerRecord).isNotNull();
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());

    var status = consumerRecord.value();

    assertThat(status.commandId()).isEqualTo(command.commandId());
    assertThat(status.commandType()).isEqualTo(command.commandType());
    assertThat(status.status()).isEqualTo(RequestJobStatus.COMPLETED);
    assertThat(status.objectId()).isEqualTo(command.objectId());
    assertThat(status.type()).isEqualTo(command.type());
    var dataPlatformObjectStatus = extractDataPlatformObjectStatuses(status.properties());
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatus);
    var propertiesWithoutDpoStatuses = status.properties().copy();
    removeDpoAndConfigurationStatus(propertiesWithoutDpoStatuses);
    assertThat(propertiesWithoutDpoStatuses).isEqualTo(command.properties());

    var result = extractResultOfType(dataPlatformObjectStatus, CreateDataOrderCommandResult.class);
    assertThat(result.topics()).isEqualTo(topics);

    var orderOfStatusTypes = createExpectedDpoStatusesForCompleteUpdate(useConfluentIdP);
    var orderOfConfigurationStatuses = createExpectedConfigurationStatuses(useConfluentIdP);
    var updateDataOrderStatuses =
        consumerRecords.subList(
            consumerRecords.size() - successfulUpdateExpectedNumberOfStatuses,
            consumerRecords.size());
    assertDpoAndConfigStatusesMatchExpected(
        updateDataOrderStatuses, orderOfStatusTypes, orderOfConfigurationStatuses);
    assertAppropriateStatusRecordReturnedFor(CommandType.UPDATE, consumerRecord, command, topics);

    // verify that topics have the updated partition count
    var admin = kafkaAdminClientProvider.getAdmin();
    admin
        .describeTopics(Collections.singletonList(inputTopicName))
        .allTopicNames()
        .toCompletionStage()
        .toCompletableFuture()
        .join()
        .forEach(
            (name, topicDescription) ->
                assertThat(topicDescription.partitions().size()).isEqualTo(newPartitionCount));

    var topicResource = new ConfigResource(ConfigResource.Type.TOPIC, inputTopicName);
    admin
        .describeConfigs(Collections.singletonList(topicResource))
        .all()
        .toCompletionStage()
        .toCompletableFuture()
        .join()
        .forEach(
            (configResource, config) -> {
              assertThat(configResource.name()).isEqualTo(inputTopicName);
              assertThat(config.get(RETENTION_MS_KEY).value())
                  .isEqualTo(
                      Long.toString(topicService.retentionInDaysToMs(retentionPeriodInDays)));
            });

    if (useConfluentIdP) {
      verifyIdentityPoolAndAcl(dataOrderId, result);
    } else {
      verifyAclNotCreated();
    }
  }

  static @NotNull List<List<DataPlatformObjectStatus>> createExpectedDpoStatusesForCompleteUpdate(
      boolean useConfluentIdP) {
    return createExpectedDpoStatusesForCompleteUpdate(useConfluentIdP, false);
  }

  static @NotNull List<List<DataPlatformObjectStatus>>
      createExpectedDpoStatusesForFailedStatusConfigUpdate() {
    return createExpectedDpoStatusesForCompleteUpdate(true, true);
  }

  private static @NotNull List<List<DataPlatformObjectStatus>>
      createExpectedDpoStatusesForCompleteUpdate(
          boolean useConfluentIdP, boolean withCommandDeserializationError) {
    var confluentAgnosticStatuses =
        List.of(
            List.of(
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.CONFIGURATION_OBJECT_UPDATE,
                    DataPlatformObjectStatus.ProvisioningStepType.POSTGRES_DB_OBJECT,
                    withCommandDeserializationError
                        ? ProvisioningStatus.FAILED
                        : ProvisioningStatus.COMPLETED),
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.KAFKA_TOPIC_CONFIGURATION_UPDATE,
                    DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                    withCommandDeserializationError
                        ? ProvisioningStatus.FAILED
                        : ProvisioningStatus.NOT_STARTED)),
            List.of(
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.KAFKA_TOPIC_CONFIGURATION_UPDATE,
                    DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                    ProvisioningStatus.PROVISIONING)),
            List.of(
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.KAFKA_TOPIC_CONFIGURATION_UPDATE,
                    DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                    ProvisioningStatus.COMPLETED)));

    if (!useConfluentIdP) {
      return confluentAgnosticStatuses;
    }

    var dpoStatuses = new ArrayList<>(confluentAgnosticStatuses);
    var confluentSpecificStatuses =
        List.of(
            List.of(
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.SECURITY_GROUP_PROVISIONING,
                    DataPlatformObjectStatus.ProvisioningStepType.SECURITY_GROUP,
                    ProvisioningStatus.PROVISIONING)),
            List.of(
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.SECURITY_GROUP_PROVISIONING,
                    DataPlatformObjectStatus.ProvisioningStepType.SECURITY_GROUP,
                    ProvisioningStatus.COMPLETED)));
    dpoStatuses.addAll(2, confluentSpecificStatuses);
    return dpoStatuses;
  }

  static @NotNull List<ConfigurationStatus> createExpectedConfigurationStatuses(
      boolean useConfluentIdP) {
    return useConfluentIdP
        ? List.of(
            ConfigurationStatus.IN_PROVISIONING,
            ConfigurationStatus.IN_PROVISIONING,
            ConfigurationStatus.IN_PROVISIONING,
            ConfigurationStatus.IN_PROVISIONING,
            ConfigurationStatus.ACTIVE)
        : List.of(
            ConfigurationStatus.IN_PROVISIONING,
            ConfigurationStatus.IN_PROVISIONING,
            ConfigurationStatus.ACTIVE);
  }

  void verifyAclNotCreated() {
    wiremock.verify(
        0, WireMock.getRequestedFor(WireMock.urlEqualTo("/kafka/v3/clusters/clstr/acls:batch")));
  }

  void verifyIdentityPoolAndAcl(UUID dataOrderId, CreateDataOrderCommandResult result)
      throws IOException {

    var inputTopicName = kafkaConfig.getInputTopicName(dataOrderId.toString());
    var outputTopicName = kafkaConfig.getOutputTopicName(dataOrderId.toString());

    // Verify identity pools

    assertConnectionInfoIsSet(dataOrderId.toString(), result);
    // Verify ACLs are created
    final var acls =
        wiremock.findAll(
            WireMock.postRequestedFor(WireMock.urlEqualTo("/kafka/v3/clusters/clstr/acls:batch")));
    final var aclRequest =
        objectMapper.readValue(
            acls.getLast().getBody(),
            new TypeReference<ConfluentListRequest<ConfluentCreateAclRequest>>() {});

    final ConfluentCreateAclRequest inputTopicAcl =
        aclRequest.getData().stream()
            .filter(acl -> acl.getResourceName().equals(inputTopicName))
            .findFirst()
            .orElse(null);
    assertThat(inputTopicAcl).isNotNull();

    final ConfluentCreateAclRequest outputTopicAcl =
        aclRequest.getData().stream()
            .filter(acl -> acl.getResourceName().equals(outputTopicName))
            .findFirst()
            .orElse(null);
    assertThat(outputTopicAcl).isNotNull();

    assertThat(inputTopicAcl.getResourceName()).isEqualTo(inputTopicName);
    assertThat(inputTopicAcl.getResourceType()).isEqualTo(ConfluentAclResourceType.TOPIC);
    assertThat(inputTopicAcl.getPatternType()).isEqualTo(ConfluentAclPatternType.LITERAL);
    assertThat(inputTopicAcl.getPrincipal()).isEqualTo("User:pool-prdcr");
    assertThat(inputTopicAcl.getHost()).isEqualTo("*");
    assertThat(inputTopicAcl.getOperation()).isEqualTo(ConfluentAclOperation.WRITE);

    assertThat(outputTopicAcl.getResourceName()).isEqualTo(outputTopicName);
    assertThat(outputTopicAcl.getResourceType()).isEqualTo(ConfluentAclResourceType.TOPIC);
    assertThat(outputTopicAcl.getPatternType()).isEqualTo(ConfluentAclPatternType.LITERAL);
    assertThat(outputTopicAcl.getPrincipal()).isEqualTo("User:pool-cnsmr");
    assertThat(outputTopicAcl.getHost()).isEqualTo("*");
    assertThat(outputTopicAcl.getOperation()).isEqualTo(ConfluentAclOperation.READ);
  }

  List<String> createTopics(CreateTopicsRequest request) {
    return this.topicService.processDataOrderTopicsCreation(request).await().indefinitely();
  }

  public void assertAppropriateStatusRecordReturnedFor(
      CommandType commandType,
      ConsumerRecord<String, Status> consumerRecord,
      Command command,
      List<@NotNull String> topics) {
    var status = consumerRecord.value();
    var dataPlatformObjectStatuses = extractDataPlatformObjectStatuses(status.properties());
    removeDpoAndConfigurationStatus(status.properties());

    assertThat(status.commandId()).isEqualTo(command.commandId());
    assertThat(status.status()).isEqualTo(RequestJobStatus.COMPLETED);
    assertThat(status.objectId()).isEqualTo(command.objectId());
    assertThat(status.commandType()).isEqualTo(command.commandType());
    assertThat(status.type()).isEqualTo(command.type());
    assertThat(status.properties()).isEqualTo(command.properties());
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);

    var result =
        extractResultOfType(dataPlatformObjectStatuses, CreateDataOrderCommandResult.class);
    assertThat(result.topics()).isEqualTo(topics);
    if (commandType.equals(CommandType.CREATE)) {
      assertThat(result.inputTopicSchema()).isNotNull();
    }
  }

  public void assertConnectionInfoIsSet(String dataOrderId, CreateDataOrderCommandResult result) {
    assertThat(result.connectionInfo()).isNotNull();
    assertThat(result.connectionInfo().getTopic())
        .isEqualTo(kafkaConfig.getOutputTopicName(dataOrderId));
    assertThat(result.connectionInfo().getProperties().get("extension.identity_pool_id"))
        .isEqualTo("pool-cnsmr");
  }
}
