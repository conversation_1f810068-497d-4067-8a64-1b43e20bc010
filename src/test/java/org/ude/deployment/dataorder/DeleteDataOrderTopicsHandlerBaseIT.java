package org.ude.deployment.dataorder;

import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.extractResultOfType;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import com.github.tomakehurst.wiremock.client.WireMock;
import io.vertx.core.json.JsonObject;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateTopicsRequest;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.dataorder.dto.DeleteDataOrderTopicsRequest;
import org.ude.deployment.dataorder.dto.DeleteTopicCommandResult;

public class DeleteDataOrderTopicsHandlerBaseIT extends BaseDataOrderHandlerIT {

  void testDataOrderTopicsDeletion(UUID dataOrderId, boolean useConfluentIDP)
      throws ExecutionException, InterruptedException {
    int partitionCount = 1;
    var createTopicRequest = new CreateTopicsRequest(dataOrderId.toString(), partitionCount, 1);
    var topics = createTopic(createTopicRequest).stream().sorted().collect(Collectors.toList());

    var commandResult = new DeleteTopicCommandResult(topics);
    var properties = new DeleteDataOrderTopicsRequest(dataOrderId.toString());

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            dataOrderId,
            ConfigurationType.DATA_ORDER_TOPIC,
            JsonObject.mapFrom(properties));
    var consumerRecord = this.commandExecuteAndAwaitStatus(command).get();
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    var actualResult =
        extractResultOfType(dataPlatformObjectStatuses, DeleteTopicCommandResult.class);
    assertThat(actualResult).isEqualTo(commandResult);
    removeDpoAndConfigurationStatus(consumerRecord.value().properties());

    assertThat(consumerRecord.key()).isEqualTo(command.commandId());
    assertThat(consumerRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                RequestJobStatus.DELETED,
                command.objectId(),
                ConfigurationType.DATA_ORDER,
                command.properties(),
                JsonObject.mapFrom(commandResult),
                List.of()));

    // confirm that topic is deleted
    var admin = kafkaAdminClientProvider.getAdmin();
    var existingTopics = admin.listTopics().names().get();
    assertThat(existingTopics).doesNotContainAnyElementsOf(topics);

    var endpoint =
        "/kafka/v3/clusters/clstr/acls?resource_name=public.ude-str.%s.output.json&resource_type=TOPIC&pattern_type=LITERAL&operation=ANY&permission=ANY"
            .formatted(dataOrderId);
    if (useConfluentIDP) {
      wiremock.verify(WireMock.deleteRequestedFor(WireMock.urlEqualTo(endpoint)));
    } else {
      wiremock.verify(0, WireMock.deleteRequestedFor(WireMock.urlEqualTo(endpoint)));
    }

    wiremock.verify(
        WireMock.deleteRequestedFor(
            WireMock.urlEqualTo(
                "/subjects/%s?permanent=false"
                    .formatted(
                        kafkaConfig.getTopicSchemaSubjectName(
                            kafkaConfig.getInputTopicName(dataOrderId.toString()))))));

    wiremock.verify(
        WireMock.deleteRequestedFor(
            WireMock.urlEqualTo(
                "/subjects/%s?permanent=true"
                    .formatted(
                        kafkaConfig.getTopicSchemaSubjectName(
                            kafkaConfig.getInputTopicName(dataOrderId.toString()))))));
  }

  List<String> createTopic(CreateTopicsRequest request) {
    return this.topicService.processDataOrderTopicsCreation(request).await().indefinitely();
  }
}
