package org.ude.deployment.dataorder;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.ude.deployment.dataorder.CreateDataOrderHandlerBaseIT.SUCCESSFUL_CREATION_EXPECTED_NUMBER_OF_STATUSES;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesDoNotContainResults;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedError;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.extractResultOfType;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.InjectSpy;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.ude.deployment.azureblobstorage.handler.CreateAzureBlobStorageHandler;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.TopicService;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateTopicsRequest;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.dao.dto.DataAccessObject;
import org.ude.deployment.dataorder.dto.CreateDataOrderCommand;
import org.ude.deployment.dataorder.dto.CreateDataOrderCommandResult;
import org.ude.deployment.dataorder.dto.ModifyDataOrderCommand;
import org.ude.deployment.flink.UpdatePipelineHandler;
import org.ude.deployment.flink.dto.command.UpdatePipelineCommand;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineCategory;
import org.ude.deployment.util.Fixture;

@QuarkusTest
public class UpdateDataOrderHandlerIT extends UpdateDataOrderHandlerBaseIT {
  @ConfigProperty(name = "api.confluent.use-identity-pool")
  private boolean useIdentityPool;

  @Inject ObjectMapper objectMapper;
  @InjectMock UpdatePipelineHandler mockUpdatePipelineHandler;
  @InjectMock CreateAzureBlobStorageHandler mockCreateAzureBlobStorageHandler;
  @InjectSpy TopicService topicService;

  @ParameterizedTest(
      name = "Initial Partition Count: {0}, New Partition Count: {1}, Retention Days: {2}")
  @CsvSource({
    "1, 1, 1", "3, 3, 2", "3, 5, 2",
  })
  @SneakyThrows
  void givenTopicsExists_whenValidUpdateCommandIsSent_thenProvisioningStatusIsCompleted(
      int initialPartitionCount, int newPartitionCount, int retentionPeriodInDays) {
    var dataOrderId = UUID.randomUUID();
    boolean withAdditionalPropertiesNull = true;

    testUpdateDataOrder(
        dataOrderId,
        schemaId,
        initialPartitionCount,
        newPartitionCount,
        retentionPeriodInDays,
        true,
        withAdditionalPropertiesNull);
    verify(this.mockUpdatePipelineHandler, never()).handle(any());
    verify(topicService, never())
        .associateSchemaToInputTopic(eq(dataOrderId.toString()), eq(schemaId));
  }

  @DisplayName(
      "Given a data order exists, when an update command omits metadataProperties, but includes additionalProperties, then the handler processes the command successfully.")
  @Test
  void
      givenDataOrderExists_whenValidUpdateCommandWithAdditionalPropertiesButNoMetadataProperties_thenProvisioningStatusIsCompleted() {
    var dataOrderId = UUID.randomUUID();
    int initialPartitionCount = 1;
    int newPartitionCount = 2;
    int retentionPeriodInDays = 2;
    boolean withAdditionalPropertiesNull = false;

    testUpdateDataOrder(
        dataOrderId,
        schemaId,
        initialPartitionCount,
        newPartitionCount,
        retentionPeriodInDays,
        true,
        withAdditionalPropertiesNull);
    verify(topicService, never())
        .associateSchemaToInputTopic(eq(dataOrderId.toString()), eq(schemaId));
  }

  @SneakyThrows
  @DisplayName(
      "Given data order exists, when valid update command is sent with new DAO, then topic ACLs are replaced.")
  @Test
  void givenDataOrderExists_whenValidUpdateCommandIsSentWithNewDao_thenTopicsACLsAreReplaced() {
    var dataOrderId = UUID.randomUUID();

    var dao1 =
        new DataAccessObject(
            daoId,
            "dao.name1",
            "dao.description1",
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            Set.of());
    var dao2 =
        new DataAccessObject(
            daoId,
            "dao.name2",
            "dao.description2",
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            Set.of());

    var inputTopic = kafkaConfig.getInputTopicName(dataOrderId.toString());
    var outputTopic = kafkaConfig.getOutputTopicName(dataOrderId.toString());
    var topics =
        List.of(
            inputTopic,
            outputTopic,
            kafkaConfig.getNonRetryableDlqTopicName(dataOrderId.toString()));

    var properties = new CreateDataOrderCommand(createStubDataOrder(schemaId, 2, 1), dao1);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            dataOrderId,
            ConfigurationType.DATA_ORDER,
            JsonObject.mapFrom(properties));

    when(this.mockCreateAzureBlobStorageHandler.handle(any()))
        .thenReturn(CompletableFuture.completedStage(null));

    var consumerRecords =
        commandExecuteAndAwaitNStatuses(command, SUCCESSFUL_CREATION_EXPECTED_NUMBER_OF_STATUSES)
            .get();
    var consumerRecord = consumerRecords.getLast();

    assertThat(consumerRecord).isNotNull();
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());

    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    assertAppropriateStatusRecordReturnedFor(CommandType.CREATE, consumerRecord, command, topics);
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    var dataOrderCreationResult =
        extractResultOfType(dataPlatformObjectStatuses, CreateDataOrderCommandResult.class);
    assertConnectionInfoIsSet(dataOrderId.toString(), dataOrderCreationResult);
    // confirm that ACL are created
    wiremock.verify(
        WireMock.postRequestedFor(WireMock.urlEqualTo("/kafka/v3/clusters/clstr/acls:batch")));

    // update command with new dao
    var updateProperties =
        new ModifyDataOrderCommand(createStubDataOrder(schemaId, 2, 2), dao2, null);

    command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.UPDATE,
            dataOrderId,
            ConfigurationType.DATA_ORDER,
            JsonObject.mapFrom(updateProperties));

    var expectedNumberOfStatusRecords =
        SUCCESSFUL_CREATION_EXPECTED_NUMBER_OF_STATUSES
            + SUCCESSFUL_UPDATE_EXPECTED_NUMBER_OF_STATUSES;
    consumerRecords = commandExecuteAndAwaitNStatuses(command, expectedNumberOfStatusRecords).get();
    consumerRecord = consumerRecords.getLast();

    assertThat(consumerRecord).isNotNull();
    var orderOfStatusTypes = createExpectedDpoStatusesForCompleteUpdate(useIdentityPool);
    var orderOfConfigurationStatuses = createExpectedConfigurationStatuses(useIdentityPool);
    var actualUpdateDataOrderStatusRecords =
        consumerRecords.subList(
            consumerRecords.size() - SUCCESSFUL_UPDATE_EXPECTED_NUMBER_OF_STATUSES,
            consumerRecords.size());
    assertDpoAndConfigStatusesMatchExpected(
        actualUpdateDataOrderStatusRecords, orderOfStatusTypes, orderOfConfigurationStatuses);
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    var updateResult =
        extractResultOfType(dataPlatformObjectStatuses, CreateDataOrderCommandResult.class);
    assertThat(updateResult).isEqualTo(dataOrderCreationResult);
    assertAppropriateStatusRecordReturnedFor(
        CommandType.UPDATE,
        consumerRecord,
        command,
        topics.stream().filter(topicName -> !topicName.contains("dlq")).toList());
    assertConnectionInfoIsSet(dataOrderId.toString(), dataOrderCreationResult);
    verify(topicService, times(1))
        .associateSchemaToInputTopic(eq(dataOrderId.toString()), eq(schemaId));

    // confirm that ACL are cleared and created again
    wiremock.verify(
        WireMock.deleteRequestedFor(WireMock.urlMatching("^/kafka/v3/clusters/clstr/acls.+"))
            .withQueryParam("resource_name", WireMock.equalTo(inputTopic))
            .withQueryParam("resource_type", WireMock.equalTo("TOPIC"))
            .withQueryParam("operation", WireMock.equalTo("ANY"))
            .withQueryParam("permission", WireMock.equalTo("ANY")));

    wiremock.verify(
        WireMock.deleteRequestedFor(WireMock.urlMatching("^/kafka/v3/clusters/clstr/acls.+"))
            .withQueryParam("resource_name", WireMock.equalTo(outputTopic))
            .withQueryParam("resource_type", WireMock.equalTo("TOPIC"))
            .withQueryParam("operation", WireMock.equalTo("ANY"))
            .withQueryParam("permission", WireMock.equalTo("ANY")));

    wiremock.verify(
        WireMock.postRequestedFor(WireMock.urlEqualTo("/kafka/v3/clusters/clstr/acls:batch")));
  }

  private static Stream<Arguments> provideInvalidDataOrderParameters() {
    return Stream.of(
        Arguments.of(
            2,
            0,
            2,
            List.of(
                "Data platform object validation failed.\n Invalid update dataorder command properties: partitionCount"
                    + " invalid")),
        Arguments.of(
            2,
            1,
            2,
            List.of(
                "Data platform object validation failed.\n Invalid update dataorder command properties: partitionCount"
                    + " invalid. Current partition count: 2. New partition"
                    + " count: 1")));
  }

  @ParameterizedTest(
      name =
          "Initial Partition Count: {0}, New Partition Count: {1}, Retention Days: {2},"
              + " Error message: {3}")
  @MethodSource("provideInvalidDataOrderParameters")
  @SneakyThrows
  void givenTopicsExists_whenInvalidUpdateCommandIsSent_thenProvisioningStatusIsFailed(
      int initialPartitionCount,
      int newPartitionCount,
      int retentionPeriodInDays,
      List<String> errors) {

    var dataOrderId = UUID.randomUUID();

    var request = new CreateTopicsRequest(dataOrderId.toString(), initialPartitionCount, 1);
    createTopics(request);

    var properties =
        new ModifyDataOrderCommand(
            createStubDataOrder(
                UUID.randomUUID().toString(), newPartitionCount, retentionPeriodInDays),
            null,
            null);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.UPDATE,
            dataOrderId,
            ConfigurationType.DATA_ORDER,
            JsonObject.mapFrom(properties));

    var consumerRecords = commandExecuteAndAwaitNStatuses(command, 2).get();

    var orderOfStatusTypes = createExpectedDpoStatusesForValidationFailure();
    var orderOfConfigurationStatuses =
        List.of(ConfigurationStatus.IN_PROVISIONING, ConfigurationStatus.DISABLED);
    assertDpoAndConfigStatusesMatchExpected(
        consumerRecords, orderOfStatusTypes, orderOfConfigurationStatuses);

    var consumerRecord = consumerRecords.getLast();

    assertThat(consumerRecord).isNotNull();
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    assertDpoStatusesReportedError(dataPlatformObjectStatuses, String.join(", ", errors));
    assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);
    removeDpoAndConfigurationStatus(consumerRecord.value().properties());
    assertThat(consumerRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                RequestJobStatus.FAILED,
                command.objectId(),
                command.type(),
                command.properties(),
                null,
                errors));
  }

  private static @NotNull List<List<DataPlatformObjectStatus>>
      createExpectedDpoStatusesForValidationFailure() {
    return List.of(
        List.of(
            DataPlatformObjectStatus.of(
                DataPlatformObjectStatus.ProvisioningStep.CONFIGURATION_OBJECT_UPDATE,
                DataPlatformObjectStatus.ProvisioningStepType.POSTGRES_DB_OBJECT,
                ProvisioningStatus.COMPLETED)),
        List.of(
            DataPlatformObjectStatus.of(
                DataPlatformObjectStatus.ProvisioningStep.CONFIGURATION_OBJECT_UPDATE,
                DataPlatformObjectStatus.ProvisioningStepType.POSTGRES_DB_OBJECT,
                ProvisioningStatus.COMPLETED)));
  }

  @DisplayName(
      "Given a data order and its pipeline exist, when valid update command is sent, then the update for the pipeline is triggered as well.")
  @Test
  void triggerPipelineUpdate()
      throws InterruptedException, JsonProcessingException, ExecutionException {
    final var cmdArgCaptor = ArgumentCaptor.forClass(Command.class);
    final var dataOrderId = UUID.randomUUID();
    final var pipelineId = UUID.randomUUID();

    when(this.mockCreateAzureBlobStorageHandler.handle(any()))
        .thenReturn(CompletableFuture.completedStage(null));

    this.createDataOrder(dataOrderId);

    final var command =
        this.objectMapper.readValue(
            Fixture.asString(
                "dataorder/update/update-with-pipeline-template.json",
                Map.of(
                    "dataOrderId", dataOrderId.toString(),
                    "schemaId", updateSchemaId,
                    "pipelineId", pipelineId.toString(),
                    "daoId", this.daoId.toString())),
            Command.class);

    when(this.mockUpdatePipelineHandler.handle(any()))
        .thenReturn(CompletableFuture.completedStage(null));

    kafkaCompanion
        .topics()
        .waitForTopic(kafkaConfig.getInputTopicName(dataOrderId.toString()))
        .await()
        .indefinitely();

    var expectedNumberOfStatusRecords =
        SUCCESSFUL_CREATION_EXPECTED_NUMBER_OF_STATUSES
            + SUCCESSFUL_UPDATE_EXPECTED_NUMBER_OF_STATUSES;
    var consumerRecords =
        commandExecuteAndAwaitNStatuses(command, expectedNumberOfStatusRecords).get();

    var actualUpdateDataOrderStatusRecords =
        consumerRecords.subList(
            consumerRecords.size() - SUCCESSFUL_UPDATE_EXPECTED_NUMBER_OF_STATUSES,
            consumerRecords.size());

    assertDpoAndConfigStatusesMatchExpected(
        actualUpdateDataOrderStatusRecords,
        createExpectedDpoStatusesForCompleteUpdate(useIdentityPool),
        createExpectedConfigurationStatuses(useIdentityPool));
    assertThat(consumerRecords.getLast().value())
        .satisfies(
            status -> {
              assertThat(status.commandType()).isEqualTo(CommandType.UPDATE);
              assertThat(status.type()).isEqualTo(ConfigurationType.DATA_ORDER);
              assertThat(status.status()).isEqualTo(RequestJobStatus.COMPLETED);
            });

    verify(this.mockUpdatePipelineHandler, times(1)).handle(cmdArgCaptor.capture());

    assertThat(cmdArgCaptor.getValue())
        .satisfies(
            cmd -> {
              assertThat(cmd.commandType()).isEqualTo(CommandType.UPDATE);
              assertThat(cmd.type()).isEqualTo(ConfigurationType.FLINK_JOB_PIPELINE);
              assertThat(cmd.objectId()).isEqualTo(pipelineId);
              assertThat(
                      this.objectMapper.convertValue(cmd.properties(), UpdatePipelineCommand.class))
                  .satisfies(
                      cmdUpdate -> {
                        assertThat(cmdUpdate.dataOrderId()).isEqualTo(dataOrderId);
                        assertThat(cmdUpdate.pipeline().pipelineId()).isEqualTo(pipelineId);
                        assertThat(cmdUpdate.pipeline().category())
                            .isEqualTo(StreamingPipelineCategory.CUSTOM);
                        assertThat(cmdUpdate.additionalProperties()).isNotNull();
                        assertThat(cmdUpdate.metadataProperties()).isNotNull();
                      });
            });
    verify(topicService, times(1))
        .associateSchemaToInputTopic(eq(dataOrderId.toString()), eq(schemaId));
  }
}
