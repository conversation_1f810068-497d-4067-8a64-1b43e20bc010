package org.ude.deployment.dataorder;

import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.SneakyThrows;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.ude.deployment.azureblobstorage.handler.CreateAzureBlobStorageHandler;
import org.ude.deployment.util.TestProfileWithConfluentIdentityPoolDisabled;

@QuarkusTest
@TestProfile(TestProfileWithConfluentIdentityPoolDisabled.class)
public class CreateDataOrderHandlerNoConfluentIdentityPoolIT extends CreateDataOrderHandlerBaseIT {
  @InjectMock CreateAzureBlobStorageHandler mockCreateAzureBlobStorageHandler;

  @DisplayName(
      "Given a command for a dataorder with DAO creation and Confluent Identity Pools disabled, when it is consumed, then it is"
          + " routed and ACLs are not created.")
  @Test
  @SneakyThrows
  void givenCommandWithDaoPropertiesAndConfluentIDPDisabled_whenConsumed_thenItIsRouted() {
    var dataOrderId = UUID.randomUUID();

    Mockito.when(this.mockCreateAzureBlobStorageHandler.handle(Mockito.any()))
        .thenReturn(CompletableFuture.completedStage(null));

    testDataOrderCreated(dataOrderId, false);
  }
}
