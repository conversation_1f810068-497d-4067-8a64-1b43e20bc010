package org.ude.deployment.dataorder;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.ude.deployment.common.infrastructure.kafka.TopicService.RETENTION_MS_KEY;
import static org.ude.deployment.common.status.StatusUtils.createDataPlatformObjectStatus;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesDoNotContainResults;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedError;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.extractResultOfType;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import io.confluent.kafka.schemaregistry.client.rest.entities.requests.RegisterSchemaResponse;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.apache.kafka.common.config.ConfigResource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.ude.deployment.azureblobstorage.command.CreateAzureBlobStorageCommand;
import org.ude.deployment.azureblobstorage.handler.CreateAzureBlobStorageHandler;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.dataorder.dto.CreateDataOrderCommand;
import org.ude.deployment.dataorder.dto.CreateDataOrderCommandResult;

@QuarkusTest
public class CreateDataOrderHandlerIT extends CreateDataOrderHandlerBaseIT {
  @InjectMock CreateAzureBlobStorageHandler mockCreateAzureBlobStorageHandler;

  List<ConfigurationType> getDataOrderConfigurationTypes() {
    return List.of(ConfigurationType.USE_CASE, ConfigurationType.DATA_ORDER);
  }

  @DisplayName("Given a command for data order creation, when it is consumed, then it is routed.")
  @ParameterizedTest
  @MethodSource("getDataOrderConfigurationTypes")
  @SneakyThrows
  void givenCommandWithCorrectProperties_whenConsumed_thenSuccessStatusReturned(
      ConfigurationType configurationType) {

    var dataOrderId = UUID.randomUUID();
    var properties = new CreateDataOrderCommand(createStubDataOrder(schemaId, 2, 10), null);

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            dataOrderId,
            configurationType,
            JsonObject.mapFrom(properties));

    when(this.mockCreateAzureBlobStorageHandler.handle(any()))
        .thenReturn(CompletableFuture.completedStage(null));

    var consumerRecords =
        this.commandExecuteAndAwaitNStatuses(
                command, SUCCESSFUL_CREATION_EXPECTED_NUMBER_OF_STATUSES)
            .get();
    var consumerRecord = consumerRecords.getLast();

    // Verify status update sent and has success
    var topicNames =
        List.of(
            kafkaConfig.getInputTopicName(dataOrderId.toString()),
            kafkaConfig.getOutputTopicName(dataOrderId.toString()),
            kafkaConfig.getNonRetryableDlqTopicName(dataOrderId.toString()));

    var result =
        new CreateDataOrderCommandResult(
            topicNames, new JsonObject(stubSchema).mapTo(RegisterSchemaResponse.class), null);

    assertThat(consumerRecord).isNotNull();
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());

    var expectedOrderOfDpoStatusTypes = createExpectedDpoStatusesForCompletedCreation();
    var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
    assertDpoAndConfigStatusesMatchExpected(
        consumerRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    removeDpoAndConfigurationStatus(consumerRecord.value().properties());

    assertThat(consumerRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                RequestJobStatus.COMPLETED,
                command.objectId(),
                command.type(),
                command.properties(),
                JsonObject.mapFrom(result),
                List.of()));

    var actualResult =
        extractResultOfType(dataPlatformObjectStatuses, CreateDataOrderCommandResult.class);
    assertThat(actualResult).isEqualTo(result);
    assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);

    // Verify the topics exist
    var admin = kafkaAdminClientProvider.getAdmin();
    var allTopicNames = admin.listTopics().names().get();
    topicNames.forEach(topicName -> assertThat(allTopicNames).contains(topicName));

    // verify retention period was set correctly
    var topicResources =
        topicNames.stream()
            .map(topicName -> new ConfigResource(ConfigResource.Type.TOPIC, topicName))
            .collect(Collectors.toList());

    admin
        .describeConfigs(topicResources)
        .all()
        .get()
        .forEach(
            (configResource, config) -> {
              assertThat(Integer.parseInt(config.get("max.message.bytes").value()) >> 20)
                  .isEqualTo(7); // 7MB
              assertThat(config.get(RETENTION_MS_KEY).value())
                  .as("Checking retention period for topic: %s", configResource.name())
                  .isEqualTo(
                      String.valueOf(
                          TimeUnit.DAYS.toMillis(
                              properties.request().configuration().retentionPeriod())));
            });
  }

  @DisplayName(
      "Given a command for a data order with DAO creation, when it is consumed, then it is"
          + " routed and ACLs are created.")
  @Test
  @SneakyThrows
  void givenCommandWithDaoProperties_whenConsumed_thenItIsRouted() {
    var dataOrderId = UUID.randomUUID();

    when(this.mockCreateAzureBlobStorageHandler.handle(any()))
        .thenReturn(CompletableFuture.completedStage(null));

    testDataOrderCreated(dataOrderId, true);
  }

  Stream<Arguments> getCommandFailureOptions() {
    return Stream.of(
        Arguments.of(
            schemaId,
            0,
            "Data platform object validation failed.\n Invalid create topic command properties: partitionCount invalid"),
        Arguments.of(
            null,
            1,
            "Data platform object validation failed.\n Invalid create topic command properties: schemaIds missing"),
        Arguments.of(
            notFoundSchemaId,
            1,
            "Data platform object validation failed.\n Schema(s) %s not found"
                .formatted(notFoundSchemaId)));
  }

  @DisplayName(
      "Given a command for data order creation with wrong properties, when it is consumed,"
          + " then it is routed with errors info.")
  @ParameterizedTest
  @MethodSource("getCommandFailureOptions")
  @SneakyThrows
  void givenCommandWithWrongProperties_whenConsumed_thenFailedStatusReturned(
      String schemaIdToUse, int partitionCount, String errorMessage) {

    var properties =
        new CreateDataOrderCommand(createStubDataOrder(schemaIdToUse, partitionCount, 10), null);
    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            UUID.randomUUID(),
            ConfigurationType.DATA_ORDER,
            JsonObject.mapFrom(properties));

    var consumerRecords = commandExecuteAndAwaitNStatuses(command, 2).get();
    var consumerRecord = consumerRecords.getLast();

    var expectedOrderOfDpoStatusTypes = createExpectedDpoStatusesForValidationFailure();
    var orderOfConfigurationStatuses = createExpectedConfigurationStatusesForValidationFailure();
    assertDpoAndConfigStatusesMatchExpected(
        consumerRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);

    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerRecord.value().properties());
    removeDpoAndConfigurationStatus(consumerRecord.value().properties());

    assertThat(consumerRecord).isNotNull();
    assertThat(consumerRecord.key()).isEqualTo(command.commandId());
    assertThat(consumerRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                RequestJobStatus.FAILED,
                command.objectId(),
                command.type(),
                command.properties(),
                null,
                List.of(errorMessage)));

    assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);
    assertDpoStatusesReportedError(dataPlatformObjectStatuses, errorMessage);
  }

  private List<ConfigurationStatus> createExpectedConfigurationStatusesForValidationFailure() {
    return List.of(ConfigurationStatus.IN_PROVISIONING, ConfigurationStatus.DISABLED);
  }

  private List<List<DataPlatformObjectStatus>> createExpectedDpoStatusesForValidationFailure() {
    return List.of(
        createExpectedDpoStatusesFor(ProvisioningStatus.NOT_STARTED),
        createExpectedDpoStatusesForTopicValidationFailure());
  }

  private List<DataPlatformObjectStatus> createExpectedDpoStatusesForTopicValidationFailure() {
    return createExpectedDpoStatusesFor(ProvisioningStatus.NOT_STARTED).stream()
        .map(
            dpoStatus ->
                dpoStatus
                        .step()
                        .equals(DataPlatformObjectStatus.ProvisioningStep.KAFKA_TOPIC_PROVISIONING)
                    ? createDataPlatformObjectStatus(
                        DataPlatformObjectStatus.ProvisioningStep.KAFKA_TOPIC_PROVISIONING,
                        ProvisioningStatus.FAILED,
                        null,
                        null)
                    : dpoStatus)
        .collect(Collectors.toList());
  }

  @DisplayName(
      "Given a command for data order creation, when it is consumed, then the blob storage handler is triggered with the necessary parameters.")
  @Test
  @SneakyThrows
  void sendCreateAzureBlobStorageCommand() {
    final var captorCmd = ArgumentCaptor.forClass(Command.class);

    when(this.mockCreateAzureBlobStorageHandler.handle(any()))
        .thenReturn(CompletableFuture.completedStage(null));

    var consumerRecords =
        this.commandExecuteAndAwaitNStatuses(
                new Command(
                    UUID.randomUUID().toString(),
                    CommandType.CREATE,
                    UUID.randomUUID(),
                    ConfigurationType.DATA_ORDER,
                    JsonObject.mapFrom(
                        new CreateDataOrderCommand(createStubDataOrder(schemaId, 2, 10), null))),
                3)
            .get();

    var expectedOrderOfDpoStatusTypes = createExpectedDpoStatusesForCompletedCreation();
    var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
    assertDpoAndConfigStatusesMatchExpected(
        consumerRecords, expectedOrderOfDpoStatusTypes, orderOfConfigurationStatuses);

    verify(this.mockCreateAzureBlobStorageHandler, times(1)).handle(captorCmd.capture());

    assertThat(captorCmd.getValue())
        .satisfies(
            cmd -> {
              assertThat(cmd.commandType()).isEqualTo(CommandType.CREATE);
              assertThat(cmd.type()).isEqualTo(ConfigurationType.AZURE_BLOB_STORAGE);
              assertThat(
                      this.objectMapper.convertValue(
                          cmd.properties(), CreateAzureBlobStorageCommand.class))
                  .isInstanceOfSatisfying(
                      CreateAzureBlobStorageCommand.class,
                      azureCmd -> {
                        assertThat(azureCmd.dataOrderId()).isNotNull();
                        assertThat(azureCmd.entraGroupIds()).isEmpty();
                        assertThat(azureCmd.retentionPeriod().toDays()).isEqualTo(10);
                      });
            });
  }
}
