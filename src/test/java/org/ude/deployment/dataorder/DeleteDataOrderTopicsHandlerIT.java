package org.ude.deployment.dataorder;

import io.quarkus.test.junit.QuarkusTest;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

@QuarkusTest
class DeleteDataOrderTopicsHandlerIT extends DeleteDataOrderTopicsHandlerBaseIT {
  @DisplayName(value = "Given delete topic command then command is handled")
  @Test
  void givenDeleteTopic_whenCommandIsSent_thenAclsAndTopicAreDeleted()
      throws ExecutionException, InterruptedException {
    testDataOrderTopicsDeletion(UUID.randomUUID(), true);
  }
}
