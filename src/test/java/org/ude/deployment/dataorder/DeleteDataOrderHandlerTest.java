package org.ude.deployment.dataorder;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.ude.deployment.dataorder.DeleteDataOrderHandler.VALIDATION_ERROR_CATEGORY_REQUIRED;
import static org.ude.deployment.dataorder.DeleteDataOrderHandler.VALIDATION_ERROR_PIPELINE_ID_REQUIRED;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.smallrye.mutiny.Uni;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.ude.deployment.azureblobstorage.handler.DeleteAzureBlobStorageHandler;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.CommandProducer;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.dataorder.dto.ModifyDataOrderCommand;
import org.ude.deployment.streamingpipeline.dto.DeleteStreamingPipelineProperties;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineCategory;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineRequest;

@ExtendWith(MockitoExtension.class)
public class DeleteDataOrderHandlerTest {

  @Mock CommandProducer commandProducer;

  @Mock ObjectMapper objectMapper;

  @Mock StatusUpdateService statusUpdateService;
  @Mock DeleteAzureBlobStorageHandler deleteAzureBlobStorageHandler;

  DeleteDataOrderHandler handler;

  @BeforeEach
  void setUp() {
    handler =
        new DeleteDataOrderHandler(
            commandProducer, objectMapper, statusUpdateService, deleteAzureBlobStorageHandler);
  }

  Command createCommand() {
    return new Command(
        UUID.randomUUID().toString(),
        CommandType.DELETE,
        UUID.randomUUID(),
        ConfigurationType.DATA_ORDER_TOPIC,
        null);
  }

  @DisplayName(
      "Given a DELETE DataOrder command, when pipeline is present in deleteDataOrderCommand, then DELETE StreamingPipeline command is sent")
  @Test
  @SneakyThrows
  void givenPipelineDataExists_whenCommandReceived_thenDeletePipelineCommandSent() {
    // Given
    var originalCommand = createCommand();
    when(commandProducer.sendCommand(any()))
        .thenReturn(Uni.createFrom().voidItem().subscribeAsCompletionStage());

    var pipeline =
        new StreamingPipelineRequest(
            UUID.randomUUID(),
            StreamingPipelineCategory.CUSTOM,
            List.of(originalCommand.objectId()));
    var dataOrderCommand = new ModifyDataOrderCommand(null, null, pipeline);

    // When
    var result = handler.executeDeleteOperation(originalCommand, dataOrderCommand);

    // Then
    assertThat(result.toCompletableFuture().get())
        .satisfies(
            command -> {
              assertThat(command.commandType()).isEqualTo(CommandType.DELETE);
              assertThat(command.objectId()).isEqualTo(pipeline.pipelineId());
              assertThat(command.type()).isEqualTo(ConfigurationType.STREAMING_PIPELINE);
              assertThat(command.properties().mapTo(DeleteStreamingPipelineProperties.class))
                  .extracting(DeleteStreamingPipelineProperties::request)
                  .isEqualTo(pipeline);
            });
  }

  @ParameterizedTest
  @MethodSource("pipelineValidationTestCases")
  void givenDeleteDataOrderCommand_whenValidationIsCalled_thenCorrectResultReturned(
      String testCase, ModifyDataOrderCommand command, List<String> expectedErrors) {

    // When
    List<String> actualErrors = handler.validatePipelineProperties(command);

    // Then
    assertThat(actualErrors)
        .describedAs(testCase)
        .containsExactlyInAnyOrderElementsOf(expectedErrors);
  }

  private static Stream<Arguments> pipelineValidationTestCases() {
    return Stream.of(
        Arguments.of(
            "Should return no errors when pipeline is null",
            new ModifyDataOrderCommand(null, null, null),
            List.of()),
        Arguments.of(
            "Should return no errors when pipeline data is complete",
            new ModifyDataOrderCommand(
                null,
                null,
                new StreamingPipelineRequest(
                    UUID.randomUUID(),
                    StreamingPipelineCategory.CUSTOM,
                    List.of(UUID.randomUUID()))),
            List.of()),
        Arguments.of(
            "Should return error when pipelineId is null",
            new ModifyDataOrderCommand(
                null,
                null,
                new StreamingPipelineRequest(
                    null, StreamingPipelineCategory.CUSTOM, List.of(UUID.randomUUID()))),
            List.of(VALIDATION_ERROR_PIPELINE_ID_REQUIRED)),
        Arguments.of(
            "Should return error when category is null",
            new ModifyDataOrderCommand(
                null,
                null,
                new StreamingPipelineRequest(UUID.randomUUID(), null, List.of(UUID.randomUUID()))),
            List.of(VALIDATION_ERROR_CATEGORY_REQUIRED)),
        Arguments.of(
            "Should return multiple errors when both pipelineId and category are null",
            new ModifyDataOrderCommand(null, null, new StreamingPipelineRequest(null, null, null)),
            List.of(VALIDATION_ERROR_PIPELINE_ID_REQUIRED, VALIDATION_ERROR_CATEGORY_REQUIRED)));
  }
}
