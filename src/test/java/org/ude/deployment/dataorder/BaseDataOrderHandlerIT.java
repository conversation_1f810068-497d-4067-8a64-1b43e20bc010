package org.ude.deployment.dataorder;

import static com.github.tomakehurst.wiremock.client.WireMock.delete;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.matchingJsonPath;
import static com.github.tomakehurst.wiremock.client.WireMock.notFound;
import static com.github.tomakehurst.wiremock.client.WireMock.okJson;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityPool;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListResponse;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListResponseMetadata;
import org.ude.deployment.common.infrastructure.kafka.KafkaAdminClientProvider;
import org.ude.deployment.common.infrastructure.kafka.KafkaConfig;
import org.ude.deployment.common.infrastructure.kafka.TopicService;
import org.ude.deployment.common.wiremock.InjectWireMock;
import org.ude.deployment.common.wiremock.WiremockResource;
import org.ude.deployment.dataorder.dto.DataOrder;
import org.ude.deployment.dataorder.dto.DataOrder.DataOrderConfiguration;
import org.ude.deployment.util.KafkaUtils;

@QuarkusTest
@QuarkusTestResource(WiremockResource.class)
public class BaseDataOrderHandlerIT extends BaseHandlerIntegrationTest {

  @InjectWireMock WireMockServer wiremock;

  @Inject KafkaConfig kafkaConfig;

  @Inject TopicService topicService;

  @Inject ObjectMapper objectMapper;

  @Inject KafkaAdminClientProvider kafkaAdminClientProvider;

  protected final String schemaId = UUID.randomUUID().toString();
  protected final String updateSchemaId = UUID.randomUUID().toString();
  protected final String notFoundSchemaId = UUID.randomUUID().toString();

  protected final UUID daoId = UUID.randomUUID();
  protected final UUID dataOrderManagerId = UUID.randomUUID();
  protected final UUID dataOrderReaderId = UUID.randomUUID();
  protected final UUID dataConsumerId = UUID.randomUUID();
  protected final UUID dataProducerId = UUID.randomUUID();

  protected Map<String, ConfluentIdentityPool> identityPoolsStubs;

  @BeforeAll
  public void init() {
    identityPoolsStubs =
        Map.of(
            "data-order-manager",
            new ConfluentIdentityPool(
                "pool-ordrnmgr",
                "claims.sub",
                "'%s' in claims.groups".formatted(dataOrderManagerId.toString()),
                "dao-%s-data-order-manager".formatted(daoId.toString()),
                "Identity pool for data-order-manager of DAO 'dao.name'"),
            "data-order-reader",
            new ConfluentIdentityPool(
                "pool-ordrrdr",
                "claims.sub",
                "'%s' in claims.groups".formatted(dataOrderReaderId.toString()),
                "dao-%s-data-order-reader".formatted(daoId.toString()),
                "Identity pool for data-order-reader of DAO 'dao.name'"),
            "data-consumer",
            new ConfluentIdentityPool(
                "pool-cnsmr",
                "claims.sub",
                "'%s' in claims.groups".formatted(dataConsumerId.toString()),
                "dao-%s-data-consumer".formatted(daoId.toString()),
                "Identity pool for data-consumer of DAO 'dao.name'"),
            "data-producer",
            new ConfluentIdentityPool(
                "pool-prdcr",
                "claims.sub",
                "'%s' in claims.groups".formatted(dataProducerId.toString()),
                "dao-%s-data-producer".formatted(daoId.toString()),
                "Identity pool for data-producer of DAO 'dao.name'"));

    initSchemaRegistryWiremock();
    initConfluentWiremock();
  }

  @AfterEach
  public void cleanup() {
    KafkaUtils.clearTopic(kafkaCompanion, statusTopic);
  }

  @NotNull protected DataOrder createStubDataOrder(
      String schemaIdToUse, int partitionCount, int retentionPeriodInDays) {
    return createStubDataOrderWithAdditionalProperties(
        schemaIdToUse, partitionCount, retentionPeriodInDays, null);
  }

  @NotNull protected DataOrder createStubDataOrderWithAdditionalProperties(
      String schemaIdToUse,
      int partitionCount,
      int retentionPeriodInDays,
      JsonObject additionalProperties) {
    var schemaIds = new ArrayList<String>();
    if (schemaIdToUse != null) {
      schemaIds.add(schemaIdToUse);
    }

    var configuration = new DataOrderConfiguration(retentionPeriodInDays, partitionCount);
    return new DataOrder(
        "DataOrder.gsk.xyz",
        "Some Description",
        "15374073-41a9-48dc-916e-c32bf67d36eb",
        true,
        "15374073-41a9-48dc-916e-c32bf67d36eb",
        schemaIds,
        configuration,
        additionalProperties,
        null);
  }

  String stubSchema =
      """
{
    "id": 444,
    "version": 1,
    "schemaType": "JSON",
    "schema": "{\\n  \\"type\\": \\"object\\",\\n  \\"properties\\": {\\n    \\"id\\": {\\"type\\": \\"integer\\"},\\n    \\"name\\": {\\"type\\": \\"string\\"}\\n  },\\n  \\"required\\": [\\"id\\", \\"name\\"]\\n}"
}
    """;

  String stubConnectionInfo =
      """
{
    "topic": "<TOPIC>",
    "properties": {
        "sasl.oauthbearer.token.endpoint.url": "https://login.microsoftonline.com/c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c/oauth2/v2.0/token",
        "client.dns.lookup": "use_all_dns_ips",
        "sasl.mechanism": "OAUTHBEARER",
        "sasl.login.callback.handler.class": "org.apache.kafka.common.security.oauthbearer.secured.OAuthBearerLoginCallbackHandler",
        "security.protocol": "SASL_SSL",
        "sasl.jaas.config": "org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule required clientId=\\"<SPN_CLIENT_ID>\\" clientSecret=\\"<SPN_CLIENT_SECRET>\\" scope=\\"<SPN_SCOPE>\\" extension_logicalCluster=\\"clstr\\" extension_identityPoolId=\\"pool-cnsmr\\";",
        "extension.identity_pool_id": "pool-cnsmr",
        "compression.type": "none",
        "extension.logical_cluster": "clstr"
    }
}
""";

  private void initSchemaRegistryWiremock() {
    wiremock.resetAll();
    wiremock.stubFor(
        get(urlPathTemplate(
                "/subjects/%s/versions/latest"
                    .formatted(kafkaConfig.getSchemaSubjectName(schemaId))))
            .atPriority(1)
            .willReturn(okJson(stubSchema)));
    wiremock.stubFor(
        get(urlPathTemplate(
                "/subjects/%s/versions/latest"
                    .formatted(kafkaConfig.getSchemaSubjectName(updateSchemaId))))
            .atPriority(1)
            .willReturn(okJson(stubSchema)));

    wiremock.stubFor(
        get(urlPathTemplate(
                "/subjects/%s/versions/latest"
                    .formatted(kafkaConfig.getSchemaSubjectName(notFoundSchemaId))))
            .atPriority(1)
            .willReturn(notFound()));

    wiremock.stubFor(
        get(urlPathTemplate(
                "/subjects/%s/versions".formatted(kafkaConfig.getSchemaSubjectName(schemaId))))
            .atPriority(3)
            .willReturn(okJson(stubSchema)));

    wiremock.stubFor(
        post(urlPathTemplate("/subjects/{subject}")).atPriority(1).willReturn(okJson(stubSchema)));

    wiremock.stubFor(
        post(urlPathTemplate("/subjects/{subject}/versions"))
            .atPriority(1)
            .willReturn(okJson(stubSchema)));

    wiremock.stubFor(
        delete(urlPathTemplate("/subjects/{subject}")).atPriority(1).willReturn(okJson("[1,2,3]")));
  }

  String stubIdentityProviderId = "idnt-prvdr";
  String stubIdentityProvider =
      """
    {
        "api_version": "iam/v2",
        "kind": "IdentityProvider",
        "id": "idnt-prvdr",
        "display_name": "Test Tenant",
        "description": "Test  Tenant",
        "identity_claim": "claims.sub",
        "state": "ENABLED",
        "issuer": "https://login.microsoftonline.com/c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c/v2.0",
        "jwks_uri": "https://login.microsoftonline.com/common/discovery/v2.0/keys",
        "keys": []
    }
""";

  String stubIdentityPoolCreated =
      """
    {
        "api_version": "iam/v2",
        "kind": "CertificateIdentityPool",
        "metadata": {
            "self": "https://confluent.cloud/api/iam/v2/identity-providers/idnt-prvdr/identity-pools/pool-3nPl",
            "resource_name": "crn://confluent.cloud/organization=9cc94473-3f84-461b-a66b-22badbf7799b/identity-provider=idnt-prvdr/identity-pool=pool-3nPl",
            "created_at": "2024-09-10T13:02:24.021981Z",
            "updated_at": "2024-12-13T08:18:27.002810Z"
        },
        "id": "pool-3nPl",
        "display_name": "dao-{daoId}-{role}",
        "description": "Identity pool for {role} of DAO '{daoName}'",
        "identity_claim": "claims.sub",
        "filter": "'{roleId}' in claims.groups",
        "principal": "pool-3nPl",
        "state": "ENABLED"
    }
""";

  @SneakyThrows
  private void initConfluentWiremock() {
    wiremock.stubFor(
        get(urlPathTemplate("/iam/v2/identity-providers/{providerId}"))
            .atPriority(1)
            .withPathParam("providerId", WireMock.equalTo(stubIdentityProviderId))
            .willReturn(okJson(stubIdentityProvider)));

    var poolsResponse =
        new ConfluentListResponse<ConfluentIdentityPool>(
            "iam/v2",
            "IdentityPoolList",
            new ConfluentListResponseMetadata(),
            identityPoolsStubs.values().stream().toList());

    var poolsJson = objectMapper.writeValueAsString(poolsResponse);

    wiremock.stubFor(
        get(urlPathTemplate("/iam/v2/identity-providers/{providerId}/identity-pools"))
            .atPriority(1)
            .withPathParam("providerId", WireMock.equalTo(stubIdentityProviderId))
            .willReturn(okJson(poolsJson)));

    identityPoolsStubs.entrySet().stream()
        .forEach(
            entry -> {
              var role = entry.getKey();
              var pool = entry.getValue();
              String json;
              try {
                json = objectMapper.writeValueAsString(pool);
              } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
              }

              wiremock.stubFor(
                  post(urlPathTemplate("/iam/v2/identity-providers/{providerId}/identity-pools"))
                      .atPriority(1)
                      .withPathParam("providerId", WireMock.equalTo(stubIdentityProviderId))
                      .withRequestBody(
                          matchingJsonPath(
                              "$[?(@.display_name == \"dao-%s-%s\")]"
                                  .formatted(daoId.toString(), role)))
                      .willReturn(okJson(json)));
            });

    wiremock.stubFor(
        delete(urlPathTemplate("/kafka/v3/clusters/clstr/acls"))
            .atPriority(1)
            .willReturn(okJson("{}")));

    wiremock.stubFor(
        post(urlPathTemplate("/kafka/v3/clusters/clstr/acls:batch"))
            .atPriority(1)
            .willReturn(okJson("{}")));
  }
}
