package org.ude.deployment.dataorder;

import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.ude.deployment.util.TestProfileWithConfluentIdentityPoolDisabled;

@QuarkusTest
@TestProfile(TestProfileWithConfluentIdentityPoolDisabled.class)
public class DeleteDataOrderTopicsHandlerNoConfluentIdentityPoolIT
    extends DeleteDataOrderTopicsHandlerBaseIT {

  @DisplayName(
      value =
          "Given delete topic command with Confluent Identity Pools disabled, then topics are deleted, but ACL deletion is skipped.")
  @Test
  void givenDeleteTopicAndConfluentIDPDisabled_whenCommandIsSent_thenTopicAreDeleted()
      throws ExecutionException, InterruptedException {
    testDataOrderTopicsDeletion(UUID.randomUUID(), false);
  }
}
