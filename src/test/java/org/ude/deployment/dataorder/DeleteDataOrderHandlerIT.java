package org.ude.deployment.dataorder;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.ude.deployment.dataorder.DeleteDataOrderHandler.VALIDATION_ERROR_CATEGORY_REQUIRED;
import static org.ude.deployment.dataorder.DeleteDataOrderHandler.VALIDATION_ERROR_PIPELINE_ID_REQUIRED;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesDoNotContainResults;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedError;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.removeDpoAndConfigurationStatus;

import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.TopicService;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateTopicsRequest;
import org.ude.deployment.common.status.ConfigurationStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.dao.dto.DataAccessObject;
import org.ude.deployment.dataorder.dto.ModifyDataOrderCommand;
import org.ude.deployment.flink.DeletePipelineHandler;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineCategory;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineRequest;

@QuarkusTest
public class DeleteDataOrderHandlerIT extends BaseDataOrderHandlerIT {

  @Inject TopicService topicService;

  @InjectMock DeletePipelineHandler deletePipelineHandler;

  @BeforeAll
  public void setup() {
    when(deletePipelineHandler.handle(any(Command.class)))
        .thenReturn(CompletableFuture.completedFuture(null));
  }

  @DisplayName(
      value =
          "Given an existing data order, when a delete data order command is sent, then the command is correctly processed.")
  @ParameterizedTest
  @MethodSource("pipelineConfigurationArgs")
  @SneakyThrows
  void givenDeleteDataOrder_whenCommandIsSent_thenDeleteDataOrderIsInitiated(
      UUID dataOrderId,
      StreamingPipelineRequest pipeline,
      RequestJobStatus requestJobStatus,
      List<String> errors) {
    var command = createCommand(dataOrderId, pipeline);
    var consumerStatusRecord = this.commandExecuteAndAwaitStatus(command).get();
    if (requestJobStatus.equals(RequestJobStatus.DELETING)) {
      assertStatusesExistForDeletion(consumerStatusRecord);
    }
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(consumerStatusRecord.value().properties());
    if (errors.isEmpty()) {
      assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    } else {
      var dpoValidationErrorPrefix = "Data platform object validation failed.\n ";
      assertDpoStatusesReportedError(
          dataPlatformObjectStatuses, dpoValidationErrorPrefix + String.join(", ", errors));
      if (errors.size() == 1) {
        errors = List.of(dpoValidationErrorPrefix + errors.getFirst());
      } else {
        var modifiedFirstError = dpoValidationErrorPrefix + errors.getFirst();
        var newErrors = new ArrayList<>(errors.subList(1, errors.size()));
        newErrors.addFirst(modifiedFirstError);
        errors = List.of(String.join(", ", newErrors));
      }
    }
    assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);
    removeDpoAndConfigurationStatus(consumerStatusRecord.value().properties());

    assertThat(consumerStatusRecord).isNotNull();
    assertThat(consumerStatusRecord.key()).isEqualTo(command.commandId());
    assertThat(consumerStatusRecord.value())
        .isEqualTo(
            new Status(
                command.commandId(),
                command.commandType(),
                requestJobStatus,
                command.objectId(),
                command.type(),
                command.properties(),
                null,
                errors));
  }

  @SneakyThrows
  @Test
  void givenDeleteDataOrderWithNoPipeline_whenDeleteCommandIsSent_thenContainerIsDeleted() {
    var dataOrderId = UUID.randomUUID();
    var command = createCommand(dataOrderId, null);
    var consumerStatusRecords = this.commandExecuteAndAwaitNStatuses(command, 3).get();

    var topicDeletionStatus = consumerStatusRecords.get(0);
    assertDpoAndConfigStatusesMatchExpected(
        List.of(topicDeletionStatus),
        List.of(
            List.of(
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.KAFKA_TOPIC_DELETION,
                    DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                    ProvisioningStatus.DELETING))),
        List.of(ConfigurationStatus.IN_PROVISIONING));

    var storageDeletionStatus = consumerStatusRecords.get(1);
    assertDpoAndConfigStatusesMatchExpected(
        List.of(storageDeletionStatus),
        List.of(
            List.of(
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.STORAGE_DELETION,
                    DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                    ProvisioningStatus.DELETED))),
        List.of(ConfigurationStatus.DELETED));

    var topicsDeletionStatus = consumerStatusRecords.get(2);
    assertDpoAndConfigStatusesMatchExpected(
        List.of(topicsDeletionStatus),
        List.of(
            List.of(
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.KAFKA_TOPIC_DELETION,
                    DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                    ProvisioningStatus.DELETED))),
        List.of(ConfigurationStatus.DELETED));
  }

  Command createCommand(UUID dataOrderId, StreamingPipelineRequest pipeline) {
    int partitionCount = 1;
    int retentionPeriodInDays = 1;

    var createTopicRequest = new CreateTopicsRequest(dataOrderId.toString(), partitionCount, 1);
    var spn = UUID.randomUUID();
    var dataOrder =
        createStubDataOrder(dataOrderId.toString(), partitionCount, retentionPeriodInDays);
    var dao =
        new DataAccessObject(
            UUID.randomUUID(), "name", "description", spn, spn, spn, spn, Set.of(dataOrderId));
    var properties = new ModifyDataOrderCommand(dataOrder, dao, pipeline);

    createTopic(createTopicRequest);

    return new Command(
        UUID.randomUUID().toString(),
        CommandType.DELETE,
        dataOrderId,
        ConfigurationType.DATA_ORDER,
        JsonObject.mapFrom(properties));
  }

  private static void assertStatusesExistForDeletion(
      ConsumerRecord<String, Status> consumerStatusRecord) {
    assertDpoAndConfigStatusesMatchExpected(
        List.of(consumerStatusRecord),
        List.of(
            List.of(
                DataPlatformObjectStatus.of(
                    DataPlatformObjectStatus.ProvisioningStep.KAFKA_TOPIC_DELETION,
                    DataPlatformObjectStatus.ProvisioningStepType.REPOSITORY,
                    ProvisioningStatus.DELETING))),
        List.of(ConfigurationStatus.IN_PROVISIONING));
  }

  List<String> createTopic(CreateTopicsRequest request) {
    return this.topicService.processDataOrderTopicsCreation(request).await().indefinitely();
  }

  private static Stream<Arguments> pipelineConfigurationArgs() {
    var dataOrderIdForPipelineWithConfiguration = UUID.randomUUID();
    var pipelineWithConfiguration =
        new StreamingPipelineRequest(
            UUID.randomUUID(),
            StreamingPipelineCategory.CUSTOM,
            List.of(dataOrderIdForPipelineWithConfiguration));
    var dataOrderIdForPipelineWithoutCategory = UUID.randomUUID();
    var pipelineWithoutCategory =
        new StreamingPipelineRequest(
            UUID.randomUUID(), null, List.of(dataOrderIdForPipelineWithoutCategory));
    var dataOrderIdForPipelineWithoutPipelineId = UUID.randomUUID();
    var pipelineWithoutPipelineId =
        new StreamingPipelineRequest(
            null,
            StreamingPipelineCategory.CUSTOM,
            List.of(dataOrderIdForPipelineWithoutPipelineId));
    var dataOrderIdForPipelineWithoutIdAndCategory = UUID.randomUUID();
    var pipelineWithoutIdAndCategory =
        new StreamingPipelineRequest(
            null, null, List.of(dataOrderIdForPipelineWithoutIdAndCategory));
    return Stream.of(
        Arguments.of(
            dataOrderIdForPipelineWithConfiguration,
            pipelineWithConfiguration,
            RequestJobStatus.DELETING,
            List.of()),
        Arguments.of(
            dataOrderIdForPipelineWithoutCategory,
            pipelineWithoutCategory,
            RequestJobStatus.FAILED,
            List.of(VALIDATION_ERROR_CATEGORY_REQUIRED)),
        Arguments.of(
            dataOrderIdForPipelineWithoutPipelineId,
            pipelineWithoutPipelineId,
            RequestJobStatus.FAILED,
            List.of(VALIDATION_ERROR_PIPELINE_ID_REQUIRED)),
        Arguments.of(
            dataOrderIdForPipelineWithoutIdAndCategory,
            pipelineWithoutIdAndCategory,
            RequestJobStatus.FAILED,
            List.of(VALIDATION_ERROR_PIPELINE_ID_REQUIRED, VALIDATION_ERROR_CATEGORY_REQUIRED)));
  }
}
