package org.ude.deployment.common.infrastructure.kafka;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.MockitoConfig;
import io.smallrye.common.annotation.Identifier;
import io.smallrye.mutiny.Uni;
import io.smallrye.reactive.messaging.kafka.api.OutgoingKafkaRecordMetadata;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Path;
import jakarta.validation.Validator;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.reactive.messaging.Message;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.MockitoAnnotations;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;

@QuarkusTest
class JsonDeserializationFailureHandlerTest {

  @InjectMock private DeadLetterProducer deadLetterProducer;

  @InjectMock
  @MockitoConfig(convertScopes = true)
  private Validator validator;

  @Inject
  @Identifier("deserialization-failure-handler") JsonDeserializationFailureHandler jsonDeserializationFailureHandler;

  @Inject ObjectMapper objectMapper;

  @Captor private ArgumentCaptor<Message<byte[]>> byteMessageArgumentCaptor;

  @ConfigProperty(name = "mp.messaging.incoming.commands.topic")
  String commandsTopic;

  @BeforeEach
  public void init() {
    MockitoAnnotations.openMocks(this);
  }

  @DisplayName("Given a command, when consume and deserialize, then return command.")
  @Test
  void givenACommand_whenConsumeAndDeserialize_thenReturnCommand() throws JsonProcessingException {
    // Arrange
    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            UUID.randomUUID(),
            ConfigurationType.DEPARTMENT,
            new JsonObject().put("body", "<Resource Body>"));
    var deserialization = Uni.createFrom().item(command);
    when(validator.validate(command)).thenReturn(new HashSet<>());

    // Act
    var result =
        jsonDeserializationFailureHandler.decorateDeserialization(
            deserialization,
            commandsTopic,
            false,
            "CommandDeserializer",
            objectMapper.writeValueAsBytes(command),
            new RecordHeaders());

    // Assert
    var argumentCaptor = ArgumentCaptor.forClass(Command.class);
    verify(validator).validate(argumentCaptor.capture());
    var capturedCommand = argumentCaptor.getValue();
    assertThat(capturedCommand).isEqualTo(command);

    // Combine the two assertions into one
    assertThat(result).isNotNull().isEqualTo(command);
    verifyNoInteractions(deadLetterProducer);
  }

  @DisplayName(
      "Given a command which cannot be deserialized, when consume and deserialize, then emit"
          + " to dead letter queue and return null.")
  @Test
  void givenCommandWhichCannotBeDeserialized_whenConsumeAndDeserialize_thenReturnNull() {
    // Arrange
    var nonDeserializableCommand = new byte[20];
    new Random().nextBytes(nonDeserializableCommand);
    Uni<Command> deserialization =
        Uni.createFrom()
            .emitter(
                em ->
                    em.fail(
                        new JsonParseException(
                            "Failed to deserialize using" + " CommandDeserializer")));

    // Act
    var result =
        jsonDeserializationFailureHandler.decorateDeserialization(
            deserialization,
            commandsTopic,
            false,
            "CommandDeserializer",
            nonDeserializableCommand,
            new RecordHeaders());

    // Assert
    assertThat(result).isNull();
    verify(deadLetterProducer).sendMessage(byteMessageArgumentCaptor.capture(), eq(commandsTopic));

    var capturedMessage = byteMessageArgumentCaptor.getValue();
    assertThat(capturedMessage.getPayload()).isEqualTo(nonDeserializableCommand);
    verifyNoInteractions(validator);
  }

  @DisplayName(
      "Given a command with invalid property values, "
          + "when validate, then emit to dead letter queue and return null.")
  @Test
  void givenCommandWithInvalidPropertyValues_whenConsumeAndDeserialize_thenReturnNull()
      throws JsonProcessingException {
    // Arrange
    var command = new Command("", null, UUID.randomUUID(), null, null);
    var deserialization = Uni.createFrom().item(command);
    var mockedViolations =
        new HashSet<>(
            List.of(
                createMockedViolation("commandId", "commandId is mandatory"),
                createMockedViolation("commandType", "commandType is mandatory"),
                createMockedViolation("type", "type is mandatory"),
                createMockedViolation("properties", "properties is mandatory")));
    when(validator.validate(command)).thenReturn(mockedViolations);

    // Act
    var result =
        jsonDeserializationFailureHandler.decorateDeserialization(
            deserialization,
            commandsTopic,
            false,
            "CommandDeserializer",
            objectMapper.writeValueAsBytes(command),
            new RecordHeaders());

    // Assert
    assertThat(result).isNull();
    verify(deadLetterProducer).sendMessage(byteMessageArgumentCaptor.capture(), eq(commandsTopic));

    var capturedMessage = byteMessageArgumentCaptor.getValue();
    assertThat(capturedMessage.getPayload()).isEqualTo(objectMapper.writeValueAsBytes(command));

    var metadata = capturedMessage.getMetadata(OutgoingKafkaRecordMetadata.class).orElseThrow();
    var dlqHeaders = metadata.getHeaders();

    var errorDetails = new String(dlqHeaders.lastHeader("errorDetails").value());
    var expectedViolationErrorDetails =
        new String[] {
          "'commandId': commandId is mandatory",
          "'commandType': commandType is mandatory",
          "'type': type is mandatory",
          "'properties': properties is mandatory"
        };
    for (var expectedViolationErrorDetail : expectedViolationErrorDetails) {
      assertThat(errorDetails).contains(expectedViolationErrorDetail);
    }
  }

  private ConstraintViolation<Command> createMockedViolation(String path, String message) {
    var violation = mock(ConstraintViolation.class);
    when(violation.getPropertyPath()).thenReturn(TestPathImpl.create(path));
    when(violation.getMessage()).thenReturn(message);
    return violation;
  }

  private static class TestPathImpl {
    public static Path create(String propertyName) {
      return mock(
          Path.class,
          invocation -> {
            if (invocation.getMethod().getName().equals("toString")) {
              return propertyName;
            }
            return invocation.getMock();
          });
    }
  }
}
