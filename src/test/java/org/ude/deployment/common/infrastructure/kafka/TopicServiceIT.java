package org.ude.deployment.common.infrastructure.kafka;

import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateTopicsRequest;

@QuarkusTest
public class TopicServiceIT extends BaseHandlerIntegrationTest {

  @Inject TopicService topicService;

  @DisplayName("Given an existing topic, when delete is called twice, then no exception is thrown")
  @Test
  void givenExistingTopics_whenDeleteIsCalledTwice_thenNoExceptionIsThrown() {

    var dataOrderId = UUID.randomUUID().toString();
    var topicCreationResult =
        topicService.processDataOrderTopicsCreation(new CreateTopicsRequest(dataOrderId, 1, 1));

    var topics = topicCreationResult.await().indefinitely();

    // call delete twice
    topicService
        .deleteTopics(topics)
        .chain(() -> topicService.deleteTopics(topics))
        .invoke(() -> Assertions.assertTrue(true, "Calling delete twice does not cause error"))
        .subscribeAsCompletionStage()
        .toCompletableFuture()
        .join();
  }
}
