package org.ude.deployment.common.infrastructure.kafka;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.eclipse.microprofile.reactive.messaging.Message;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.ude.deployment.common.CommandRouter;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;

@QuarkusTest
class CommandConsumerTest {

  @InjectMock private CommandRouter commandRouter;

  @Inject CommandConsumer commandConsumer;

  @DisplayName("Given a command, when it is consumed, then it is routed.")
  @Test
  @SuppressWarnings("unchecked")
  void givenACommand_whenConsume_thenRoute() {
    // Arrange
    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            UUID.randomUUID(),
            ConfigurationType.DEPARTMENT,
            new JsonObject());
    var commandMessage = (Message<Command>) mock(Message.class);
    when(commandMessage.getPayload()).thenReturn(command);
    when(commandRouter.route(any(Command.class)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    commandConsumer.consumeCommand(commandMessage).await().indefinitely();

    // Assert
    var argumentCaptor = ArgumentCaptor.forClass(Command.class);
    verify(commandRouter).route(argumentCaptor.capture());
    var capturedCommand = argumentCaptor.getValue();
    assertThat(capturedCommand).isEqualTo(command);
    verify(commandMessage).ack();
  }

  @DisplayName(
      "Given a command which is null, when it is consumed, then it is not routed, but"
          + " acknowledged.")
  @Test
  @SuppressWarnings("unchecked")
  void givenANullCommand_whenConsume_thenNotRoute() {
    // Arrange
    var commandMessage = (Message<Command>) mock(Message.class);
    when(commandMessage.getPayload()).thenReturn(null);
    when(commandRouter.route(any(Command.class)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    var result = commandConsumer.consumeCommand(commandMessage).await().indefinitely();

    // Assert
    verify(commandRouter, never()).route(any(Command.class));
    verify(commandMessage).ack();
    assertThat(result).isNull();
  }
}
