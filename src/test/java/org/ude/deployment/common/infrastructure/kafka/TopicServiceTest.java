package org.ude.deployment.common.infrastructure.kafka;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import io.smallrye.mutiny.Uni;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AlterConfigsResult;
import org.apache.kafka.clients.admin.CreatePartitionsResult;
import org.apache.kafka.clients.admin.CreateTopicsResult;
import org.apache.kafka.clients.admin.DescribeTopicsResult;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.common.KafkaFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.ude.deployment.common.infrastructure.confluent.ConfluentClusterClient;
import org.ude.deployment.common.infrastructure.confluent.ConfluentIdentityProvidersService;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateTopicsRequest;

@ExtendWith(MockitoExtension.class)
class TopicServiceTest {

  @Mock private KafkaAdminClientProvider kafkaAdminClientProvider;

  @Mock private KafkaConfig kafkaConfig;

  @Mock private AdminClient adminClient;

  @Mock private CreateTopicsResult createTopicsResult;

  @Mock private DescribeTopicsResult describeTopicsResult;

  @Mock private CreatePartitionsResult createPartitionsResult;

  @Mock private AlterConfigsResult alterConfigsResult;

  @Mock private ConfluentIdentityProvidersService confluentIdentityProvidersService;

  @Mock private ConfluentClusterClient confluentClusterClient;

  @Mock private KafkaSchemaRegistryClient kafkaSchemaRegistryClient;

  private TopicService topicService;

  @BeforeEach
  void setUp() {
    topicService =
        new TopicService(
            kafkaAdminClientProvider,
            kafkaConfig,
            confluentIdentityProvidersService,
            confluentClusterClient,
            kafkaSchemaRegistryClient);
  }

  @DisplayName(
      "Given CreateTopicsRequest  object, when createTopic is called, then createTopics is"
          + " called")
  @Test
  void givenValidCreateTopicsRequest_whenCreateTopicIsCalled_thenMatchingTopicIsCreated() {
    // Given

    CreateTopicsRequest request = new CreateTopicsRequest("test-id", 3, 2);
    KafkaFuture<Void> future = KafkaFuture.completedFuture(null);

    when(adminClient.createTopics(any())).thenReturn(createTopicsResult);
    when(createTopicsResult.all()).thenReturn(future);
    when(kafkaAdminClientProvider.getAdmin()).thenReturn(adminClient);

    // When
    var result = topicService.processDataOrderTopicsCreation(request);

    // Then
    result
        .subscribe()
        .with(
            success -> verify(adminClient, times(1)).createTopics(any()),
            failure -> fail("Should not fail"));
  }

  @DisplayName(
      "Given a list of topics and new partition count, when updating partitions, then"
          + " triggers admin client update operation")
  @Test
  void
      givenUpdatePartitionRequestForAListOfTopics_whenUpdatePartitionCountIsCalled_thenUpdateCallIsTriggered() {
    // Given
    var topicNames = List.of("topic1", "topic2");
    var newPartitionCount = 5;
    KafkaFuture<Void> future = KafkaFuture.completedFuture(null);

    when(adminClient.createPartitions(any())).thenReturn(createPartitionsResult);
    when(createPartitionsResult.all()).thenReturn(future);
    when(kafkaAdminClientProvider.getAdmin()).thenReturn(adminClient);

    // When
    var result = topicService.updatePartitionCountForTopics(topicNames, newPartitionCount);

    // Then
    result
        .subscribe()
        .with(
            success ->
                verify(adminClient, times(1))
                    .createPartitions(
                        argThat(
                            map ->
                                map.size() == topicNames.size()
                                    && map.values().stream()
                                        .allMatch(p -> p.totalCount() == newPartitionCount))),
            failure -> fail("Should not fail"));
  }

  @DisplayName(
      "Given retention period in days, when converting to milliseconds, then returns correct"
          + " milliseconds value (days * 24 * 60 * 60 * 1000)")
  @Test
  void givenRetentionDaysInDays_whenRetentionInDaysToMsIsCalled_thenCorrectMsIsReturned() {
    // Given
    int retentionDays = 7;

    // When
    Long result = topicService.retentionInDaysToMs(retentionDays);

    // Then
    assertThat(result).isEqualTo(7 * 24 * 60 * 60 * 1000);
  }

  @DisplayName(
      "Given a list of topic names, when requesting topic descriptions, then returns"
          + " corresponding topic details")
  @Test
  void givenAListOfTopics_whenGetTopicDescriptionIsCalled_thenTopicDescriptionIsReturned() {
    // Given
    var topicNames = List.of("topic1", "topic2");
    var descriptionMap = new HashMap<String, TopicDescription>();
    KafkaFuture<Map<String, TopicDescription>> future = KafkaFuture.completedFuture(descriptionMap);

    when(adminClient.describeTopics(topicNames)).thenReturn(describeTopicsResult);
    when(describeTopicsResult.allTopicNames()).thenReturn(future);
    when(kafkaAdminClientProvider.getAdmin()).thenReturn(adminClient);

    // When
    Uni<List<TopicDescription>> result = topicService.getTopicDescriptions(topicNames);

    // Then
    result
        .subscribe()
        .with(
            descriptions -> {
              verify(adminClient, times(1)).describeTopics(topicNames);
              assertThat(descriptions).isEmpty();
            },
            failure -> fail("Should not fail"));
  }
}
