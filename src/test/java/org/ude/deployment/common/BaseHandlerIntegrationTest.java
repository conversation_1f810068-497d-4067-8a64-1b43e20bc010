package org.ude.deployment.common;

import io.quarkus.kafka.client.serialization.ObjectMapperSerde;
import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.kafka.InjectKafkaCompanion;
import io.quarkus.test.kafka.KafkaCompanionResource;
import io.smallrye.reactive.messaging.kafka.companion.KafkaCompanion;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.util.KafkaUtils;

@QuarkusTestResource(KafkaCompanionResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class BaseHandlerIntegrationTest {
  @InjectKafkaCompanion protected KafkaCompanion kafkaCompanion;

  @ConfigProperty(name = "mp.messaging.incoming.commands.topic")
  protected String commandsTopic;

  @ConfigProperty(name = "mp.messaging.outgoing.status.topic")
  protected String statusTopic;

  @ConfigProperty(name = "mp.messaging.outgoing.dead-letter.topic")
  protected String deadLetterTopic;

  @BeforeAll
  public final void baseRegisterSerdes() {
    kafkaCompanion.registerSerde(Command.class, new ObjectMapperSerde<>(Command.class));
    kafkaCompanion.registerSerde(Status.class, new ObjectMapperSerde<>(Status.class));
  }

  @AfterEach
  public final void baseCleanTopics() {
    List.of(statusTopic, commandsTopic, deadLetterTopic)
        .forEach(t -> KafkaUtils.clearTopic(kafkaCompanion, t));
  }

  protected CompletableFuture<ConsumerRecord<String, Status>> commandExecuteAndAwaitStatus(
      final Command cmd) {
    return commandExecuteAndAwaitNStatuses(cmd, 1).thenApply(List::getLast);
  }

  // do NOT remove the async nature
  @SneakyThrows(InterruptedException.class)
  protected CompletableFuture<List<ConsumerRecord<String, Status>>> commandExecuteAndAwaitNStatuses(
      final Command cmd, final int numberOfStatuses) {
    this.kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(this.commandsTopic, cmd.commandId(), cmd))
        .awaitCompletion();
    TimeUnit.SECONDS.sleep(1); // TODO: replace with awaitility (checking the topic)
    this.kafkaCompanion.topics().waitForTopic(this.statusTopic).await().indefinitely();
    return CompletableFuture.supplyAsync(
        () ->
            KafkaUtils.getLastNStatusMessages(
                this.kafkaCompanion, this.statusTopic, numberOfStatuses));
  }
}
