package org.ude.deployment.common;

import static org.assertj.core.api.Assertions.assertThat;

import com.github.tomakehurst.wiremock.WireMockServer;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.wiremock.InjectWireMock;
import org.ude.deployment.common.wiremock.WiremockResource;
import org.ude.deployment.dao.CreateDaoHandler;
import org.ude.deployment.project.CreateProjectHandler;
import org.ude.deployment.schema.CreateSchemaHandler;

@QuarkusTest
@QuarkusTestResource(WiremockResource.class)
class CommandHandlerFactoryTest {

  @InjectMock private CreateProjectHandler createProjectHandler;

  @Inject CommandHandlerFactory commandHandlerFactory;

  @InjectWireMock WireMockServer wiremock;

  @DisplayName(
      "Given Department configurationType and commandType, when getHandler, then return"
          + " appropriate commandHandler.")
  @Test
  void givenDepartmentConfigurationTypeAndCommandType_whenGetHandler_thenReturnCommandHandler() {
    // Arrange
    // Act
    var handler =
        commandHandlerFactory.getHandler(ConfigurationType.DEPARTMENT, CommandType.CREATE);

    // Assert
    assertThat(handler.isPresent()).isTrue();
    var commandHandler = handler.get();
    assertThat(commandHandler).isNotNull();
    assertThat(commandHandler).isInstanceOf(CreateProjectHandler.class);
  }

  @DisplayName(
      "Given Schema configurationType and commandType, when getHandler, then return"
          + " appropriate commandHandler.")
  @Test
  void givenSchemaConfigurationTypeAndCommandType_whenGetHandler_thenReturnCommandHandler() {
    // Arrange
    // Act
    var handler = commandHandlerFactory.getHandler(ConfigurationType.SCHEMA, CommandType.CREATE);

    // Assert
    assertThat(handler).isPresent();
    var commandHandler = handler.get();
    assertThat(commandHandler).isNotNull();
    assertThat(commandHandler).isInstanceOf(CreateSchemaHandler.class);
  }

  @DisplayName(
      "Given DAO configurationType and CREATE commandType, when getHandler, then return"
          + " appropriate commandHandler.")
  @Test
  void givenDaoCreateCommand_whenGetHandler_thenReturnCommandHandler() {
    // Arrange
    // Act
    var handler =
        commandHandlerFactory.getHandler(ConfigurationType.DATA_ACCESS_OBJECT, CommandType.CREATE);
    // Assert
    assertThat(handler).isPresent();
    var commandHandler = handler.get();
    assertThat(commandHandler).isNotNull();
    assertThat(commandHandler).isInstanceOf(CreateDaoHandler.class);
  }

  @DisplayName(
      "Given an invalid combination of configurationType and commandType, "
          + "when getHandler, then return empty Optional.")
  @Test
  void
      givenInvalidCombinationOfConfigurationTypeAndCommandType_whenGetHandler_thenReturnEmptyOptional() {
    // Arrange
    var configurationType = ConfigurationType.DEPARTMENT;
    var invalidCommandType = CommandType.UPDATE;

    // Act
    var handler = commandHandlerFactory.getHandler(configurationType, invalidCommandType);

    // Assert
    assertThat(handler).isEmpty();
  }
}
