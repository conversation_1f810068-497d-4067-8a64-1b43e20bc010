package org.ude.deployment.common;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.dao.CreateDaoHandler;
import org.ude.deployment.dao.dto.DataAccessObject;
import org.ude.deployment.project.CreateProjectHandler;
import org.ude.deployment.schema.CreateSchemaHandler;

@QuarkusTest
public class CommandRouterTest {

  @InjectMock private CommandHandlerFactory commandHandlerFactory;

  @InjectMock private CreateProjectHandler createProjectHandler;

  @InjectMock private CreateSchemaHandler createSchemaHandler;

  @InjectMock private CreateDaoHandler createDaoHandler;

  @Inject private CommandRouter commandRouter;

  @DisplayName(
      "Given a DEPARTMENT create command, when it is routed, then the handler is called with"
          + " the command")
  @Test
  public void givenProjectCreateCommand_whenRoute_thenHandlerIsCalledWithTheCommand() {
    // Arrange
    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            UUID.randomUUID(),
            ConfigurationType.DEPARTMENT,
            null);
    when(commandHandlerFactory.getHandler(command.type(), command.commandType()))
        .thenReturn(Optional.of(createProjectHandler));
    when(createProjectHandler.handle(any(Command.class)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    var result = commandRouter.route(command);

    // Assert
    var argumentCaptor = ArgumentCaptor.forClass(Command.class);
    verify(createProjectHandler).handle(argumentCaptor.capture());
    var capturedCommand = argumentCaptor.getValue();
    verify(commandHandlerFactory).getHandler(capturedCommand.type(), capturedCommand.commandType());
    assertThat(capturedCommand).isEqualTo(command);
    assertThat(result).isNotNull();
    assertThat(result.toCompletableFuture()).isDone();
  }

  @DisplayName(
      "Given a SCHEMA create command, when it is routed, then the handler is called with the"
          + " command")
  @Test
  public void givenSchemaCreatedCommand_whenRoute_thenHandlerIsCalledWithTheCommand() {
    // Arrange
    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            UUID.randomUUID(),
            ConfigurationType.SCHEMA,
            null);
    when(commandHandlerFactory.getHandler(command.type(), command.commandType()))
        .thenReturn(Optional.of(createSchemaHandler));
    when(createSchemaHandler.handle(any(Command.class)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    var result = commandRouter.route(command);

    // Assert
    var argumentCaptor = ArgumentCaptor.forClass(Command.class);
    verify(createSchemaHandler).handle(argumentCaptor.capture());
    var capturedCommand = argumentCaptor.getValue();
    verify(commandHandlerFactory).getHandler(capturedCommand.type(), capturedCommand.commandType());
    assertThat(capturedCommand).isEqualTo(command);
    assertThat(result).isNotNull();
    assertThat(result.toCompletableFuture()).isDone();
  }

  @DisplayName(
      "Given a DATA_ACCESS_OBJECT create command, when it is routed, then the handler is"
          + " called with the command")
  @Test
  public void givenDaoCreatedCommand_whenRoute_thenHandlerIsCalledWithTheCommand() {
    // Arrange
    var dao =
        new DataAccessObject(
            UUID.randomUUID(),
            "dao.name",
            "dao.description",
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            UUID.randomUUID(),
            Set.of());

    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            UUID.randomUUID(),
            ConfigurationType.DATA_ACCESS_OBJECT,
            JsonObject.mapFrom(dao));

    when(commandHandlerFactory.getHandler(command.type(), command.commandType()))
        .thenReturn(Optional.of(createDaoHandler));
    when(createDaoHandler.handle(any(Command.class)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    var result = commandRouter.route(command);

    // Assert
    var argumentCaptor = ArgumentCaptor.forClass(Command.class);
    verify(createDaoHandler).handle(argumentCaptor.capture());
    var capturedCommand = argumentCaptor.getValue();
    verify(commandHandlerFactory).getHandler(capturedCommand.type(), capturedCommand.commandType());
    assertThat(capturedCommand).isEqualTo(command);
    assertThat(result).isNotNull();
    assertThat(result.toCompletableFuture()).isDone();
  }
}
