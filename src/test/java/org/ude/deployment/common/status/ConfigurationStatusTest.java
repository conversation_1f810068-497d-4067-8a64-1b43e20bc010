package org.ude.deployment.common.status;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.ude.deployment.common.command.CommandType;

class ConfigurationStatusTest {

  @DisplayName(
      "Given a request job status, when determining configuration status, then the correct configuration status is returned.")
  @ParameterizedTest
  @EnumSource(value = RequestJobStatus.class)
  void
      givenRequestJobStatus_whenDetermineConfigurationStatus_thenCorrectConfigurationStatusIsReturned(
          RequestJobStatus input) {
    var actualConfigurationStatus =
        JobStateUtils.determineConfigurationStatus(
            input,
            input.equals(RequestJobStatus.DELETED) ? CommandType.DELETE : CommandType.CREATE);

    var statusMapping =
        Map.of(
            RequestJobStatus.FAILED, ConfigurationStatus.DISABLED,
            RequestJobStatus.COMPLETED, ConfigurationStatus.ACTIVE,
            RequestJobStatus.DELETED, ConfigurationStatus.DELETED,
            RequestJobStatus.PARTIALLY_FAILED, ConfigurationStatus.DISABLED);

    assertThat(actualConfigurationStatus)
        .isEqualTo(statusMapping.getOrDefault(input, ConfigurationStatus.IN_PROVISIONING));
  }

  @DisplayName(
      "Given a configuration status string, when deserializing, then the correct configuration status is returned.")
  @ParameterizedTest(
      name = "fromValue(\"{0}\") should return {1}, and toString() should return \"{0}\"")
  @CsvSource({
    "InProvisioning, IN_PROVISIONING",
    "Active, ACTIVE",
    "Disabled, DISABLED",
    "Deleted, DELETED"
  })
  void givenConfigurationStatusStrings_whenDeserializing_thenCorrectConfigurationStatusIsReturned(
      String input, ConfigurationStatus expectedEnum) {
    assertThat(ConfigurationStatus.fromValue(input)).isEqualTo(expectedEnum);
    assertThat(expectedEnum.toString()).isEqualTo(input);
  }
}
