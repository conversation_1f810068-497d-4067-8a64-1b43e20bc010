package org.ude.deployment.common.status;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;

class ProvisioningStepDeserializerTest {
  private ObjectMapper objectMapper;

  @BeforeEach
  public void setUp() {
    objectMapper = new ObjectMapper();
  }

  @DisplayName(
      "Given the default space-separated provisioning step string, when deserializing, then the correct enum value is returned.")
  @ParameterizedTest(
      name = "fromValue(\"{0}\") should return {1}, and toString() should return \"{0}\"")
  @CsvSource({
    "Object Registration, OBJECT_REGISTRATION",
    "Object Registration, OBJECT_REGISTRATION",
    "Security Group Provisioning, SECURITY_GROUP_PROVISIONING",
    "Kafka Topic Provisioning, KAFKA_TOPIC_PROVISIONING"
  })
  void givenDefaultSpaceSeparatedProvisioningStep_whenDeserializing_thenCorrectEnumIsReturned(
      String input, ProvisioningStep expectedEnum) throws Exception {
    var actual = objectMapper.readValue("\"" + input + "\"", ProvisioningStep.class);

    assertThat(actual).isEqualTo(expectedEnum);
    assertThat(actual.toString()).isEqualTo(input);
  }

  @DisplayName(
      "Given a PascalCased provisioning step string, when deserializing, then the correct enum value is returned.")
  @ParameterizedTest(
      name = "fromValue(\"{0}\") should return {1}, and toString() should return \"{0}\"")
  @CsvSource({
    "ObjectRegistration, OBJECT_REGISTRATION",
    "SecurityGroupProvisioning, SECURITY_GROUP_PROVISIONING",
    "KafkaTopicProvisioning, KAFKA_TOPIC_PROVISIONING"
  })
  void givenPascalCasedProvisioningStep_whenDeserializing_thenCorrectEnumIsReturned(
      String input, ProvisioningStep expectedEnum) throws Exception {
    var actual = objectMapper.readValue("\"" + input + "\"", ProvisioningStep.class);

    assertThat(actual).isEqualTo(expectedEnum);
    assertThat(actual.toString()).isEqualTo(input.replaceAll("([a-z])([A-Z])", "$1 $2"));
  }

  @DisplayName(
      "Given an invalid provisioning step string, when deserializing, then an exception is thrown.")
  @Test
  void givenInvalidProvisioningStep_whenDeserializing_thenExceptionIsThrown() {
    var invalidStep = "\"InvalidStep\"";

    var exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> objectMapper.readValue(invalidStep, ProvisioningStep.class));

    assertThat(exception.getMessage()).contains("Unknown provisioning step");
  }
}
