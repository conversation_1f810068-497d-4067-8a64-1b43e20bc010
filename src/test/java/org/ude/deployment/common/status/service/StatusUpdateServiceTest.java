package org.ude.deployment.common.status.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.vertx.core.json.JsonObject;
import jakarta.inject.Inject;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.StatusProducer;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;

@QuarkusTest
public class StatusUpdateServiceTest {

  @InjectMock private StatusProducer statusProducer;

  @Inject private StatusUpdateService statusUpdateService;

  @DisplayName(
      "Given a command, when its provisioning status changes, then a status update is sent.")
  @Test
  public void givenACommand_whenUpdateStatus_thenSendProvisioningStatusUpdate() {
    // Arrange
    var command =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.CREATE,
            UUID.randomUUID(),
            ConfigurationType.DEPARTMENT,
            new JsonObject().put("body", "sample Department body"));

    when(statusProducer.sendStatusUpdate(any(Status.class)))
        .thenReturn(CompletableFuture.completedFuture(null));

    // Act
    var result = statusUpdateService.updateProvisioningStatus(command, RequestJobStatus.COMPLETED);

    // Assert
    var argumentCaptor = ArgumentCaptor.forClass(Status.class);
    verify(statusProducer).sendStatusUpdate(argumentCaptor.capture());
    var capturedStatus = argumentCaptor.getValue();
    assertThat(capturedStatus)
        .satisfies(
            statusUpdate -> {
              assertThat(statusUpdate.commandId()).isEqualTo(command.commandId());
              assertThat(statusUpdate.commandType()).isEqualTo(command.commandType());
              assertThat(statusUpdate.status()).isEqualTo(RequestJobStatus.COMPLETED);
              assertThat(statusUpdate.objectId()).isEqualTo(command.objectId());
              assertThat(statusUpdate.type()).isEqualTo(command.type());
              assertThat(statusUpdate.properties()).isEqualTo(command.properties());
            });

    assertThat(result).isNotNull();
    assertThat(result.toCompletableFuture().isDone()).isTrue();
  }
}
