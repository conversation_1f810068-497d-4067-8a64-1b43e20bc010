package org.ude.deployment.common.status;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

class RequestJobStatusTest {
  private final ObjectMapper objectMapper = new ObjectMapper();

  @DisplayName(
      "Given a RequestJobStatus enum, when serializing with <PERSON>, then the value string is produced")
  @ParameterizedTest(name = "Serializing {0} should produce \"{1}\"")
  @CsvSource({
    "SUBMITTED, \"Submitted\"",
    "PROVISIONING, \"Provisioning\"",
    "FAILED, \"Failed\"",
    "PARTIALLY_FAILED, \"Partially Failed\"",
    "COMPLETED, \"Completed\"",
    "DELETING, \"Deleting\"",
    "DELETED, \"Deleted\"",
    "IN_PROGRESS, \"InProgress\"",
    "CANCELLED, \"Cancelled\""
  })
  void givenRequestJobStatusEnum_whenSerializing_thenValueStringProduced(
      RequestJobStatus status, String expectedStatusValue) throws Exception {
    var actualStatusValue = objectMapper.writeValueAsString(status);
    assertThat(actualStatusValue).isEqualTo(expectedStatusValue);
  }
}
