package org.ude.deployment.common.status;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStepType;

class ProvisioningStepTypeDeserializerTest {
  private ObjectMapper objectMapper;

  @BeforeEach
  public void setUp() {
    objectMapper = new ObjectMapper();
  }

  @DisplayName(
      "Given the default space-separated provisioning step type string, when deserializing, then the correct enum value is returned.")
  @ParameterizedTest(
      name = "fromValue(\"{0}\") should return {1}, and toString() should return \"{0}\"")
  @CsvSource({
    "Postgres DB Object, POSTGRES_DB_OBJECT",
    "Repository, REPOSITORY",
    "InternalRepository, INTERNAL_REPOSITORY",
    "Workbench, WORKBENCH",
    "StreamingHost, STREAMING_HOST",
    "BatchPipeline, BATCH_PIPELINE",
    "Pipeline Channel, PIPELINE_CHANNEL",
    "SecurityGroup, SECURITY_GROUP",
    "Department, DEPARTMENT",
    "SchemaRegistry, SCHEMA_REGISTRY"
  })
  void givenDefaultProvisioningStepType_whenDeserializing_thenCorrectEnumIsReturned(
      String input, ProvisioningStepType expectedEnum) throws Exception {
    var actual = objectMapper.readValue("\"" + input + "\"", ProvisioningStepType.class);

    assertThat(actual).isEqualTo(expectedEnum);
    assertThat(actual.toString()).isEqualTo(input);
  }

  @DisplayName(
      "Given a PascalCased provisioning step type string, when deserializing, then the correct enum value is returned.")
  @ParameterizedTest(
      name = "fromValue(\"{0}\") should return {1}, and toString() should return \"{2}\"")
  @CsvSource({
    "PostgresDBObject, POSTGRES_DB_OBJECT, Postgres DB Object",
    "InternalRepository, INTERNAL_REPOSITORY, InternalRepository",
    "StreamingHost, STREAMING_HOST, StreamingHost",
    "BatchPipeline, BATCH_PIPELINE, BatchPipeline",
    "PipelineChannel, PIPELINE_CHANNEL, Pipeline Channel",
    "SecurityGroup, SECURITY_GROUP, SecurityGroup",
    "SchemaRegistry, SCHEMA_REGISTRY, SchemaRegistry"
  })
  void givenPascalCasedProvisioningStepType_whenDeserializing_thenCorrectEnumIsReturned(
      String input, ProvisioningStepType expectedEnum, String expectedToString) throws Exception {
    var actual = objectMapper.readValue("\"" + input + "\"", ProvisioningStepType.class);

    assertThat(actual).isEqualTo(expectedEnum);
    assertThat(actual.toString()).isEqualTo(expectedToString);
  }

  @DisplayName(
      "Given an invalid provisioning step type string, when deserializing, then an exception is thrown.")
  @Test
  void givenInvalidProvisioningStepType_whenDeserializing_thenExceptionIsThrown() {
    var invalidStepType = "\"InvalidType\"";

    var exception =
        assertThrows(
            IllegalArgumentException.class,
            () -> objectMapper.readValue(invalidStepType, ProvisioningStepType.class));

    assertThat(exception.getMessage()).contains("Unknown provisioning step type: InvalidType");
  }
}
