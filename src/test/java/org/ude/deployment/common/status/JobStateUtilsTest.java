package org.ude.deployment.common.status;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.OffsetDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStepType;

class JobStateUtilsTest {
  private DataPlatformObjectStatus firstDPOStatus;
  private DataPlatformObjectStatus secondDPOStatus;

  @BeforeEach
  void setup() {
    var provisioningStep = ProvisioningStep.OBJECT_REGISTRATION;
    firstDPOStatus =
        new DataPlatformObjectStatus(
            provisioningStep,
            "id1",
            ProvisioningStepType.POSTGRES_DB_OBJECT,
            ProvisioningStatus.FAILED,
            OffsetDateTime.now(),
            "msg",
            null);
    secondDPOStatus =
        new DataPlatformObjectStatus(
            provisioningStep,
            "id2",
            ProvisioningStepType.POSTGRES_DB_OBJECT,
            ProvisioningStatus.COMPLETED,
            OffsetDateTime.now(),
            "msg",
            null);
  }

  @Nested
  class MergeDataPlatformObjectStatusTests {
    @Test
    @DisplayName(
        "Given existing dpoStatuses, when merging with new dpoStatuses, then existing dpoStatuses of the same type are overwritten.")
    void givenExistingSteps_whenMergingWithNewSteps_thenExistingStepsAreOverwritten() {
      // Arrange
      var existing = List.of(firstDPOStatus);
      var newDpoStatuses = List.of(secondDPOStatus);

      // Act
      var merged = JobStateUtils.mergeDataPlatformObjectStatus(existing, newDpoStatuses);

      // Assert
      assertThat(merged.size()).isEqualTo(1);
      assertThat(merged).contains(secondDPOStatus);
      assertThat(merged).doesNotContain(firstDPOStatus);
    }

    @Test
    @DisplayName(
        "Given existing dpoStatuses, when merging with new dpoStatuses, then new dpoStatuses are added")
    void givenExistingSteps_whenMergingWithNewSteps_thenNewStepsAreAdded() {
      // Arrange
      var newStepType = ProvisioningStep.KAFKA_TOPIC_PROVISIONING;
      var topicDpoStatus =
          new DataPlatformObjectStatus(
              newStepType,
              "id3",
              ProvisioningStepType.REPOSITORY,
              ProvisioningStatus.PROVISIONING,
              OffsetDateTime.now(),
              "Creating topic",
              null);

      // Act
      var merged =
          JobStateUtils.mergeDataPlatformObjectStatus(
              List.of(firstDPOStatus), List.of(topicDpoStatus));

      // Assert
      assertThat(merged.size()).isEqualTo(2);
      assertThat(merged).contains(topicDpoStatus);
      assertThat(merged).contains(firstDPOStatus);
    }

    @Test
    @DisplayName(
        "Given existing dpoStatuses, when merging with an empty list, then no statuses are added")
    void givenExistingSteps_whenMergingWithEmptyList_thenNoStatusesAreAdded() {
      // Arrange
      List<DataPlatformObjectStatus> emptyStatus = List.of();

      // Act
      var merged =
          JobStateUtils.mergeDataPlatformObjectStatus(List.of(firstDPOStatus), emptyStatus);

      // Assert
      assertThat(merged.size()).isEqualTo(1);
      assertThat(merged).contains(firstDPOStatus);
    }

    @Test
    @DisplayName(
        "Given existing dpoStatuses, when merging with a null list, then no statuses are added.")
    void givenExistingSteps_whenMergingWithNullList_thenNoStatusesAreAdded() {
      // Arrange
      List<DataPlatformObjectStatus> newEmptyStatuses = null;

      // Act
      var merged =
          JobStateUtils.mergeDataPlatformObjectStatus(List.of(firstDPOStatus), newEmptyStatuses);

      // Assert
      assertThat(merged.size()).isEqualTo(1);
      assertThat(merged).contains(firstDPOStatus);
    }
  }

  @Nested
  class CalculateJobStatusTests {
    @Test
    @DisplayName(
        "Given dpoStatuses, when calculating job status with partially failed, then status is partially failed")
    void givenSteps_whenCalculatingJobStatusWithPartiallyFailed_thenStatusIsPartiallyFailed() {
      var dpoStatuses =
          List.of(newStep(ProvisioningStatus.COMPLETED), newStep(ProvisioningStatus.FAILED));

      var status = JobStateUtils.calculateJobStatus(dpoStatuses, CommandType.CREATE);

      assertThat(status).isEqualTo(RequestJobStatus.PARTIALLY_FAILED);
    }

    @Test
    @DisplayName(
        "Given dpoStatuses, when calculating job status with all skipped, then status is completed")
    void givenSteps_whenCalculatingJobStatusWithAllSkipped_thenStatusIsCompleted() {
      var dpoStatuses =
          List.of(newStep(ProvisioningStatus.SKIPPED), newStep(ProvisioningStatus.SKIPPED));

      var requestJobStatus = JobStateUtils.calculateJobStatus(dpoStatuses, CommandType.CREATE);

      assertThat(requestJobStatus).isEqualTo(RequestJobStatus.COMPLETED);
    }

    @Test
    @DisplayName(
        "Given dpoStatuses, when calculating job status with mix of provisioning and deleting, then status is provisioning")
    void
        givenSteps_whenCalculatingJobStatusWithMixOfProvisioningAndDeleting_thenStatusIsProvisioning() {
      // Arrange
      var dpoStatuses =
          List.of(newStep(ProvisioningStatus.PROVISIONING), newStep(ProvisioningStatus.DELETING));

      // Act
      var requestJobStatus = JobStateUtils.calculateJobStatus(dpoStatuses, CommandType.CREATE);

      // Assert
      assertThat(requestJobStatus).isEqualTo(RequestJobStatus.PROVISIONING);
    }

    @Test
    @DisplayName(
        "Given dpoStatuses, when calculating job status with all completed, then the job status is completed")
    void givenSteps_whenCalculatingJobStatusWithAllCompleted_thenStatusIsCompleted() {
      // Arrange
      var dpoStatuses =
          List.of(newStep(ProvisioningStatus.COMPLETED), newStep(ProvisioningStatus.COMPLETED));

      // Act
      var requestJobStatus = JobStateUtils.calculateJobStatus(dpoStatuses, CommandType.CREATE);

      // Assert
      assertThat(requestJobStatus).isEqualTo(RequestJobStatus.COMPLETED);
    }

    @Test
    @DisplayName(
        "Given dpoStatuses, when calculating job status with all failed, then the job status is failed")
    void givenSteps_whenCalculatingJobStatusWithAllFailed_thenStatusIsFailed() {
      // Arrange
      var dpoStatuses =
          List.of(newStep(ProvisioningStatus.FAILED), newStep(ProvisioningStatus.FAILED));

      // Act
      var requestJobStatus = JobStateUtils.calculateJobStatus(dpoStatuses, CommandType.CREATE);

      // Assert
      assertThat(requestJobStatus).isEqualTo(RequestJobStatus.FAILED);
    }

    @Test
    @DisplayName(
        "Given an empty list of dpo statuses, when calculating job status, then the job status is completed")
    void givenAnEmptyListOfDpoStatuses_whenCalculatingJobStatus_thenStatusIsCompleted() {
      // Arrange
      List<DataPlatformObjectStatus> dpoStatuses = List.of();

      // Act
      var requestJobStatus = JobStateUtils.calculateJobStatus(dpoStatuses, CommandType.CREATE);

      // Assert
      assertThat(requestJobStatus).isEqualTo(RequestJobStatus.SUBMITTED);
    }

    @Test
    @DisplayName(
        "Given a null list of dpoStatuses, when calculating job status, then the job status is 'SUBMITTED'")
    void givenNullListOfDpoStatuses_whenCalculatingJobStatus_thenStatusIsCompleted() {
      // Arrange
      List<DataPlatformObjectStatus> dpoStatuses = null;

      // Act
      var requestJobStatus = JobStateUtils.calculateJobStatus(dpoStatuses, CommandType.CREATE);

      // Assert
      assertThat(requestJobStatus).isEqualTo(RequestJobStatus.SUBMITTED);
    }

    @Test
    @DisplayName(
        "Given dpoStatuses with a mix of COMPLETED and NOT_STARTED, when calculating job status, then the status is 'PROVISIONING'")
    void
        givenSteps_whenCalculatingJobStatusWithMixOfProvisioningAndNotStarted_thenStatusIsProvisioning() {
      // Arrange
      var dpoStatuses = List.of(secondDPOStatus, newStep(ProvisioningStatus.NOT_STARTED));

      // Act
      var requestJobStatus = JobStateUtils.calculateJobStatus(dpoStatuses, CommandType.CREATE);

      // Assert
      assertThat(requestJobStatus).isEqualTo(RequestJobStatus.PROVISIONING);
    }
  }

  @Nested
  class DetermineConfigurationStatusTests {
    @Test
    @DisplayName(
        "Given job status provisioning, when determining configuration status, then configuration status is in provisioning")
    void
        givenJobStatusProvisioning_whenDeterminingConfigurationStatus_thenConfigurationStatusIsInProvisioning() {
      // Arrange
      var jobStatus = RequestJobStatus.PROVISIONING;
      var commandType = CommandType.CREATE;

      // Act
      var configurationStatus = JobStateUtils.determineConfigurationStatus(jobStatus, commandType);

      // Assert
      assertThat(configurationStatus).isEqualTo(ConfigurationStatus.IN_PROVISIONING);
    }

    @Test
    @DisplayName(
        "Given job status completed, when determining configuration status, then configuration status is active")
    void
        givenJobStatusCompleted_whenDeterminingConfigurationStatus_thenConfigurationStatusIsActive() {
      // Arrange
      var completedJobStatus = RequestJobStatus.COMPLETED;
      var commandType = CommandType.CREATE;

      // Act
      var configurationStatus =
          JobStateUtils.determineConfigurationStatus(completedJobStatus, commandType);

      // Assert
      assertThat(configurationStatus).isEqualTo(ConfigurationStatus.ACTIVE);
    }

    @Test
    @DisplayName(
        "Given job status failed, when determining configuration status, then configuration status is disabled")
    void
        givenJobStatusFailed_whenDeterminingConfigurationStatus_thenConfigurationStatusIsDisabled() {
      // Arrange
      var failedJobStatus = RequestJobStatus.FAILED;
      var commandType = CommandType.CREATE;

      // Act
      var configurationStatus =
          JobStateUtils.determineConfigurationStatus(failedJobStatus, commandType);

      // Assert
      assertThat(configurationStatus).isEqualTo(ConfigurationStatus.DISABLED);
    }

    @Test
    @DisplayName(
        "Given job status deleted, when determining configuration status, then configuration status is deleted")
    void
        givenJobStatusDeleted_whenDeterminingConfigurationStatus_thenConfigurationStatusIsDeleted() {
      // Arrange
      var jobStatus = RequestJobStatus.DELETED;
      var commandType = CommandType.DELETE;

      // Act
      var configurationStatus = JobStateUtils.determineConfigurationStatus(jobStatus, commandType);

      // Assert
      assertThat(configurationStatus).isEqualTo(ConfigurationStatus.DELETED);
    }
  }

  @Nested
  class GetTargetJobStatusTests {
    @Test
    @DisplayName(
        "Given a current deployment type of 'CREATE', when getting target job status for the current deployment type, then the target job status is 'COMPLETED'")
    void
        givenDeploymentTypeIsCreate_whenGettingTargetJobStatus_thenTargetJobStatusIsProvisioning() {
      // Arrange
      var currentDeploymentType = CommandType.CREATE;

      // Act
      var targetJobStatus = JobStateUtils.getTargetJobStatus(currentDeploymentType);

      // Assert
      assertThat(targetJobStatus).isEqualTo(RequestJobStatus.COMPLETED);
    }

    @Test
    @DisplayName(
        "Given a current deployment type of 'DELETE', when getting target job status for the current deployment type, then the target job status is 'DELETED'")
    void givenDeploymentTypeIsDelete_whenGettingTargetJobStatus_thenTargetJobStatusIsDeleted() {
      // Arrange
      var currentDeploymentType = CommandType.DELETE;

      // Act
      var targetJobStatus = JobStateUtils.getTargetJobStatus(currentDeploymentType);

      // Assert
      assertThat(targetJobStatus).isEqualTo(RequestJobStatus.DELETED);
    }

    @Test
    @DisplayName(
        "Given a current deployment type which is null, when getting target job status for the current deployment type, then the target job status is 'DELETED'")
    void givenDeploymentTypeIsNull_whenGettingTargetJobStatus_thenTargetJobStatusIsDeleted() {
      // Arrange
      CommandType currentDeploymentType = null;

      // Act
      var targetJobStatus = JobStateUtils.getTargetJobStatus(currentDeploymentType);

      // Assert
      assertThat(targetJobStatus).isEqualTo(RequestJobStatus.DELETED);
    }
  }

  @Nested
  class JobStatusAccumulatorTests {
    @Test
    @DisplayName(
        "Given a status of FAILED, when accumulated, then 'hasFailed' is true and 'allCompleted' is false")
    void givenStatusFailed_whenAccumulated_thenHasFailedTrueAllCompletedFalse() {
      // Arrange
      var accumulator = new JobStateUtils.JobStatusAccumulator();
      var status =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.FAILED);

      // Act
      accumulator.accumulate(status);

      // Assert
      assertThat(accumulator.hasFailed).isTrue();
      assertThat(accumulator.allCompleted).isFalse();
    }

    @Test
    @DisplayName(
        "Given a status of COMPLETED, when accumulated, then 'someCompleted' is false and 'allCompleted' is true")
    void givenStatusCompleted_whenAccumulated_thenSomeCompletedTrue() {
      // Arrange
      var accumulator = new JobStateUtils.JobStatusAccumulator();
      var status =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.COMPLETED);

      // Act
      accumulator.accumulate(status);

      // Assert
      assertThat(accumulator.allCompleted).isTrue();
      assertThat(accumulator.someCompleted).isFalse();
    }

    @Test
    @DisplayName(
        "Given a status of DELETED, when accumulated, then 'allCompleted' is true while 'someCompleted' is false and isDeleting is false")
    void givenStatusDeleted_whenAccumulated_thenSomeCompletedTrue() {
      // Arrange
      var accumulator = new JobStateUtils.JobStatusAccumulator();
      var status =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.DELETED);

      // Act
      accumulator.accumulate(status);

      // Assert
      assertThat(accumulator.allCompleted).isTrue();
      assertThat(accumulator.someCompleted).isFalse();
      assertThat(accumulator.isDeleting).isFalse();
    }

    @Test
    @DisplayName(
        "Given a status of SKIPPED, when accumulated, then 'allCompleted' is true while 'someCompleted' is false")
    void givenStatusSkipped_whenAccumulated_thenSomeCompletedTrue() {
      // Arrange
      var accumulator = new JobStateUtils.JobStatusAccumulator();
      var status =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.SKIPPED);

      // Act
      accumulator.accumulate(status);

      // Assert
      assertThat(accumulator.allCompleted).isTrue();
      assertThat(accumulator.someCompleted).isFalse();
    }

    @Test
    @DisplayName(
        "Given a status of PROVISIONING, when accumulated, then 'isProvisioning' is true and 'allCompleted' is false")
    void givenStatusProvisioning_whenAccumulated_thenIsProvisioningTrueAllCompletedFalse() {
      // Arrange
      var accumulator = new JobStateUtils.JobStatusAccumulator();
      var status =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.PROVISIONING);

      // Act
      accumulator.accumulate(status);

      // Assert
      assertThat(accumulator.isProvisioning).isTrue();
      assertThat(accumulator.allCompleted).isFalse();
    }

    @Test
    @DisplayName(
        "Given a status of DELETING, when accumulated, then 'isDeleting' is true and 'allCompleted' is false")
    void givenStatusDeleting_whenAccumulated_thenIsDeletingTrueAllCompletedFalse() {
      // Arrange
      var accumulator = new JobStateUtils.JobStatusAccumulator();
      var status =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.DELETING);

      // Act
      accumulator.accumulate(status);

      // Assert
      assertThat(accumulator.isDeleting).isTrue();
      assertThat(accumulator.allCompleted).isFalse();
    }

    @Test
    @DisplayName(
        "Given a status of NOT_STARTED, when accumulated, then 'someHaveNotStarted' is true and 'allCompleted' is false")
    void givenStatusNotStarted_whenAccumulated_thenSomeHaveNotStartedTrueAllCompletedFalse() {
      // Arrange
      var accumulator = new JobStateUtils.JobStatusAccumulator();
      var status =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.NOT_STARTED);

      // Act
      accumulator.accumulate(status);

      // Assert
      assertThat(accumulator.someHaveNotStarted).isTrue();
      assertThat(accumulator.allCompleted).isFalse();
      assertThat(accumulator.someCompleted).isFalse();
      assertThat(accumulator.hasFailed).isFalse();
    }

    @Test
    @DisplayName(
        "Given multiple statuses including COMPLETED, FAILED, PROVISIONING, and DELETING, when accumulated, then all flags are set accordingly")
    void givenMultipleStatuses_whenAccumulated_thenAllFlagsSetCorrectly() {
      // Arrange
      var accumulator = new JobStateUtils.JobStatusAccumulator();
      var completed =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.COMPLETED);
      var failed =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.FAILED);
      var provisioning =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.PROVISIONING);
      var deleting =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.DELETING);
      var notStarted =
          DataPlatformObjectStatus.of(
              ProvisioningStep.OBJECT_REGISTRATION,
              ProvisioningStepType.POSTGRES_DB_OBJECT,
              ProvisioningStatus.NOT_STARTED);

      // Act
      accumulator.accumulate(completed);
      accumulator.accumulate(failed);
      accumulator.accumulate(provisioning);
      accumulator.accumulate(deleting);
      accumulator.accumulate(notStarted);

      // Assert
      assertThat(accumulator.hasFailed).isTrue();
      assertThat(accumulator.someCompleted).isFalse();
      assertThat(accumulator.isProvisioning).isTrue();
      assertThat(accumulator.someHaveNotStarted).isTrue();
      assertThat(accumulator.isDeleting).isTrue();
      assertThat(accumulator.allCompleted).isFalse();
    }

    @Test
    @DisplayName(
        "Given no statuses accumulated, when checking the accumulator, then all flags are set to false except 'allCompleted'")
    void givenNoStatusesAccumulated_whenCheckingAccumulator_thenFlagsSetToFalseAllCompletedTrue() {
      // Arrange / Act
      var accumulator = new JobStateUtils.JobStatusAccumulator();

      // Assert
      assertThat(accumulator.hasFailed).isFalse();
      assertThat(accumulator.allCompleted).isTrue();
      assertThat(accumulator.someCompleted).isFalse();
      assertThat(accumulator.isProvisioning).isFalse();
      assertThat(accumulator.someHaveNotStarted).isFalse();
      assertThat(accumulator.isDeleting).isFalse();
    }
  }

  private DataPlatformObjectStatus newStep(ProvisioningStatus status) {
    var step = ProvisioningStep.KAFKA_TOPIC_PROVISIONING;
    return new DataPlatformObjectStatus(
        step,
        "id",
        ProvisioningStepType.REPOSITORY,
        status,
        OffsetDateTime.now(),
        "Processing topic command",
        null);
  }
}
