package org.ude.deployment.common.status;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStepType;

class JobStatusContextTest {
  @Nested
  class FromCommandPropertiesTest {
    @Test
    @DisplayName(
        "Given a command with properties containing an empty list of DPO statuseses, when deserializing from command properties, then a context is created.")
    void
        givenCommandPropertiesContainingEmptyListOfDPOStatus_whenDeserializing_thenContextShouldBeCreated() {
      // Arrange
      var mockCommand = mock(Command.class);
      when(mockCommand.properties())
          .thenReturn(new JsonObject().put("dataPlatformObjectStatus", List.of()));

      // Act
      var context = JobStatusContext.fromCommandProperties(mockCommand);

      // Assert
      assertThat(context).isNotNull();
      assertThat(context.dpoStatuses()).isEmpty();
      assertThat(context.configurationStatus()).isEqualTo(ConfigurationStatus.IN_PROVISIONING);
      assertThat(context.requestJobStatus()).isEqualTo(RequestJobStatus.SUBMITTED);
    }

    @DisplayName(
        "Given a command with properties without DPO statuses, when deserializing from command properties, then context should be created with default status properties.")
    @Test
    void
        givenCommandPropertiesWithoutDPOStatus_whenDeserializing_thenContextShouldBeCreatedWithDefaultStatusProperties() {
      // Arrange
      var mockCommand = mock(Command.class);
      when(mockCommand.properties()).thenReturn(new JsonObject());

      // Act
      var context = JobStatusContext.fromCommandProperties(mockCommand);

      // Assert
      assertThat(context).isNotNull();
      assertThat(context.dpoStatuses()).isEmpty();
      assertThat(context.configurationStatus()).isEqualTo(ConfigurationStatus.IN_PROVISIONING);
      assertThat(context.requestJobStatus()).isEqualTo(RequestJobStatus.SUBMITTED);
    }

    @Test
    @DisplayName(
        "Given a command with data, when requesting a status context from command properties, then context should reflect correct statuses with a default request job status")
    void
        givenCommandPropertiesWithDPOStatus_whenDeserializing_thenContextShouldBeCreatedWithDefaultRequestJobStatus() {
      // Arrange
      var mockCommand = mock(Command.class);
      var mockSteps =
          List.of(
              new DataPlatformObjectStatus(
                  ProvisioningStep.OBJECT_REGISTRATION,
                  "id1",
                  ProvisioningStepType.POSTGRES_DB_OBJECT,
                  ProvisioningStatus.COMPLETED,
                  OffsetDateTime.now().withOffsetSameInstant(ZoneOffset.UTC),
                  "",
                  null));

      when(mockCommand.properties())
          .thenReturn(
              new JsonObject()
                  .put("dataPlatformObjectStatus", new JsonArray(mockSteps))
                  .put("configurationStatus", ConfigurationStatus.IN_PROVISIONING));

      // Act
      var context = JobStatusContext.fromCommandProperties(mockCommand);

      // Assert
      assertThat(context.dpoStatuses()).isEqualTo(mockSteps);
      assertThat(context.configurationStatus()).isEqualTo(ConfigurationStatus.IN_PROVISIONING);
      assertThat(context.requestJobStatus()).isEqualTo(RequestJobStatus.SUBMITTED);
    }
  }

  @Nested
  class SaveToCommandTest {
    @Test
    @DisplayName(
        "Given a context with DPO statuses, when saving the status context to a command, then properties should be updated with data platform object status and configuration status.")
    void givenContext_whenSavingToCommand_thenPropertiesShouldBeUpdated() {
      // Arrange
      var mockCommand = mock(Command.class);
      var steps = List.of(newStep(ProvisioningStatus.PROVISIONING));
      var context =
          new JobStatusContext(steps, ConfigurationStatus.ACTIVE, RequestJobStatus.COMPLETED);
      var properties = mock(JsonObject.class);
      when(mockCommand.properties()).thenReturn(properties);

      // Act
      context.saveToCommand(mockCommand);

      // Assert
      verify(mockCommand.properties(), times(1)).put(eq("dataPlatformObjectStatus"), eq(steps));
      verify(mockCommand.properties(), times(1))
          .put(eq("configurationStatus"), eq(ConfigurationStatus.ACTIVE));
    }

    @Test
    @DisplayName(
        "Given a context with dpoStatuses and a configuration status, when saving to a command, then properties should be preserved and only two put calls should be made.")
    void saveToCommand_PreservesContextOnSave() {
      // Arrange
      var mockCommand = mock(Command.class);
      var dpoStatus = List.of(newStep(ProvisioningStatus.FAILED));
      var context =
          new JobStatusContext(dpoStatus, ConfigurationStatus.DISABLED, RequestJobStatus.FAILED);
      var properties = mock(JsonObject.class);
      when(mockCommand.properties()).thenReturn(properties);

      // Act
      context.saveToCommand(mockCommand);

      // Assert
      verify(mockCommand.properties(), times(2)).put(any(), any());
      verify(mockCommand.properties(), times(1)).put(eq("dataPlatformObjectStatus"), eq(dpoStatus));
      verify(mockCommand.properties(), times(1))
          .put(eq("configurationStatus"), eq(ConfigurationStatus.DISABLED));
    }
  }

  @Nested
  class MergeWithTest {
    @Test
    @DisplayName(
        "Given a context with existing completed status, when merging with new failed step, then existing dpoStatuses are replaced with merged failed status")
    void
        givenContextWithExistingCompletedStatus_whenMergingWithNewFailedStatus_thenExistingStatusIsReplacedWithMergedFailedStatus() {
      // Arrange
      var context =
          new JobStatusContext(
              new ArrayList<>(List.of(newStep(ProvisioningStatus.PROVISIONING))),
              ConfigurationStatus.IN_PROVISIONING,
              RequestJobStatus.PROVISIONING);

      var failedStatus = newStep(ProvisioningStatus.FAILED);
      var newDPOStatuses = List.of(failedStatus);

      // Act
      context.mergeWith(newDPOStatuses);

      // Assert
      assertThat(context.dpoStatuses()).hasSize(1).containsExactly(failedStatus);
    }

    @Test
    @DisplayName(
        "Given a context with a status that is in provisioning, when merging with the latest status for the same provisioning step, then new status overrides the existing status.")
    void
        givenContextWithStatusInProvisioning_whenMergingWithLatestStatusForSameProvisioningStep_thenNewStatusOverridesExisting() {
      // Arrange
      var commonStatus = ProvisioningStep.KAFKA_TOPIC_PROVISIONING;
      var existingStatus =
          new DataPlatformObjectStatus(
              commonStatus,
              "oldId",
              ProvisioningStepType.REPOSITORY,
              ProvisioningStatus.PROVISIONING,
              OffsetDateTime.now(),
              "",
              null);
      var additionalExistingStatus = newStep(ProvisioningStatus.PROVISIONING);
      var newStatus =
          new DataPlatformObjectStatus(
              commonStatus,
              "newId",
              ProvisioningStepType.REPOSITORY,
              ProvisioningStatus.COMPLETED,
              OffsetDateTime.now(),
              "",
              null);

      var context =
          new JobStatusContext(
              new ArrayList<>(List.of(existingStatus, additionalExistingStatus)),
              ConfigurationStatus.IN_PROVISIONING,
              RequestJobStatus.PROVISIONING);

      // Act
      context.mergeWith(List.of(newStatus));

      // Assert
      assertThat(context.dpoStatuses())
          .hasSize(2)
          .contains(newStatus)
          .doesNotContain(existingStatus);
    }
  }

  @Nested
  class WithUpdatedStatusForTests {
    @Test
    @DisplayName(
        "Given a deployment process where all dpoStatuses have completed, when updating the status for the deployment type 'CREATE', then new statuses are calculated for the deployment process and reflected in the context.")
    void
        givenContextWithAllStepsCompleted_whenUpdatingStatusForCREATE_thenNewStatusesAreCalculated() {
      // Arrange
      var context =
          new JobStatusContext(
              List.of(
                  newStep(ProvisioningStep.OBJECT_REGISTRATION, ProvisioningStatus.COMPLETED),
                  newStep(ProvisioningStep.KAFKA_TOPIC_PROVISIONING, ProvisioningStatus.COMPLETED)),
              ConfigurationStatus.IN_PROVISIONING,
              RequestJobStatus.PROVISIONING);
      var targetType = CommandType.CREATE;

      // Act
      var updatedContext = context.withUpdatedStatusFor(targetType);

      // Assert
      assertThat(updatedContext.requestJobStatus())
          .isEqualTo(RequestJobStatus.COMPLETED)
          .isNotEqualTo(RequestJobStatus.PROVISIONING);
      assertThat(updatedContext.configurationStatus())
          .isEqualTo(ConfigurationStatus.ACTIVE)
          .isNotEqualTo(ConfigurationStatus.IN_PROVISIONING);
      assertThat(updatedContext.dpoStatuses()).isNotEmpty().containsAll(context.dpoStatuses());
    }

    @Test
    @DisplayName(
        "Given a deployment process where all dpoStatuses have completed, when updating the status for the deployment type 'DELETE', then new statuses are calculated for the deployment process and reflected in the context.")
    void
        givenContextWithAllStepsCompleted_whenUpdatingStatusForDELETE_thenNewStatusesAreCalculated() {
      // Arrange
      var context =
          new JobStatusContext(
              List.of(
                  newStep(
                      ProvisioningStep.CONFIGURATION_OBJECT_UPDATE, ProvisioningStatus.COMPLETED)),
              ConfigurationStatus.ACTIVE,
              RequestJobStatus.DELETING);
      var targetType = CommandType.DELETE;

      // Act
      var updatedContext = context.withUpdatedStatusFor(targetType);

      // Assert
      assertThat(updatedContext.requestJobStatus())
          .isEqualTo(RequestJobStatus.DELETED)
          .isNotEqualTo(RequestJobStatus.DELETING);
      assertThat(updatedContext.configurationStatus())
          .isEqualTo(ConfigurationStatus.DELETED)
          .isNotEqualTo(ConfigurationStatus.ACTIVE);
    }

    @Test
    @DisplayName(
        "Given a deployment process with all dpoStatuses completed but one of the dpoStatuses has failed, when updating the status for the deployment type 'CREATE', then new statuses are calculated for the deployment process and reflected in the context.")
    void
        givenContextWithAllStepsCompletedButOneFailed_whenUpdatingStatusForCREATE_thenNewStatusesAreCalculated() {
      // Arrange
      var context =
          new JobStatusContext(
              List.of(
                  newStep(ProvisioningStep.OBJECT_REGISTRATION, ProvisioningStatus.COMPLETED),
                  newStep(ProvisioningStep.KAFKA_TOPIC_PROVISIONING, ProvisioningStatus.FAILED)),
              ConfigurationStatus.IN_PROVISIONING,
              RequestJobStatus.PROVISIONING);

      // Act
      var updatedContext = context.withUpdatedStatusFor(CommandType.CREATE);

      // Assert
      assertThat(updatedContext.requestJobStatus())
          .isEqualTo(RequestJobStatus.FAILED)
          .isNotEqualTo(RequestJobStatus.PROVISIONING);
      assertThat(updatedContext.configurationStatus())
          .isEqualTo(ConfigurationStatus.DISABLED)
          .isNotEqualTo(ConfigurationStatus.IN_PROVISIONING);
    }
  }

  private static DataPlatformObjectStatus newStep(ProvisioningStatus status) {
    return newStep(ProvisioningStep.OBJECT_REGISTRATION, status);
  }

  private static DataPlatformObjectStatus newStep(
      ProvisioningStep step, ProvisioningStatus status) {
    return new DataPlatformObjectStatus(
        ProvisioningStep.OBJECT_REGISTRATION,
        "id",
        step.equals(ProvisioningStep.OBJECT_REGISTRATION)
            ? ProvisioningStepType.POSTGRES_DB_OBJECT
            : ProvisioningStepType.REPOSITORY,
        status,
        OffsetDateTime.now(),
        "",
        null);
  }
}
