package org.ude.deployment.common.wiremock;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import io.quarkus.test.common.QuarkusTestResourceLifecycleManager;
import io.quarkus.test.junit.callback.QuarkusTestBeforeClassCallback;
import java.util.Map;

public class WiremockResource
    implements QuarkusTestResourceLifecycleManager, QuarkusTestBeforeClassCallback {
  private WireMockServer wiremock;

  @Override
  public Map<String, String> start() {
    wiremock =
        new WireMockServer(
            WireMockConfiguration.wireMockConfig()
                .dynamicPort()
                .withPermittedSystemKeys("mocked.*"));
    wiremock.start();

    return Map.of("quarkus.wiremock-url", wiremock.baseUrl());
  }

  @Override
  public void stop() {
    if (null != wiremock) {
      wiremock.stop();
    }
  }

  @Override
  public void inject(TestInjector testInjector) {
    testInjector.injectIntoFields(
        wiremock,
        new TestInjector.AnnotatedAndMatchesType(InjectWireMock.class, WireMockServer.class));
  }

  @Override
  public void beforeClass(final Class<?> unused) { // (!) doesn't work for @Nested classes
    this.wiremock.resetAll();
  }
}
