package org.ude.deployment.azureblobstorage.azure;

import static org.assertj.core.api.Assertions.assertThat;

import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.ude.deployment.azureblobstorage.azure.util.AzureStorageAccountUtils;

@QuarkusTest
class LargeMessageConfigAzureTest {

  @Inject LargeMessageConfigAzure config;
  @Inject AzureStorageAccountUtils storageAccountUtils;

  @Test
  void testInContainerNameFormat() {
    // Given
    final int retentionDays = 7;
    final String dataOrderId = UUID.randomUUID().toString();

    // When
    final String containerNameIn =
        storageAccountUtils.containerName(config.in(), dataOrderId, retentionDays);

    // Then
    assertThat(containerNameIn).isEqualTo(dataOrderId);
  }

  @Test
  void testOutContainerNameFormat() {
    // Given
    final int retentionDays = 7;
    final String dataOrderId = UUID.randomUUID().toString();

    // When
    final String containerNameOut =
        storageAccountUtils.containerName(config.out(), dataOrderId, retentionDays);

    // Then
    assertThat(containerNameOut).isEqualTo(retentionDays + "d-" + dataOrderId);
  }
}
