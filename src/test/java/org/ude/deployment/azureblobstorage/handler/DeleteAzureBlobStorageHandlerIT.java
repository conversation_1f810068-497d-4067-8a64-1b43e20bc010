package org.ude.deployment.azureblobstorage.handler;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import java.util.Map;
import java.util.UUID;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.util.Fixture;

@QuarkusTest
public class DeleteAzureBlobStorageHandlerIT extends AbstractAzureBlobStorage {
  private static final String ORDER_ID = UUID.randomUUID().toString();

  @Inject ObjectMapper objectMapper;

  @Test
  @SneakyThrows
  public void testDeleteAzureBlobStorage() {
    final var containerName = this.containerName(4);
    this.createContainers(containerName);
    final var cmd = this.command(4);
    assertThat(this.commandExecuteAndAwaitStatus(cmd).get().value())
        .satisfies(
            status -> {
              assertThat(status.status()).isEqualTo(RequestJobStatus.DELETED);
            });
    assertThat(this.blobClientIn.getBlobContainerClient(containerName).exists()).isFalse();
    assertThat(this.blobClientOut.getBlobContainerClient(containerName).exists()).isFalse();
  }

  private String containerName(final int duration) {
    return "%dd-%s".formatted(duration, ORDER_ID);
  }

  @SneakyThrows
  private Command command(final int duration) {
    return this.objectMapper.readValue(
        Fixture.asString(
            "azureblobstorage/valid-delete-command-template.json",
            Map.of("dataOrderId", ORDER_ID, "duration", String.valueOf(duration))),
        Command.class);
  }

  private void createContainers(final String containerName) {
    this.blobServiceConnectorIn.getBlobClient().createBlobContainer(containerName);
    this.blobServiceConnectorOut.getBlobClient().getBlobContainerClient(containerName);
  }
}
