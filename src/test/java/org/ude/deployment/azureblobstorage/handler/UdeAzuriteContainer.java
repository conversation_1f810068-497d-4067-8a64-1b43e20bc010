package org.ude.deployment.azureblobstorage.handler;

import com.google.common.collect.ObjectArrays;
import org.testcontainers.azure.AzuriteContainer;

public final class UdeAzuriteContainer extends AzuriteContainer {
  private static final String AZURITE_VERSION = "3.33.0";

  public UdeAzuriteContainer(final String accountName) {
    super("mcr.microsoft.com/azure-storage/azurite:" + AZURITE_VERSION);
    this.withStartupAttempts(100).withEnv("AZURITE_ACCOUNTS", accountName + ":" + accountName);
  }

  @Override
  protected void configure() {
    super.configure();
    this.setCommandParts(ObjectArrays.concat(this.getCommandParts(), "--skipApiVersionCheck"));
  }
}
