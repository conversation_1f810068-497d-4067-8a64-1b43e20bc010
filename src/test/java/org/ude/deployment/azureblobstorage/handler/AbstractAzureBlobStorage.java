package org.ude.deployment.azureblobstorage.handler;

import static org.mockito.Mockito.when;

import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.common.ResourceArg;
import io.quarkus.test.junit.mockito.InjectSpy;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.ude.deployment.azureblobstorage.azure.BlobServiceClientConnector;
import org.ude.deployment.azureblobstorage.azure.LargeMessageConfigAzure;
import org.ude.deployment.azureblobstorage.azure.qualifier.BlobServiceClientInQualifier;
import org.ude.deployment.azureblobstorage.azure.qualifier.BlobServiceClientOutQualifier;
import org.ude.deployment.common.BaseHandlerIntegrationTest;

@QuarkusTestResource(
    value = AzuriteContainerResource.class,
    initArgs =
        @ResourceArg(
            name = AzuriteContainerResource.ARG_ACCOUNTS,
            value = "devstoreaccount1,devstoreaccount2"))
abstract class AbstractAzureBlobStorage extends BaseHandlerIntegrationTest {
  protected BlobServiceClient blobClientIn;
  protected BlobServiceClient blobClientOut;

  @Inject LargeMessageConfigAzure config;

  @InjectSpy @BlobServiceClientInQualifier
  protected BlobServiceClientConnector blobServiceConnectorIn;

  @InjectSpy @BlobServiceClientOutQualifier
  protected BlobServiceClientConnector blobServiceConnectorOut;

  @BeforeEach
  void abstractAzureBlobStorageInit() {
    this.blobClientIn =
        new BlobServiceClientBuilder()
            .connectionString(config.credential().connectionString().orElseThrow())
            .endpoint(blobServiceConnectorIn.getBlobClient().getAccountUrl())
            .buildClient();
    this.blobClientOut =
        new BlobServiceClientBuilder()
            .connectionString(config.credential().connectionString().orElseThrow())
            .endpoint(blobServiceConnectorOut.getBlobClient().getAccountUrl())
            .buildClient();

    when(blobServiceConnectorIn.getBlobClient()).thenReturn(blobClientIn);
    when(blobServiceConnectorIn.getConfig()).thenReturn(this.config.in());
    when(blobServiceConnectorOut.getBlobClient()).thenReturn(blobClientOut);
    when(blobServiceConnectorOut.getConfig()).thenReturn(this.config.out());
  }
}
