package org.ude.deployment.azureblobstorage.handler;

import io.quarkus.test.common.QuarkusTestResourceLifecycleManager;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

public final class AzuriteContainerResource implements QuarkusTestResourceLifecycleManager {
  public static final String ARG_ACCOUNTS = "accounts";

  private Map<String, UdeAzuriteContainer> emulators = new HashMap<>();

  @Override
  public void init(final Map<String, String> initArgs) {
    this.emulators =
        Arrays.stream(
                initArgs.getOrDefault(ARG_ACCOUNTS, "devstoreaccount1,devstoreaccount2").split(","))
            .collect(
                HashMap::new,
                (hm, acc) -> hm.put(acc, new UdeAzuriteContainer(acc)),
                HashMap::putAll);
  }

  @Override
  public Map<String, String> start() {
    this.emulators.values().forEach(UdeAzuriteContainer::start);

    return this.emulators.entrySet().stream()
        .collect(
            HashMap::new,
            (hm, entry) -> {
              final var acc = entry.getKey();
              final var emulator = entry.getValue();
              hm.put(
                  "testcontainers.azure.%s.endpoint".formatted(acc),
                  "http://%s:%d/%s"
                      .formatted(emulator.getHost(), emulator.getFirstMappedPort(), acc));
              hm.put(
                  "testcontainers.azure.%s.connection-string".formatted(acc),
                  emulator.getConnectionString(acc, acc));
            },
            HashMap::putAll);
  }

  @Override
  public void stop() {
    this.emulators.values().forEach(UdeAzuriteContainer::stop);
  }

  @Override
  public void inject(final TestInjector testInjector) {
    this.emulators.forEach(
        (acc, emulator) ->
            testInjector.injectIntoFields(
                emulator,
                field ->
                    field.getType().equals(UdeAzuriteContainer.class)
                        && StringUtils.containsIgnoreCase(field.getName(), acc)));
  }
}
