package org.ude.deployment.azureblobstorage.handler;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusReportedError;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;
import static org.ude.deployment.util.StatusUtils.extractResultOfType;

import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.file.datalake.DataLakeDirectoryClient;
import com.azure.storage.file.datalake.DataLakeFileSystemClient;
import com.azure.storage.file.datalake.DataLakeServiceClient;
import com.azure.storage.file.datalake.models.PathAccessControlEntry;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.util.Fixture;

@QuarkusTest
class CreateAzureBlobStorageHandlerIT extends AbstractAzureBlobStorage {
  private static final String ORDER_ID = UUID.randomUUID().toString();

  @Inject ObjectMapper objectMapper;

  @Test
  @SneakyThrows
  void createWithoutEntraGroupIds() {
    final var containerNameIn = ORDER_ID;
    final var containerNameOut = this.containerName(4);
    final var cmd = this.command(4);

    assertThat(this.commandExecuteAndAwaitStatus(cmd).get().value())
        .satisfies(
            status -> {
              assertThat(status.status()).isEqualTo(RequestJobStatus.COMPLETED);
              var dataPlatformObjectStatuses =
                  extractDataPlatformObjectStatuses(status.properties());
              var result = extractResultOfType(dataPlatformObjectStatuses, Map.class);

              assertThat(result.get(this.blobServiceConnectorIn.getConfig().endpoint()))
                  .isEqualTo(containerNameIn);
              assertThat(result.get(this.blobServiceConnectorOut.getConfig().endpoint()))
                  .isEqualTo(containerNameOut);
            });
    assertThat(this.blobClientIn.getBlobContainerClient(containerNameIn).exists()).isTrue();
    assertThat(this.blobClientOut.getBlobContainerClient(containerNameOut).exists()).isTrue();
  }

  @Test()
  @SneakyThrows
  void retryWithoutEntraGroupIds() {
    final var containerDuration = 2;
    // For IN containers, the name is just the orderId (no retention prefix)
    final var containerNameIn = ORDER_ID;
    // For OUT containers, the name includes the retention period
    final var containerNameOut = this.containerName(containerDuration);
    final var cmd = this.command(containerDuration);
    final var blobContainerClientIn = mock(BlobServiceClient.class);
    final var blobContainerClientOut = mock(BlobServiceClient.class);

    when(blobContainerClientIn.createBlobContainerIfNotExists(containerNameIn))
        .thenThrow(new IllegalStateException())
        .thenReturn(this.blobClientIn.createBlobContainerIfNotExists(containerNameIn));

    when(blobContainerClientOut.createBlobContainerIfNotExists(containerNameOut))
        .thenThrow(new IllegalStateException())
        .thenReturn(this.blobClientOut.createBlobContainerIfNotExists(containerNameOut));

    when(blobServiceConnectorIn.getConfig()).thenReturn(this.config.in());
    when(blobServiceConnectorOut.getConfig()).thenReturn(this.config.out());
    when(blobServiceConnectorIn.getBlobClient()).thenReturn(blobContainerClientIn);
    when(blobServiceConnectorOut.getBlobClient()).thenReturn(blobContainerClientOut);

    assertThat(this.commandExecuteAndAwaitStatus(cmd).get().value().status())
        .isEqualTo(RequestJobStatus.COMPLETED);

    assertThat(this.blobClientIn.getBlobContainerClient(containerNameIn).exists()).isTrue();
    assertThat(this.blobClientOut.getBlobContainerClient(containerNameOut).exists()).isTrue();

    verify(blobContainerClientIn, Mockito.times(3)).createBlobContainerIfNotExists(containerNameIn);
    verify(blobContainerClientOut, Mockito.times(2))
        .createBlobContainerIfNotExists(containerNameOut);
  }

  @Test
  @SneakyThrows
  void handleExceptions() {
    final var containerName = this.containerName(3);
    final var cmd = this.command(3);
    final var blobContainerClient = mock(BlobServiceClient.class);

    // Delete containers if they already exist
    if (this.blobClientIn.getBlobContainerClient(containerName).exists()) {
      this.blobClientIn.deleteBlobContainer(containerName);
    }
    if (this.blobClientOut.getBlobContainerClient(containerName).exists()) {
      this.blobClientOut.deleteBlobContainer(containerName);
    }

    when(blobContainerClient.createBlobContainerIfNotExists(anyString()))
        .thenThrow(new IllegalStateException("Test Exception"));

    when(this.blobServiceConnectorIn.getBlobClient()).thenReturn(blobContainerClient);
    when(this.blobServiceConnectorOut.getBlobClient()).thenReturn(blobContainerClient);

    assertThat(this.commandExecuteAndAwaitStatus(cmd).get().value())
        .satisfies(
            status -> {
              var dataPlatformObjectStatuses =
                  extractDataPlatformObjectStatuses(status.properties());
              assertThat(status.status()).isEqualTo(RequestJobStatus.FAILED);
              assertDpoStatusReportedError(
                  dataPlatformObjectStatuses,
                  DataPlatformObjectStatus.ProvisioningStep.KAFKA_TOPIC_PROVISIONING,
                  "Test Exception");
            });

    // Verify containers don't exist
    assertThat(this.blobClientIn.getBlobContainerClient(containerName).exists()).isFalse();
    assertThat(this.blobClientOut.getBlobContainerClient(containerName).exists()).isFalse();
  }

  @Test
  @SneakyThrows
  void createWithEntraGroupIds() {
    final var cmd = this.commandWithEntraGroupIds(3);
    // For simplicity, just create the containers directly
    final var containerName = this.containerName(5);
    final var dataLakeServiceClient = mock(DataLakeServiceClient.class);
    final var dataLakeFileSystemClient = mock(DataLakeFileSystemClient.class);
    final var dataLakeDirectoryClient = mock(DataLakeDirectoryClient.class);

    when(dataLakeServiceClient.getFileSystemClient(anyString()))
        .thenReturn(dataLakeFileSystemClient);
    when(dataLakeFileSystemClient.getDirectoryClient(anyString()))
        .thenReturn(dataLakeDirectoryClient);

    when(this.blobServiceConnectorIn.getDataLakeClient()).thenReturn(dataLakeServiceClient);
    when(this.blobServiceConnectorOut.getDataLakeClient()).thenReturn(dataLakeServiceClient);

    // Delete containers if they already exist
    if (this.blobClientIn.getBlobContainerClient(containerName).exists()) {
      this.blobClientIn.deleteBlobContainer(containerName);
    }
    if (this.blobClientOut.getBlobContainerClient(containerName).exists()) {
      this.blobClientOut.deleteBlobContainer(containerName);
    }

    assertThat(this.commandExecuteAndAwaitStatus(cmd).get().value().status())
        .isEqualTo(RequestJobStatus.COMPLETED);

    @SuppressWarnings("unchecked")
    final ArgumentCaptor<List<PathAccessControlEntry>> captor = ArgumentCaptor.forClass(List.class);

    verify(dataLakeDirectoryClient, times(1))
        .setAccessControlList(captor.capture(), eq(null), eq(null));

    var acls = captor.getAllValues();
    assertThat(acls.get(0))
        .usingRecursiveFieldByFieldElementComparator()
        .containsExactlyInAnyOrder(
            PathAccessControlEntry.parse("user::rwx"),
            PathAccessControlEntry.parse("group::r-x"),
            PathAccessControlEntry.parse("other::---"),
            PathAccessControlEntry.parse("group:44444444-4444-4444-4444-444444444444:rwx"),
            PathAccessControlEntry.parse("group:33333333-3333-3333-3333-333333333333:r-x"),
            PathAccessControlEntry.parse("group:22222222-2222-2222-2222-222222222222:r-x"),
            PathAccessControlEntry.parse("group:11111111-1111-1111-1111-111111111111:rwx"));
  }

  private String containerName(final int duration) {
    return "%dd-%s".formatted(duration, ORDER_ID);
  }

  @SneakyThrows
  private Command command(final int duration) {
    return this.objectMapper.readValue(
        Fixture.asString(
            "azureblobstorage/valid-create-command-template.json",
            Map.of("dataOrderId", ORDER_ID, "duration", String.valueOf(duration))),
        Command.class);
  }

  @SneakyThrows
  private Command commandWithEntraGroupIds(final int duration) {
    return this.objectMapper.readValue(
        Fixture.asString(
            "azureblobstorage/create-command-with-entra-ids.json",
            Map.of("dataOrderId", ORDER_ID, "duration", String.valueOf(duration))),
        Command.class);
  }
}
