package org.ude.deployment.streamingpipeline;

import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.okJson;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathTemplate;
import static org.assertj.core.api.Assertions.assertThat;
import static org.ude.deployment.streamingpipeline.CreateStreamingPipelineHandler.DCOM_CLIENT_NAME;
import static org.ude.deployment.util.StatusUtils.PipelineUtils.createExpectedConfigurationStatuses;
import static org.ude.deployment.util.StatusUtils.PipelineUtils.createExpectedDpoStatusesForCompletedCreation;
import static org.ude.deployment.util.StatusUtils.PipelineUtils.createExpectedRequestJobStatuses;
import static org.ude.deployment.util.StatusUtils.assertDpoAndConfigStatusesMatchExpected;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesDoNotContainResults;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedError;
import static org.ude.deployment.util.StatusUtils.assertDpoStatusesReportedNoErrors;
import static org.ude.deployment.util.StatusUtils.extractDataPlatformObjectStatuses;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import io.fabric8.kubernetes.api.model.GenericKubernetesResource;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.dsl.base.CustomResourceDefinitionContext;
import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.kubernetes.client.WithKubernetesTestServer;
import jakarta.inject.Inject;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.IntStream;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.ude.deployment.common.BaseHandlerIntegrationTest;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.TopicService;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateTopicsRequest;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;
import org.ude.deployment.common.wiremock.InjectWireMock;
import org.ude.deployment.common.wiremock.WiremockResource;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.dto.RetentionPeriod;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineCategory;
import org.ude.deployment.util.Fixture;
import org.ude.deployment.util.KafkaUtils;

@WithKubernetesTestServer
@QuarkusTest
@QuarkusTestResource(WiremockResource.class)
class CreateStreamingPipelineHandlerIT extends BaseHandlerIntegrationTest {

  @Inject KubernetesClient client;

  @Inject ObjectMapper objectMapper;

  @Inject FlinkEnvVariableConfig flinkEnvVariableConfig;

  CustomResourceDefinitionContext flinkDeploymentContext;
  static String dataOrderId = "data-order-1";

  static String dataOrderInputTopicSchema = "private.ude-str.data-order-1.input.json-value";

  private final String dataOrderWithNoSchemaAssociation = "data-order-no-schema";

  @Inject TopicService topicService;

  @InjectWireMock WireMockServer wiremock;

  @BeforeAll
  public void beforeAll() {
    createTestTopics();
    initSchemaRegistryWiremock(dataOrderInputTopicSchema);
    registerFlinkCRD();
  }

  void createTestTopics() {
    createTopics(new CreateTopicsRequest(dataOrderId, 1, 1));
    createTopics(new CreateTopicsRequest(dataOrderWithNoSchemaAssociation, 1, 1));
  }

  private void registerFlinkCRD() {
    try {
      var crdYaml =
          new String(
              Files.readAllBytes(
                  Paths.get(
                      Objects.requireNonNull(
                              getClass()
                                  .getResource(
                                      "/kubernetes/flinkdeployments.flink.apache.org-v1.yml"))
                          .toURI())));

      InputStream crdInputStream =
          new ByteArrayInputStream(crdYaml.getBytes(StandardCharsets.UTF_8));
      client.load(crdInputStream).create();

      flinkDeploymentContext =
          new CustomResourceDefinitionContext.Builder()
              .withGroup("flink.apache.org")
              .withVersion("v1beta1")
              .withScope("Namespaced")
              .withPlural("flinkdeployments")
              .build();
    } catch (Exception e) {
      throw new RuntimeException("Failed to register the CRD: " + e.getMessage());
    }
  }

  @AfterEach
  public void afterEach() {
    KafkaUtils.clearTopic(kafkaCompanion, statusTopic);
  }

  @ParameterizedTest
  @MethodSource("pipelineTestCases")
  @DisplayName(
      "Given a streaming pipeline command, when valid configs are generated, then {0} streaming"
          + " pipeline is successfully created.")
  void
      givenStreamingPipelineCommand_whenValidConfigsAreGenerated_thenStreamingPipelineIsSuccessfullyCreated(
          String name,
          String client,
          String dataOrderId,
          StreamingPipelineCategory category,
          String resourcePrefix,
          String jobType)
          throws JsonProcessingException {

    // Arrange
    var processingSchemaId = UUID.randomUUID().toString();
    var storageSchemaId = UUID.randomUUID().toString();
    var pipelineRequest =
        Fixture.asString(
            "streamingpipeline/create-pipeline-template.json",
            Map.of(
                "name",
                name,
                "dataOrderId",
                dataOrderId,
                "category",
                category.getValue(),
                "client",
                client,
                "processingSchemaId",
                processingSchemaId,
                "storageSchemaId",
                storageSchemaId));
    var command = this.objectMapper.readValue(pipelineRequest, Command.class);

    // Act
    kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
        .awaitCompletion();

    // Assert
    var pipelineCommands =
        kafkaCompanion
            .consume(Command.class)
            .withOffsetReset(OffsetResetStrategy.EARLIEST)
            .fromTopics(commandsTopic, 2)
            .awaitCompletion();

    assertThat(pipelineCommands.awaitCompletion().getRecords().size()).isEqualTo(2);
    assertThat(pipelineCommands.getRecords().getFirst().value().commandType())
        .isEqualTo(command.commandType());
    assertThat(pipelineCommands.getRecords().getLast().value().type())
        .isEqualTo(ConfigurationType.FLINK_JOB_PIPELINE);

    final int numberOfStatuses = 3;
    var statusRecords =
        KafkaUtils.getLastNStatusMessages(kafkaCompanion, statusTopic, numberOfStatuses);
    assertThat(statusRecords).isNotNull().hasSize(numberOfStatuses);
    var orderOfStatusTypes = createExpectedDpoStatusesForCompletedCreation();
    var orderOfConfigurationStatuses = createExpectedConfigurationStatuses();
    var orderOfRequestJobStatuses = createExpectedRequestJobStatuses();

    IntStream.range(0, statusRecords.size())
        .forEach(
            i ->
                assertStatusEquals(
                    statusRecords.get(i), command, orderOfRequestJobStatuses.get(i), List.of()));
    assertDpoAndConfigStatusesMatchExpected(
        statusRecords, orderOfStatusTypes, orderOfConfigurationStatuses);
    verifyKubernetesResource(dataOrderId, resourcePrefix, jobType);
  }

  private static List<Arguments> pipelineTestCases() {
    var dcomCase = "%s.%s".formatted(DCOM_CLIENT_NAME, UUID.randomUUID().toString());
    return List.of(
        Arguments.of(
            "Test Pipeline",
            "client1",
            dataOrderId,
            StreamingPipelineCategory.CUSTOM,
            "gdc-",
            "gdc-job"),
        Arguments.of(
            "Test Pipeline",
            "client2",
            dataOrderId,
            StreamingPipelineCategory.PASSTHRU,
            "pthru-",
            "passthrough-job"),
        Arguments.of(
            dcomCase,
            DCOM_CLIENT_NAME,
            dataOrderId,
            StreamingPipelineCategory.PASSTHRU,
            "pthru-",
            "passthrough-job"));
  }

  private List<Arguments> resourceValidationTestCases() {
    var missingTopicError =
        "Data platform object validation failed.\n Missing topics for dataOrderId random: private.ude-str.random.input.json, private.ude-str.random.non-retryable.dlq.json, public.ude-str.random.output.json";
    var missingSchemaError =
        "Data platform object validation failed.\n Schema private.ude-str.%s.input.json-value does not exist"
            .formatted(dataOrderWithNoSchemaAssociation);
    return List.of(
        Arguments.of(dataOrderWithNoSchemaAssociation, "random", List.of(missingSchemaError)),
        Arguments.of("random", dataOrderInputTopicSchema, List.of(missingTopicError)),
        Arguments.of("random", "random", List.of(missingTopicError)));
  }

  @ParameterizedTest
  @MethodSource("resourceValidationTestCases")
  @DisplayName(
      "Given DCOM streaming pipeline command, when routed then appropriate validation rules apply.")
  void givenDcomPipeline_whenRouted_thenValidationRulesApply(
      String dataOrderId, String schemaId, List<String> errors) throws JsonProcessingException {
    // Arrange
    initSchemaRegistryWiremock(schemaId);
    var name = UUID.randomUUID().toString();
    var processingSchemaId = UUID.randomUUID().toString();
    var storageSchemaId = UUID.randomUUID().toString();
    var pipelineRequest =
        Fixture.asString(
            "streamingpipeline/create-pipeline-template.json",
            Map.of(
                "name",
                name,
                "dataOrderId",
                dataOrderId,
                "category",
                StreamingPipelineCategory.CUSTOM.getValue(),
                "client",
                DCOM_CLIENT_NAME,
                "processingSchemaId",
                processingSchemaId,
                "storageSchemaId",
                storageSchemaId));
    var command = this.objectMapper.readValue(pipelineRequest, Command.class);
    // Act
    kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
        .awaitCompletion();

    // Assert
    var statusRecord = KafkaUtils.getLastStatusMessage(kafkaCompanion, statusTopic);
    assertThat(statusRecord).isNotNull();
    assertStatusEquals(statusRecord, command, RequestJobStatus.FAILED, errors);
  }

  @Test
  @DisplayName(
      "Given a GDC streaming pipeline command without schemas, when routed, then appropriate validation rules apply.")
  void givenPipelineCommandWithoutSchemas_whenRouted_thenValidationRulesApply()
      throws JsonProcessingException {
    // Arrange
    var name = UUID.randomUUID().toString();
    var pipelineRequest =
        Fixture.asString(
            "streamingpipeline/create-pipeline-template-without-schemas.json",
            Map.of(
                "name",
                name,
                "dataOrderId",
                dataOrderId,
                "category",
                StreamingPipelineCategory.CUSTOM.getValue()));
    var command = this.objectMapper.readValue(pipelineRequest, Command.class);

    // Act
    kafkaCompanion
        .produce(Command.class)
        .fromRecords(new ProducerRecord<>(commandsTopic, command.commandId(), command))
        .awaitCompletion();

    // Assert
    var statusRecords = KafkaUtils.getLastStatusMessage(kafkaCompanion, statusTopic);
    assertThat(statusRecords).isNotNull();
    assertStatusEquals(
        statusRecords,
        command,
        RequestJobStatus.FAILED,
        List.of(
            "Data platform object validation failed.\n Cannot create a pipeline of category CUSTOM / GDC for pipeline '%s' if less than 2 schema IDs are provided"
                .formatted(name)));
  }

  @Test
  @SneakyThrows
  void acceptColdStorageConfiguration() {
    final var command =
        this.objectMapper.readValue(
            Fixture.asString(
                "streamingpipeline/create-pipeline-with-valid-cold-storage-configuration.json"),
            Command.class);
    assertThat(this.commandExecuteAndAwaitStatus(command).get().value())
        .satisfies(
            status -> {
              final var feature =
                  status
                      .properties()
                      .getJsonObject("flinkDeploymentConfig")
                      .getJsonObject("featureSinkColdStorage");
              assertThat(feature.getBoolean("isEnabled")).isTrue();
              assertThat(feature.getInteger("maxBatchSize")).isEqualTo(6);
              assertThat(feature.getInteger("aggregationTimeoutS")).isEqualTo(7);
              assertThat(
                      feature
                          .getJsonObject("retentionPeriod")
                          .mapTo(RetentionPeriod.class)
                          .toDays())
                  .isEqualTo(5);
            });
  }

  private static void assertStatusEquals(
      ConsumerRecord<String, Status> actualStatusRecord,
      Command command,
      RequestJobStatus expectedStatus,
      List<String> errors) {
    var dataPlatformObjectStatuses =
        extractDataPlatformObjectStatuses(actualStatusRecord.value().properties());
    if (errors.isEmpty()) {
      assertDpoStatusesReportedNoErrors(dataPlatformObjectStatuses);
    } else {
      assertDpoStatusesReportedError(dataPlatformObjectStatuses, String.join(", ", errors));
    }
    assertDpoStatusesDoNotContainResults(dataPlatformObjectStatuses);

    assertThat(actualStatusRecord.value())
        .extracting(
            Status::commandType,
            Status::objectId,
            Status::type,
            Status::result,
            Status::errors,
            Status::status)
        .containsExactly(
            command.commandType(),
            command.objectId(),
            command.type(),
            null,
            errors,
            expectedStatus);
  }

  private void verifyKubernetesResource(String dataOrderId, String prefix, String jobType) {
    var resourceName = prefix + dataOrderId;
    GenericKubernetesResource resource =
        client
            .genericKubernetesResources(flinkDeploymentContext)
            .inNamespace(flinkEnvVariableConfig.namespace)
            .withName(resourceName)
            .get();

    assertThat(resource).isNotNull();
    assertThat(resource.getMetadata().getName()).isEqualTo(resourceName);
    var jobTypeLabel = resource.get("spec", "podTemplate", "metadata", "labels", "jobType");
    assertThat(jobTypeLabel).isEqualTo(jobType);
  }

  private void initSchemaRegistryWiremock(String foundSchema) {
    String stubSchema =
        """
                      {
                          "id": 333,
                          "version": 1,
                          "schemaType": "JSON",
                          "schema": "{}"
                      }""";
    wiremock.stubFor(
        get(urlPathTemplate("/subjects/%s/versions/latest".formatted(foundSchema)))
            .atPriority(1)
            .willReturn(okJson(stubSchema)));
    wiremock.stubFor(
        get(urlPathTemplate("/subjects/%s/versions".formatted(foundSchema)))
            .atPriority(3)
            .willReturn(okJson(stubSchema)));
  }

  private void createTopics(CreateTopicsRequest request) {
    this.topicService.processDataOrderTopicsCreation(request).await().indefinitely();
  }
}
