package org.ude.deployment.streamingpipeline;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.ude.deployment.util.DeploymentConfigUtils.getDeploymentName;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.vertx.core.json.JsonObject;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.CommandProducer;
import org.ude.deployment.flink.dto.BaseDeploymentConfig;
import org.ude.deployment.flink.dto.GdcFlinkDeploymentConfig;
import org.ude.deployment.streamingpipeline.config.factory.DeploymentConfigFactory;
import org.ude.deployment.streamingpipeline.config.factory.DeploymentConfigFactoryProvider;
import org.ude.deployment.streamingpipeline.dto.NewStreamingPipelineRequest;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineCategory;

class CreateStreamingPipelineHandlerTest {

  @Mock ObjectMapper objectMapper;

  @Mock CommandProducer commandProducer;

  @Mock DeploymentConfigFactoryProvider deploymentConfigFactoryProvider;

  @InjectMocks CreateStreamingPipelineHandler handler;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  private static final String TEST_PIPELINE_NAME = "Test Pipeline";
  private static final String TEST_PROJECT_ID = "123";
  private static final StreamingPipelineCategory TEST_CATEGORY = StreamingPipelineCategory.CUSTOM;

  @Test
  @DisplayName("Given a valid command, when handle, then creates and sends deployment commands.")
  void givenValidCommand_whenHandle_thenCreatesAndSendsDeploymentCommands() {
    // Arrange
    var mockCommand = createMockCommand();
    NewStreamingPipelineRequest mockPipelineRequest = createMockPipelineRequest();

    when(objectMapper.convertValue(any(JsonObject.class), eq(NewStreamingPipelineRequest.class)))
        .thenReturn(mockPipelineRequest);

    when(deploymentConfigFactoryProvider.getDeploymentConfigFactory(
            any(StreamingPipelineCategory.class)))
        .thenReturn(createMockDeploymentConfigFactory());

    CompletableFuture<Void> mockFuture = CompletableFuture.completedFuture(null);
    when(commandProducer.sendCommand(any())).thenReturn(mockFuture);

    // Act
    handler.handle(mockCommand);

    // Assert
    var argumentCaptor = ArgumentCaptor.forClass(JsonObject.class);
    verify(objectMapper)
        .convertValue(argumentCaptor.capture(), eq(NewStreamingPipelineRequest.class));
    var capturedPipelineRequest = argumentCaptor.getValue();
    assertThat(capturedPipelineRequest).isEqualTo(mockCommand.properties());
    var categoryArgumentCaptor = ArgumentCaptor.forClass(StreamingPipelineCategory.class);
    verify(deploymentConfigFactoryProvider, times(2))
        .getDeploymentConfigFactory(categoryArgumentCaptor.capture());
    var capturedCategory = categoryArgumentCaptor.getValue();
    assertThat(capturedCategory).isEqualTo(TEST_CATEGORY);
    verify(commandProducer, times(2)).sendCommand(any(Command.class));
  }

  private Command createMockCommand() {
    return new Command(
        UUID.randomUUID().toString(),
        CommandType.CREATE,
        UUID.randomUUID(),
        ConfigurationType.STREAMING_PIPELINE,
        new JsonObject());
  }

  private NewStreamingPipelineRequest createMockPipelineRequest() {
    return new NewStreamingPipelineRequest(
        TEST_PIPELINE_NAME,
        "Test Streaming Pipeline Description",
        TEST_PROJECT_ID,
        TEST_CATEGORY,
        List.of(
            new NewStreamingPipelineRequest.SchemaDataOrdersMapping(
                "schema-1",
                List.of(
                    new PipelineDataOrderPropertiesConfig(
                        "data-order-1",
                        "Test Data Order 1",
                        Optional.empty(),
                        List.of("processing-schema-uuid-1", "storage-schema-uuid-1"),
                        null,
                        new PipelineDataOrderPropertiesConfig.MetadataProperties(
                            List.of(
                                new PipelineDataOrderPropertiesConfig.MetadataProperties
                                    .DataEnrichment(
                                    "storage-schema-uuid-1",
                                    List.of(
                                        PipelineDataOrderPropertiesConfig.MetadataProperties
                                            .VehicleProperty.VIN)))),
                        new JsonObject()),
                    new PipelineDataOrderPropertiesConfig(
                        "data-order-2",
                        "Test Data Order 2",
                        Optional.empty(),
                        List.of("processing-schema-uuid-2", "storage-schema-uuid-2"),
                        7,
                        new PipelineDataOrderPropertiesConfig.MetadataProperties(
                            List.of(
                                new PipelineDataOrderPropertiesConfig.MetadataProperties
                                    .DataEnrichment(
                                    "storage-schema-uuid-2",
                                    List.of(
                                        PipelineDataOrderPropertiesConfig.MetadataProperties
                                            .VehicleProperty.VIN)))),
                        new JsonObject())))),
        new JsonObject());
  }

  private DeploymentConfigFactory createMockDeploymentConfigFactory() {
    return (pipelineConfig) -> createMockBaseDeploymentConfig();
  }

  private BaseDeploymentConfig createMockBaseDeploymentConfig() {
    return new GdcFlinkDeploymentConfig(
        getDeploymentName(TEST_CATEGORY.getDeploymentType(), "pipeline"),
        "gdcImageName",
        "busybox",
        "input-topic",
        "output-topic",
        "signals",
        "normalization_rule",
        "non-retryable-error-topic",
        "storage-schema-uuid-1",
        "[]",
        "[]",
        Optional.empty(),
        Optional.empty());
  }
}
