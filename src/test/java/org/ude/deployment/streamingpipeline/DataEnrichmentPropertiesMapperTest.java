package org.ude.deployment.streamingpipeline;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig.MetadataProperties;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig.MetadataProperties.DataEnrichment;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig.MetadataProperties.VehicleProperty;

class DataEnrichmentPropertiesMapperTest {

  private static final String TEST_VMS_SOURCE = "private.ude-str.vms-connector.flattened.json";

  private DataEnrichmentPropertiesMapper mapper;

  @BeforeEach
  void setUp() {
    mapper = new DataEnrichmentPropertiesMapper(TEST_VMS_SOURCE);
  }

  @DisplayName(
      "Given vehicle properties, when createEnrichmentConfigString, then return valid json.")
  @ParameterizedTest
  @MethodSource("vehiclePropertyCombinations")
  void givenVehicleProperties_whenCreateEnrichmentConfigString_thenReturnValidJson(
      List<VehicleProperty> properties, String expected) throws JsonProcessingException {
    // Arrange
    var dataEnrichment = new DataEnrichment("test-schema-id", properties);
    var metadataProperties = new MetadataProperties(List.of(dataEnrichment));

    // Act
    var result = mapper.createEnrichmentConfigString(metadataProperties);

    // Assert
    assertThat(result).isEqualTo(expected);
  }

  private static Stream<Arguments> vehiclePropertyCombinations() {
    return Stream.of(
        Arguments.of(
            List.of(VehicleProperty.VIN),
            "[{\"schemaId\":\"test-schema-id\",\"enrichmentData\":[{\"source\":\"%s\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"VIN\"}]}]}]"
                .formatted(TEST_VMS_SOURCE)),
        Arguments.of(
            List.of(VehicleProperty.VIN, VehicleProperty.BRAND),
            "[{\"schemaId\":\"test-schema-id\",\"enrichmentData\":[{\"source\":\"%s\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"VIN\"},{\"sourceName\":\"brand\",\"outputName\":\"Brand\"}]}]}]"
                .formatted(TEST_VMS_SOURCE)),
        Arguments.of(
            List.of(
                VehicleProperty.VIN,
                VehicleProperty.BRAND,
                VehicleProperty.PLATFORM,
                VehicleProperty.COUNTRY),
            "[{\"schemaId\":\"test-schema-id\",\"enrichmentData\":[{\"source\":\"%s\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"VIN\"},{\"sourceName\":\"brand\",\"outputName\":\"Brand\"},{\"sourceName\":\"platform\",\"outputName\":\"Platform\"},{\"sourceName\":\"country\",\"outputName\":\"Country\"}]}]}]"
                .formatted(TEST_VMS_SOURCE)));
  }

  @DisplayName(
      "Given null or empty data enrichments, when createEnrichmentConfigString, then return"
          + " empty json.")
  @ParameterizedTest
  @NullAndEmptySource
  void givenNullOrEmptyDataEnrichments_whenCreateEnrichmentConfigString_thenReturnEmptyJson(
      List<DataEnrichment> dataEnrichments) throws JsonProcessingException {
    // Arrange
    var props = new MetadataProperties(dataEnrichments);

    // Act
    var result = mapper.createEnrichmentConfigString(props);

    // Assert
    assertThat(result).isEqualTo("[]");
  }

  @DisplayName(
      "Given multiple data enrichments, when createEnrichmentConfigString, then return valid"
          + " json.")
  @ParameterizedTest
  @MethodSource("multipleEnrichmentConfigs")
  void givenMultipleDataEnrichments_whenCreateEnrichmentConfigString_thenReturnValidJson(
      List<DataEnrichment> enrichments, String expected) throws JsonProcessingException {
    // Arrange
    var metadataProperties = new MetadataProperties(enrichments);

    // Act
    var result = mapper.createEnrichmentConfigString(metadataProperties);

    // Assert
    assertThat(result).isEqualTo(expected);
  }

  private static Stream<Arguments> multipleEnrichmentConfigs() {
    return Stream.of(
        Arguments.of(
            List.of(
                new DataEnrichment("schema-1", List.of(VehicleProperty.VIN)),
                new DataEnrichment("schema-2", List.of(VehicleProperty.BRAND))),
            "[{\"schemaId\":\"schema-1\",\"enrichmentData\":[{\"source\":\"%s\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"vin\",\"outputName\":\"VIN\"}]}]},{\"schemaId\":\"schema-2\",\"enrichmentData\":[{\"source\":\"%s\",\"type\":\"kafka\",\"fields\":[{\"sourceName\":\"brand\",\"outputName\":\"Brand\"}]}]}]"
                .formatted(TEST_VMS_SOURCE, TEST_VMS_SOURCE)));
  }

  @DisplayName(
      "Given empty vehicle properties list, when createEnrichmentConfigString, then return"
          + " valid json with empty fields.")
  @Test
  void
      givenEmptyVehiclePropertiesList_whenCreateEnrichmentConfigString_thenReturnValidJsonWithEmptyFields()
          throws JsonProcessingException {
    // Arrange
    var dataEnrichment = new DataEnrichment("test-schema-id", List.of());
    var metadataProperties = new MetadataProperties(List.of(dataEnrichment));

    // Act
    var result = mapper.createEnrichmentConfigString(metadataProperties);

    // Assert
    assertTrue(result.contains("\"schemaId\":\"test-schema-id\""));
    assertTrue(result.contains("\"fields\":[]"));
  }

  @DisplayName(
      "Given null vehicle properties list, when createEnrichmentConfigString, then return"
          + " valid json enrichment configs.")
  @Test
  void
      givenNullVehiclePropertiesList_whenCreateEnrichmentConfigString_thenReturnValidJsonEnrichmentConfigs()
          throws JsonProcessingException {
    // Arrange
    var dataEnrichment = new DataEnrichment("test-schema-id", null);
    var metadataProperties = new MetadataProperties(List.of(dataEnrichment));

    // Act
    var result = mapper.createEnrichmentConfigString(metadataProperties);

    // Assert
    var expected =
        "[{\"schemaId\":\"test-schema-id\",\"enrichmentData\":[{\"source\":\"%s\",\"type\":\"kafka\",\"fields\":[]}]}]"
            .formatted(TEST_VMS_SOURCE);
    assertThat(result).isEqualTo(expected);
  }
}
