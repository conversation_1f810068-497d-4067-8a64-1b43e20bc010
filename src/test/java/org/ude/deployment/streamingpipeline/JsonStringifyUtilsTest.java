package org.ude.deployment.streamingpipeline;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.vertx.core.json.JsonArray;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class JsonStringifyUtilsTest {

  @DisplayName("Given null array, when stringify, then return default value.")
  @Test
  void givenNullArray_whenStringify_thenReturnDefaultValue() throws JsonProcessingException {
    // Arrange
    JsonArray objects = null;

    // Act
    var result = JsonStringifyUtils.stringify(objects, "[]");

    // Assert
    assertThat(result).isEqualTo("[]");
  }

  @DisplayName("Given empty array, when stringify, then return default value.")
  @Test
  void givenEmptyArray_whenStringify_thenReturnDefaultValue() throws JsonProcessingException {
    // Arrange
    var objects = new JsonArray();

    // Act
    var result = JsonStringifyUtils.stringify(objects, "[]");

    // Assert
    assertThat(result).isEqualTo("[]");
  }

  @DisplayName("Given an array of objects, when stringify, then return string representation.")
  @Test
  void givenArrayOfObjects_whenStringify_thenReturnStringRepresentation()
      throws JsonProcessingException {
    // Arrange
    var objects = new JsonArray().add(Map.of("key", "value"));

    // Act
    var result = JsonStringifyUtils.stringify(objects, "[]");

    // Assert
    assertThat(result).isEqualTo("[{\"key\":\"value\"}]");
  }

  @DisplayName(
      "Given an array of complex objects, when stringify, then return string representation.")
  @Test
  void givenArrayOfComplexObjects_whenStringify_thenReturnStringRepresentation()
      throws JsonProcessingException {
    // Arrange
    var complexObj = Map.of("name", "Test", "additionalInfo", Map.of("age", 30));
    var objects = new JsonArray().add(complexObj);

    // Act
    var result = JsonStringifyUtils.stringify(objects, "[]");

    // Assert
    assertThat(result).isEqualTo("[{\"additionalInfo\":{\"age\":30},\"name\":\"Test\"}]");
  }
}
