package org.ude.deployment.streamingpipeline.dto;

import io.vertx.core.json.JsonObject;
import java.util.List;

public record NewStreamingPipelineRequest(
    String name,
    String description,
    String projectId,
    StreamingPipelineCategory category,
    List<SchemaDataOrdersMapping> mappings,
    JsonObject additionalProperties) {

  public record SchemaDataOrdersMapping(
      String schemaId, List<PipelineDataOrderPropertiesConfig> dataOrders) {}
}
