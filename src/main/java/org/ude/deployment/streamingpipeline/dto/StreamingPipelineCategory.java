package org.ude.deployment.streamingpipeline.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Arrays;
import lombok.Getter;
import org.ude.deployment.flink.dto.DeploymentType;

@Getter
public enum StreamingPipelineCategory {
  // TODO: remove this Custom->GDC mapping once DCOM adopts the new register streaming pipeline flow
  @JsonProperty("Custom")
  CUSTOM("Custom", DeploymentType.GDC_PIPELINE),
  @JsonProperty("Passthru")
  PASSTHRU("Passthru", DeploymentType.PASSTHROUGH_PIPELINE);

  private final String value;

  private final DeploymentType deploymentType;

  StreamingPipelineCategory(String value, DeploymentType deploymentType) {
    this.value = value;
    this.deploymentType = deploymentType;
  }

  public static StreamingPipelineCategory fromString(final String str) {
    return Arrays.stream(values())
        .filter(enm -> enm.value.equalsIgnoreCase(str))
        .findAny()
        .orElseThrow(
            () -> new IllegalArgumentException("Unable to convert '%s' to enum".formatted(str)));
  }

  public static StreamingPipelineCategory fromDeploymentType(final DeploymentType deploymentType) {
    return Arrays.stream(values())
        .filter(enm -> enm.deploymentType.equals(deploymentType))
        .findAny()
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "Unable to convert '%s' to enum".formatted(deploymentType)));
  }
}
