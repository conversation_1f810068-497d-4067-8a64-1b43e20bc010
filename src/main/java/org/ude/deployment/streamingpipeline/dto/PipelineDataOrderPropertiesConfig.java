package org.ude.deployment.streamingpipeline.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.vertx.core.json.JsonObject;
import java.util.List;
import lombok.Builder;
import lombok.Getter;

@Builder
public record PipelineDataOrderPropertiesConfig(
    @JsonAlias("id") String dataOrderId,
    String name,
    List<String> schemaIds,
    Integer retentionPeriod,
    MetadataProperties metadataProperties,
    JsonObject additionalProperties) {
  public record MetadataProperties(List<DataEnrichment> dataEnrichment) {
    public record DataEnrichment(String schemaId, List<VehicleProperty> vehicleProps) {}

    @Getter
    public enum VehicleProperty {
      VIN("VIN"),
      BRAND("Brand"),
      PLATFORM("Platform"),
      COUNTRY("Country");

      private final String value;

      VehicleProperty(String value) {
        this.value = value;
      }
    }
  }
}
