package org.ude.deployment.streamingpipeline.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.vertx.core.json.JsonObject;
import java.util.List;
import java.util.Optional;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import org.ude.deployment.flink.dto.ColdStorageConfiguration;

@Builder
public record PipelineDataOrderPropertiesConfig(
    @JsonAlias("id") String dataOrderId,
    String name,
    @NonNull Optional<DataOrderEntity> dataOrder,
    List<String> schemaIds,
    Integer retentionPeriod,
    MetadataProperties metadataProperties,
    JsonObject additionalProperties) {
  public record MetadataProperties(List<DataEnrichment> dataEnrichment) {
    public record DataEnrichment(String schemaId, List<VehicleProperty> vehicleProps) {}

    @Getter
    public enum VehicleProperty {
      VIN("VIN"),
      BRAND("Brand"),
      PLATFORM("Platform"),
      COUNTRY("Country");

      private final String value;

      VehicleProperty(String value) {
        this.value = value;
      }
    }
  }

  // DataOrderEntity in management api
  public record DataOrderEntity(@NonNull DataOrderEntityDetails details) {}

  // NewDataOrderRequest in management api
  public record DataOrderEntityDetails(
      @NonNull DataOrderEntityDetailsConfiguration configuration) {}

  // NewDataOrderRequest.DataOrderRequestConfig in management api
  public record DataOrderEntityDetailsConfiguration(
      Optional<DataOrderEntityDetailsConfigurationDataLake> dataLake) {}

  // DataOrderConfigDataLake in management api
  public record DataOrderEntityDetailsConfigurationDataLake(
      @NonNull @JsonProperty("adlsgen2") Optional<ColdStorageConfiguration> coldStorage) {}
}
