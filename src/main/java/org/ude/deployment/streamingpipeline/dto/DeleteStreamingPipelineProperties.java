package org.ude.deployment.streamingpipeline.dto;

import org.ude.deployment.dataorder.dto.ModifyDataOrderCommand;

/**
 * Command properties to delete a streaming pipeline.
 *
 * @param request the request to delete the pipeline
 * @param deleteDataOrder if the request originated from a DELETE DataOrder command. This would make
 *     the originalDataOrderProperties mandatory
 * @param originalDataOrderProperties the original delete DataOrder command properties which needs
 *     to be emitted after the pipeline has been deleted. May be null if deleteDataOrder is false
 */
public record DeleteStreamingPipelineProperties(
    StreamingPipelineRequest request,
    boolean deleteDataOrder,
    ModifyDataOrderCommand originalDataOrderProperties) {}
