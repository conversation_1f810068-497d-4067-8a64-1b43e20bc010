package org.ude.deployment.streamingpipeline;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.vertx.core.json.JsonArray;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Collections;
import java.util.List;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.streamingpipeline.dto.EnrichmentConfig;
import org.ude.deployment.streamingpipeline.dto.EnrichmentConfig.EnrichmentData;
import org.ude.deployment.streamingpipeline.dto.EnrichmentConfig.Mapping;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig.MetadataProperties;

@ApplicationScoped
public class DataEnrichmentPropertiesMapper {
  private static final String KAFKA_SOURCE_TYPE = "kafka";

  private final String vmsSource;

  public DataEnrichmentPropertiesMapper(
      @ConfigProperty(name = "flink-deployment-config.vms-input-topic-name") String vmsSource) {
    this.vmsSource = vmsSource;
  }

  public String createEnrichmentConfigString(MetadataProperties dataOrderPropertiesConfigs)
      throws JsonProcessingException {
    var enrichmentConfigs = createEnrichmentConfigs(dataOrderPropertiesConfigs);
    return JsonStringifyUtils.stringify(new JsonArray(enrichmentConfigs), "[]");
  }

  private List<EnrichmentConfig> createEnrichmentConfigs(MetadataProperties metadataProperties) {
    if (metadataProperties == null
        || metadataProperties.dataEnrichment() == null
        || metadataProperties.dataEnrichment().isEmpty()) {
      return Collections.emptyList();
    }

    return metadataProperties.dataEnrichment().stream().map(this::mapToEnrichmentConfig).toList();
  }

  private EnrichmentConfig mapToEnrichmentConfig(MetadataProperties.DataEnrichment dataEnrichment) {
    return new EnrichmentConfig(
        dataEnrichment.schemaId(), enrichmentDataOf(dataEnrichment.vehicleProps()));
  }

  private @NotNull List<EnrichmentData> enrichmentDataOf(
      List<MetadataProperties.VehicleProperty> vehicleProperties) {
    return List.of(
        new EnrichmentData(vmsSource, KAFKA_SOURCE_TYPE, propertyMappingsOf(vehicleProperties)));
  }

  private static @NotNull List<Mapping> propertyMappingsOf(
      List<MetadataProperties.VehicleProperty> vehicleProperties) {
    if (vehicleProperties == null || vehicleProperties.isEmpty()) {
      return Collections.emptyList();
    }

    return vehicleProperties.stream()
        .map(property -> new Mapping(property.getValue().toLowerCase(), property.getValue()))
        .toList();
  }
}
