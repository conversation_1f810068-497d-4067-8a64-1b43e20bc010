package org.ude.deployment.streamingpipeline.config.factory;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.ude.deployment.common.infrastructure.kafka.KafkaConfig;
import org.ude.deployment.flink.DeploymentNameResolver;
import org.ude.deployment.flink.dto.BaseDeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.dto.PassthroughDeploymentConfig;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig;

@ApplicationScoped
public class PassthroughDeploymentConfigFactory implements DeploymentConfigFactory {

  private final KafkaConfig kafkaConfig;
  private final FlinkEnvVariableConfig flinkEnvVariableConfig;
  private final DeploymentNameResolver deploymentNameResolver;

  @Inject
  public PassthroughDeploymentConfigFactory(
      KafkaConfig kafkaConfig,
      FlinkEnvVariableConfig flinkEnvVariableConfig,
      DeploymentNameResolver deploymentNameResolver) {
    this.kafkaConfig = kafkaConfig;
    this.flinkEnvVariableConfig = flinkEnvVariableConfig;
    this.deploymentNameResolver = deploymentNameResolver;
  }

  @Override
  public BaseDeploymentConfig createDeploymentConfig(
      PipelineDataOrderPropertiesConfig dataOrderPropertiesConfig) {
    return new PassthroughDeploymentConfig(
        deploymentNameResolver.resolve(
            DeploymentType.PASSTHROUGH_PIPELINE.getPrefix(),
            dataOrderPropertiesConfig.dataOrderId()),
        flinkEnvVariableConfig.flinkPassthroughJobName,
        kafkaConfig.getInputTopicName(dataOrderPropertiesConfig.dataOrderId()),
        kafkaConfig.getOutputTopicName(dataOrderPropertiesConfig.dataOrderId()),
        kafkaConfig.getNonRetryableDlqTopicName(dataOrderPropertiesConfig.dataOrderId()));
  }
}
