package org.ude.deployment.streamingpipeline.config.factory;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.vertx.core.json.JsonArray;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.Optional;
import org.ude.deployment.common.infrastructure.kafka.KafkaConfig;
import org.ude.deployment.flink.DeploymentNameResolver;
import org.ude.deployment.flink.dto.BaseDeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.dto.GdcFlinkDeploymentConfig;
import org.ude.deployment.flink.dto.RetentionPeriod;
import org.ude.deployment.streamingpipeline.DataEnrichmentPropertiesMapper;
import org.ude.deployment.streamingpipeline.JsonStringifyUtils;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig;

@ApplicationScoped
public class GdcDeploymentConfigFactory implements DeploymentConfigFactory {
  public static final int STORAGE_SCHEMA_INDEX = 1;

  private final KafkaConfig kafkaConfig;
  private final FlinkEnvVariableConfig flinkEnvVariableConfig;
  private final DataEnrichmentPropertiesMapper dataEnrichmentPropertiesMapper;
  private final DeploymentNameResolver deploymentNameResolver;

  @Inject
  public GdcDeploymentConfigFactory(
      KafkaConfig kafkaConfig,
      FlinkEnvVariableConfig flinkEnvVariableConfig,
      DataEnrichmentPropertiesMapper dataEnrichmentPropertiesMapper,
      DeploymentNameResolver deploymentNameResolver) {
    this.kafkaConfig = kafkaConfig;
    this.flinkEnvVariableConfig = flinkEnvVariableConfig;
    this.dataEnrichmentPropertiesMapper = dataEnrichmentPropertiesMapper;
    this.deploymentNameResolver = deploymentNameResolver;
  }

  @Override
  public BaseDeploymentConfig createDeploymentConfig(
      final PipelineDataOrderPropertiesConfig dataOrderPropertiesConfig)
      throws JsonProcessingException {
    return GdcFlinkDeploymentConfig.builder()
        .deploymentName(
            deploymentNameResolver.resolve(
                DeploymentType.GDC_PIPELINE.getPrefix(), dataOrderPropertiesConfig.dataOrderId()))
        .jobImageName(flinkEnvVariableConfig.flinkGdcJobName)
        .flinkInitContainerImageName(flinkEnvVariableConfig.flinkInitContainerImageName)
        .inputTopicName(kafkaConfig.getInputTopicName(dataOrderPropertiesConfig.dataOrderId()))
        .outputTopicName(kafkaConfig.getOutputTopicName(dataOrderPropertiesConfig.dataOrderId()))
        .vdcDatabaseName(flinkEnvVariableConfig.vdcDatabaseName)
        .vdcRulesTableName(flinkEnvVariableConfig.vdcRulesTableName)
        .nonRetryableErrorTopicName(
            kafkaConfig.getNonRetryableDlqTopicName(dataOrderPropertiesConfig.dataOrderId()))
        .storageSchemaId(dataOrderPropertiesConfig.schemaIds().get(STORAGE_SCHEMA_INDEX))
        .enrichmentConfig(
            dataEnrichmentPropertiesMapper.createEnrichmentConfigString(
                dataOrderPropertiesConfig.metadataProperties()))
        .fctRules(JsonStringifyUtils.stringify(getGIds(dataOrderPropertiesConfig), "[]"))
        .featureSignalPackaging(this.featureSignalPackaging(dataOrderPropertiesConfig))
        .featureSinkColdStorage(this.featureColdStorage(dataOrderPropertiesConfig))
        .build();
  }

  private static JsonArray getGIds(PipelineDataOrderPropertiesConfig dataOrderPropertiesConfig) {
    return dataOrderPropertiesConfig.additionalProperties() != null
        ? dataOrderPropertiesConfig.additionalProperties().getJsonArray("gIds")
        : null;
  }

  private Optional<GdcFlinkDeploymentConfig.TopologyFeatureSinkColdStorage> featureColdStorage(
      final PipelineDataOrderPropertiesConfig pipelineProperties) {
    return pipelineProperties
        .dataOrder()
        .flatMap(
            doe ->
                doe.details()
                    .configuration()
                    .dataLake()
                    .flatMap(
                        PipelineDataOrderPropertiesConfig
                                .DataOrderEntityDetailsConfigurationDataLake
                            ::coldStorage)
                    .map(
                        cfgCs ->
                            GdcFlinkDeploymentConfig.TopologyFeatureSinkColdStorage.builder()
                                .isEnabled(true)
                                .retentionPeriod(RetentionPeriod.ofDays(cfgCs.retentionPeriod()))
                                .aggregationTimeoutS(cfgCs.rollingConfiguration().batchFrequency())
                                .maxBatchSize(cfgCs.rollingConfiguration().maxBatchSize())
                                .containerNameTpl(Optional.empty())
                                .build()));
  }

  private Optional<GdcFlinkDeploymentConfig.TopologyFeatureSignalPackaging> featureSignalPackaging(
      final PipelineDataOrderPropertiesConfig pipelineProperties) {

    return Optional.ofNullable(pipelineProperties.additionalProperties())
        .map(
            o ->
                Optional.ofNullable(o.getBoolean("signalPackaging"))
                    .orElseGet(() -> o.getBoolean("signalPackageEnabled")))
        .map(
            flag ->
                GdcFlinkDeploymentConfig.TopologyFeatureSignalPackaging.builder()
                    .isEnabled(flag)
                    .build());
  }
}
