package org.ude.deployment.streamingpipeline.config.factory;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.ude.deployment.flink.dto.BaseDeploymentConfig;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig;

public interface DeploymentConfigFactory {
  BaseDeploymentConfig createDeploymentConfig(PipelineDataOrderPropertiesConfig pipelineConfig)
      throws JsonProcessingException;
}
