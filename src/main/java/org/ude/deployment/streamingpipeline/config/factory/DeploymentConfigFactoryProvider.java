package org.ude.deployment.streamingpipeline.config.factory;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineCategory;

@ApplicationScoped
public class DeploymentConfigFactoryProvider {

  private final GdcDeploymentConfigFactory gdcDeploymentConfigFactory;
  private final PassthroughDeploymentConfigFactory passthroughDeploymentConfigFactory;

  @Inject
  public DeploymentConfigFactoryProvider(
      GdcDeploymentConfigFactory gdcDeploymentConfigFactory,
      PassthroughDeploymentConfigFactory passthroughDeploymentConfigFactory) {
    this.gdcDeploymentConfigFactory = gdcDeploymentConfigFactory;
    this.passthroughDeploymentConfigFactory = passthroughDeploymentConfigFactory;
  }

  public DeploymentConfigFactory getDeploymentConfigFactory(StreamingPipelineCategory category) {
    return this.getDeploymentConfigFactory(category.getDeploymentType());
  }

  public DeploymentConfigFactory getDeploymentConfigFactory(final DeploymentType type) {
    return switch (type) {
      case GDC_PIPELINE -> gdcDeploymentConfigFactory;
      case PASSTHROUGH_PIPELINE -> passthroughDeploymentConfigFactory;
    };
  }
}
