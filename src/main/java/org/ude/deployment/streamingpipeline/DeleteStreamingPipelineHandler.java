package org.ude.deployment.streamingpipeline;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.concurrent.CompletionStage;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.CommandProducer;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.streamingpipeline.dto.DeleteStreamingPipelineProperties;

@ApplicationScoped
public class DeleteStreamingPipelineHandler implements CommandHandler {
  private final StatusUpdateService statusUpdateService;
  private final CommandProducer commandProducer;
  private final ObjectMapper objectMapper;

  public DeleteStreamingPipelineHandler(
      StatusUpdateService statusUpdateService,
      CommandProducer commandProducer,
      ObjectMapper objectMapper) {
    this.statusUpdateService = statusUpdateService;
    this.commandProducer = commandProducer;
    this.objectMapper = objectMapper;
  }

  @Override
  public CompletionStage<Void> handle(Command command) {
    return commandProducer
        .sendCommand(constructDeletePipelineCommand(command))
        .exceptionallyCompose(
            throwable ->
                statusUpdateService.failed(
                    command,
                    CommandType.DELETE,
                    ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING,
                    throwable));
  }

  private Command constructDeletePipelineCommand(Command command) {
    var deleteStreamingPipelineProperties =
        objectMapper.convertValue(command.properties(), DeleteStreamingPipelineProperties.class);

    var originalDataOrderProperties =
        deleteStreamingPipelineProperties.deleteDataOrder()
            ? deleteStreamingPipelineProperties.originalDataOrderProperties()
            : null;

    return new Command(
        command.commandId(),
        CommandType.DELETE,
        command.objectId(),
        ConfigurationType.FLINK_JOB_PIPELINE,
        JsonObject.mapFrom(
            new DeleteStreamingPipelineProperties(
                deleteStreamingPipelineProperties.request(),
                deleteStreamingPipelineProperties.deleteDataOrder(),
                originalDataOrderProperties)));
  }
}
