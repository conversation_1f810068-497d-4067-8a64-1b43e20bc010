package org.ude.deployment.streamingpipeline;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import io.vertx.core.json.JsonArray;

public final class JsonStringifyUtils {

  public static String stringify(JsonArray array, String defaultValue)
      throws JsonProcessingException {
    if (array == null || array.isEmpty()) {
      return defaultValue;
    }
    return new ObjectMapper()
        .configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true)
        .writeValueAsString(array.getList());
  }
}
