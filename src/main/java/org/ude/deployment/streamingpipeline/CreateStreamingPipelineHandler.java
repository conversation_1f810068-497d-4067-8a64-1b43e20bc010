package org.ude.deployment.streamingpipeline;

import static org.ude.deployment.common.status.StatusUtils.toDataPlatformObjectProvisionException;
import static org.ude.deployment.common.status.StatusUtils.toDataPlatformObjectValidationException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.unchecked.Unchecked;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import lombok.RequiredArgsConstructor;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.exception.ConfigurationProcessingException.ConfigurationSerializationException;
import org.ude.deployment.common.infrastructure.kafka.CommandProducer;
import org.ude.deployment.common.infrastructure.kafka.KafkaConfig;
import org.ude.deployment.common.infrastructure.kafka.KafkaSchemaRegistryClient;
import org.ude.deployment.common.infrastructure.kafka.TopicService;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.flink.dto.DeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.RetentionPeriod;
import org.ude.deployment.streamingpipeline.config.factory.DeploymentConfigFactoryProvider;
import org.ude.deployment.streamingpipeline.dto.NewStreamingPipelineRequest;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineCategory;

@ApplicationScoped
@RequiredArgsConstructor
public class CreateStreamingPipelineHandler implements CommandHandler {
  public static final String DCOM_CLIENT_NAME = "DCOM";
  private static final CommandType COMMAND_TYPE = CommandType.CREATE;

  private final ObjectMapper objectMapper;
  private final DeploymentConfigFactoryProvider deploymentConfigFactoryProvider;
  private final CommandProducer commandProducer;
  private final StatusUpdateService statusUpdateService;
  private final KafkaConfig kafkaConfig;
  private final TopicService topicService;
  private final KafkaSchemaRegistryClient kafkaSchemaRegistryClient;

  @Override
  public CompletionStage<Void> handle(Command command) {
    Log.infof("Handling create streaming pipeline command: %s", command);
    var pipelineRequest = parseStreamingPipelineRequest(command.properties());
    return Uni.createFrom()
        .completionStage(validateDataOrderResources(pipelineRequest))
        .onFailure()
        .transform(
            e ->
                toDataPlatformObjectValidationException(
                    List.of(e.getMessage()), ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING))
        .chain(
            res ->
                Uni.createFrom()
                    .item(createDeploymentCommands(command.objectId(), pipelineRequest)))
        .chain(
            deploymentCommands ->
                Multi.createFrom()
                    .iterable(deploymentCommands)
                    .onItem()
                    .transformToUniAndMerge(
                        cmd -> Uni.createFrom().completionStage(sendCommand(cmd)))
                    .collect()
                    .asList())
        .replaceWithVoid()
        .onFailure()
        .transform(
            e ->
                toDataPlatformObjectProvisionException(
                    e, ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING))
        .onFailure()
        .recoverWithUni(
            throwable -> {
              var defaultProvisioningStep = ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING;
              return Uni.createFrom()
                  .completionStage(
                      statusUpdateService.failed(
                          command, COMMAND_TYPE, defaultProvisioningStep, throwable));
            })
        .subscribeAsCompletionStage();
  }

  private NewStreamingPipelineRequest parseStreamingPipelineRequest(JsonObject request) {
    return objectMapper.convertValue(request, NewStreamingPipelineRequest.class);
  }

  private List<Command> createDeploymentCommands(
      UUID objectId, NewStreamingPipelineRequest pipelineRequest)
      throws ConfigurationSerializationException {
    return pipelineRequest.mappings().stream()
        .flatMap(schemaDataOrderMapping -> schemaDataOrderMapping.dataOrders().stream())
        .map(
            dataOrderPropertiesConfig -> {
              try {
                return new Command(
                    UUID.randomUUID().toString(),
                    CommandType.CREATE,
                    objectId,
                    ConfigurationType.FLINK_JOB_PIPELINE,
                    objectMapper.convertValue(
                        createPipelineDeploymentConfig(
                            pipelineRequest.name(),
                            pipelineRequest.projectId(),
                            pipelineRequest.category(),
                            dataOrderPropertiesConfig),
                        JsonObject.class));
              } catch (JsonProcessingException e) {
                throw new ConfigurationSerializationException(e);
              }
            })
        .toList();
  }

  private DeploymentConfig createPipelineDeploymentConfig(
      String pipelineName,
      String projectId,
      StreamingPipelineCategory category,
      PipelineDataOrderPropertiesConfig dataOrderPropertiesConfig)
      throws JsonProcessingException {
    var factory = deploymentConfigFactoryProvider.getDeploymentConfigFactory(category);
    var baseDeploymentConfig = factory.createDeploymentConfig(dataOrderPropertiesConfig);

    return new DeploymentConfig(
        category.getDeploymentType(),
        dataOrderPropertiesConfig.dataOrderId(),
        pipelineName,
        projectId,
        RetentionPeriod.ofDays(
            Objects.nonNull(dataOrderPropertiesConfig.retentionPeriod())
                ? dataOrderPropertiesConfig.retentionPeriod()
                : 1),
        baseDeploymentConfig);
  }

  private CompletableFuture<Void> sendCommand(Command command) {
    return commandProducer.sendCommand(command);
  }

  private CompletableFuture<Void> validateDataOrderResources(
      NewStreamingPipelineRequest pipelineRequest) {
    if (isGdcPipeline(pipelineRequest.category()) && !hasStorageSchemaId(pipelineRequest)) {
      return CompletableFuture.failedFuture(
          new IllegalArgumentException(
              "Cannot create a pipeline of category CUSTOM / GDC for pipeline '%s' if less than 2 schema IDs are provided"
                  .formatted(pipelineRequest.name())));
    }

    if (!shouldValidateResources(pipelineRequest)) {
      return CompletableFuture.completedFuture(null);
    }

    Log.infof("Resources for %s pipeline will be validated", pipelineRequest.name());
    var dataOrderIds =
        pipelineRequest.mappings().stream()
            .flatMap(mapping -> mapping.dataOrders().stream())
            .map(PipelineDataOrderPropertiesConfig::dataOrderId)
            .toList();

    return Multi.createFrom()
        .iterable(dataOrderIds)
        .onItem()
        .transformToUniAndMerge(this::validateSingleDataOrder)
        .collect()
        .asList()
        .replaceWithVoid()
        .subscribeAsCompletionStage();
  }

  private boolean isGdcPipeline(StreamingPipelineCategory streamingPipelineCategory) {
    return streamingPipelineCategory.getDeploymentType().equals(DeploymentType.GDC_PIPELINE);
  }

  private static boolean hasStorageSchemaId(NewStreamingPipelineRequest pipelineRequest) {
    return pipelineRequest.mappings().stream()
        .anyMatch(
            mapping ->
                mapping.dataOrders() != null
                    && !mapping.dataOrders().isEmpty()
                    && mapping.dataOrders().stream()
                        .allMatch(
                            dataOrder ->
                                dataOrder.schemaIds() != null
                                    && dataOrder.schemaIds().size() >= 2));
  }

  private Uni<Void> validateSingleDataOrder(String dataOrderId) {
    var inputTopicName = kafkaConfig.getInputTopicName(dataOrderId);
    return validateTopicExistence(dataOrderId).chain(() -> validateTopicSchema(inputTopicName));
  }

  Uni<Void> validateTopicExistence(String dataOrderId) {
    var topics =
        List.of(
            kafkaConfig.getInputTopicName(dataOrderId),
            kafkaConfig.getOutputTopicName(dataOrderId),
            kafkaConfig.getNonRetryableDlqTopicName(dataOrderId));

    return Multi.createFrom()
        .iterable(topics)
        .onItem()
        .transformToUniAndMerge(
            topicName ->
                topicService
                    .getTopicDescriptions(List.of(topicName))
                    .onItem()
                    .transform(un -> topicName)
                    .onFailure()
                    .recoverWithItem(throwable -> null))
        .collect()
        .asList()
        .onItem()
        .invoke(
            topicNames -> {
              var missingTopics =
                  topics.stream()
                      .filter(topicName -> !topicNames.contains(topicName))
                      .sorted()
                      .toList();
              if (!missingTopics.isEmpty()) {
                throw new RuntimeException(
                    "Missing topics for dataOrderId "
                        + dataOrderId
                        + ": "
                        + String.join(", ", missingTopics));
              }
            })
        .replaceWithVoid();
  }

  private Uni<Void> validateTopicSchema(String topicName) {
    var subjectName = kafkaConfig.getTopicSchemaSubjectName(topicName);
    return Uni.createFrom()
        .item(Unchecked.supplier(() -> kafkaSchemaRegistryClient.doesSchemaExist(subjectName)))
        .onItem()
        .invoke(
            exists -> {
              if (!exists) {
                throw new RuntimeException("Schema %s does not exist".formatted(subjectName));
              }
            })
        .replaceWithVoid();
  }

  private boolean shouldValidateResources(NewStreamingPipelineRequest pipelineRequest) {
    return Optional.ofNullable(pipelineRequest.additionalProperties())
        .map(props -> props.getString("client"))
        .map(client -> client.equalsIgnoreCase(DCOM_CLIENT_NAME))
        .orElse(false);
  }
}
