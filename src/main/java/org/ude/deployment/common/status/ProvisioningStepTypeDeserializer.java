package org.ude.deployment.common.status;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStepType;

public class ProvisioningStepTypeDeserializer extends JsonDeserializer<ProvisioningStepType> {
  @Override
  public ProvisioningStepType deserialize(JsonParser p, DeserializationContext ctxt)
      throws IOException {
    var input = p.getText();

    for (var stepType : ProvisioningStepType.values()) {
      var normalizedStepValue = stepType.getValue().replaceAll("\\s+", "");
      if (stepType.getValue().equalsIgnoreCase(input)
          || normalizedStepValue.equalsIgnoreCase(input)) {
        return stepType;
      }
    }

    throw new IllegalArgumentException("Unknown provisioning step type: " + input);
  }
}
