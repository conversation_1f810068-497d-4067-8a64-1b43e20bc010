package org.ude.deployment.common.status;

import com.fasterxml.jackson.annotation.JsonValue;

public enum ProvisioningStatus {
  NOT_STARTED("Not Started"),
  PROVISIONING("Provisioning"),
  DELETING("Deleting"),
  FAILED("Failed"),
  COMPLETED("Completed"),
  DELETED("Deleted"),
  SKIPPED("Skipped");

  private final String value;

  ProvisioningStatus(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return value;
  }
}
