package org.ude.deployment.common.status;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;

public class ProvisioningStepDeserializer extends JsonDeserializer<ProvisioningStep> {
  @Override
  public ProvisioningStep deserialize(JsonParser p, DeserializationContext ctxt)
      throws IOException {
    var input = p.getText();

    for (var step : ProvisioningStep.values()) {
      var normalizedStepValue = step.getValue().replaceAll("\\s+", "");
      if (step.getValue().equalsIgnoreCase(input) || normalizedStepValue.equalsIgnoreCase(input)) {
        return step;
      }
    }

    throw new IllegalArgumentException("Unknown provisioning step: " + input);
  }
}
