package org.ude.deployment.common.status;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

public record DataPlatformObjectStatus(
    ProvisioningStep step,
    String stepId,
    ProvisioningStepType type,
    ProvisioningStatus status,
    OffsetDateTime timeStamp,
    String message,
    Object additionalInfo) {

  public static DataPlatformObjectStatus initialize(
      ProvisioningStep step, ProvisioningStepType type) {
    return DataPlatformObjectStatus.of(step, type, ProvisioningStatus.NOT_STARTED);
  }

  public static DataPlatformObjectStatus of(
      ProvisioningStep step, ProvisioningStepType type, ProvisioningStatus status) {
    return of(step, type, status, null);
  }

  public static DataPlatformObjectStatus of(
      ProvisioningStep step, ProvisioningStepType type, ProvisioningStatus status, String message) {
    return of(step, type, status, message, null);
  }

  public static DataPlatformObjectStatus of(
      ProvisioningStep step,
      ProvisioningStepType type,
      ProvisioningStatus status,
      String message,
      Object additionalInfo) {
    return new DataPlatformObjectStatus(
        step,
        UUID.randomUUID().toString(),
        type,
        status,
        OffsetDateTime.now(ZoneOffset.UTC),
        message,
        additionalInfo);
  }

  @JsonDeserialize(using = ProvisioningStepTypeDeserializer.class)
  public enum ProvisioningStepType {
    POSTGRES_DB_OBJECT("Postgres DB Object"),
    REPOSITORY("Repository"),
    INTERNAL_REPOSITORY("InternalRepository"),
    WORKBENCH("Workbench"),
    STREAMING_HOST("StreamingHost"),
    BATCH_PIPELINE("BatchPipeline"),
    PIPELINE_CHANNEL("Pipeline Channel"),
    SECURITY_GROUP("SecurityGroup"),
    DEPARTMENT("Department"),
    SCHEMA_REGISTRY("SchemaRegistry");

    private final String value;

    ProvisioningStepType(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return value;
    }
  }

  @JsonDeserialize(using = ProvisioningStepDeserializer.class)
  public enum ProvisioningStep {
    OBJECT_REGISTRATION("Object Registration"),
    SECURITY_GROUP_PROVISIONING("Security Group Provisioning"),
    KAFKA_TOPIC_PROVISIONING("Kafka Topic Provisioning"),
    ADLS_GEN2_PROVISIONING("ADLSGen2 Provisioning"),
    INTERNAL_REPOSITORY_PROVISIONING("Internal Repository Provisioning"),
    HISTORICAL_REPOSITORY_PROVISIONING("Historical Repository Provisioning"),
    WORKBENCH_PROVISIONING("Workbench Provisioning"),
    PIPELINE_CHANNEL_PROVISIONING("Pipeline Channel Provisioning"),
    ORCHESTRATOR_RESOURCE_GROUP_PROVISIONING("Orchestrator Resource Group Provisioning"),
    CUSTOMER_RESOURCE_GROUP_PROVISIONING("Customer Resource Group Provisioning"),
    VNET_PROVISIONING("VNet Provisioning"),
    VNET_DELETION("VNet Deletion"),
    SQL_DB_PROVISIONING("Sql DB Provisioning"),
    AZURE_FUNCTION_PROVISIONING("Azure Function Provisioning"),
    STREAMING_HOST_PROVISIONING("Streaming Host Provisioning"),
    UPDATE_PIPELINES_MAPPING_LIST("Update Pipelines Mapping List"),
    AZURE_DATA_FACTORY_PROVISIONING("AzureDataFactory Provisioning"),
    ORCHESTRATOR_AZURE_DATA_FACTORY_PROVISIONING("Orchestrator AzureDataFactory Provisioning"),
    CUSTOMER_AZURE_DATA_FACTORY_PROVISIONING("Customer AzureDataFactory Provisioning"),
    SPARK_JOB_PROVISIONING("Spark Job Provisioning"),
    BATCH_PIPELINE_PROVISIONING("BatchPipeline Provisioning"),
    KAFKA_TOPIC_CONFIGURATION_UPDATE("Kafka Topic Configuration Update"),
    KAFKA_TOPIC_DELETION("Kafka Topic Deletion"),
    STORAGE_DELETION("Storage Deletion"),
    SPARK_JOB_DELETION("Spark Job Deletion"),
    DEPARTMENT_DELETION("Department Deletion"),
    STAGING_REPOSITORY_DELETION("Staging Repository Deletion"),
    STAGING_REPOSITORY_PROVISIONING("Staging Repository Provisioning"),
    WORKBENCH_UNLINKING("Workbench Unlinking"),
    WORKBENCH_LINKING("Workbench Linking"),
    CONFIGURATION_OBJECT_UPDATE("Configuration Object Update"),
    SCHEMA_REGISTRY_PROVISIONING("Schema Registry Provisioning");

    private final String value;

    ProvisioningStep(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return value;
    }
  }
}
