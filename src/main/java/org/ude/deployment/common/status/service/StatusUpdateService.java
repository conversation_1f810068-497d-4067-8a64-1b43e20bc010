package org.ude.deployment.common.status.service;

import static org.ude.deployment.common.status.StatusUtils.createDataPlatformObjectStatus;
import static org.ude.deployment.common.status.StatusUtils.createDataPlatformObjectStatuses;

import io.quarkus.logging.Log;
import io.smallrye.mutiny.CompositeException;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletionStage;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectException;
import org.ude.deployment.common.infrastructure.kafka.StatusProducer;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.JobStatusContext;
import org.ude.deployment.common.status.ProvisioningStatus;
import org.ude.deployment.common.status.RequestJobStatus;
import org.ude.deployment.common.status.Status;

@ApplicationScoped
public class StatusUpdateService {
  private final StatusProducer statusProducer;

  @Inject
  public StatusUpdateService(StatusProducer statusProducer) {
    this.statusProducer = statusProducer;
  }

  private CompletionStage<Void> updateProvisioningStatus(
      Command originalCommand,
      CommandType commandType,
      List<DataPlatformObjectStatus> steps,
      JsonObject result,
      List<String> errors) {
    JobStatusContext jobStatusContext;
    try {
      jobStatusContext = JobStatusContext.fromCommandProperties(originalCommand);
    } catch (IllegalArgumentException e) {
      Log.errorf(
          e,
          "While deserializing 'dataPlatformObjectStatus': Failed to update status for command %s",
          originalCommand);
      originalCommand.properties().remove("dataPlatformObjectStatus");

      return failed(
              originalCommand,
              commandType,
              steps.stream().map(DataPlatformObjectStatus::step).toList(),
              e)
          .thenAccept(v -> Log.info("Emitted failed status update"));
    }

    var updatedJobStatusContext = updateJobStatusContext(commandType, steps, jobStatusContext);

    updatedJobStatusContext.saveToCommand(originalCommand);

    return updateProvisioningStatus(
        originalCommand, updatedJobStatusContext.requestJobStatus(), result, errors);
  }

  private static @NotNull JobStatusContext updateJobStatusContext(
      CommandType commandType,
      List<DataPlatformObjectStatus> steps,
      JobStatusContext jobStatusContext) {
    jobStatusContext.mergeWith(steps);

    return jobStatusContext.withUpdatedStatusFor(commandType);
  }

  // TODO: remove this temporary method - it is only used to prevent breaking the Management API
  // with
  // the new Status schema
  public CompletionStage<Void> updateProvisioningStatus(
      Command originalCommand, RequestJobStatus status) {
    return updateProvisioningStatus(originalCommand, status, null, List.of());
  }

  public CompletionStage<Void> updateProvisioningStatus(
      Command originalCommand, RequestJobStatus status, JsonObject result, List<String> errors) {
    Log.infof(
        "Creating status update for command ID '%s' with status '%s'",
        originalCommand.commandId(), status);

    var statusUpdate =
        new Status(
            originalCommand.commandId(),
            originalCommand.commandType(),
            status,
            originalCommand.objectId(),
            originalCommand.type(),
            originalCommand.properties(),
            result,
            errors);
    return statusProducer.sendStatusUpdate(statusUpdate);
  }

  public CompletionStage<Void> failed(
      final Command cmd,
      final CommandType commandType,
      final List<ProvisioningStep> defaultProvisioningSteps,
      final Throwable throwable) {
    Log.errorf(throwable, "Failed to process command %s:", cmd);
    var stepsFromException = extractFailedProvisioningSteps(throwable);

    return this.updateProvisioningStatus(
        cmd,
        commandType,
        createDataPlatformObjectStatuses(
            stepsFromException.isEmpty() ? defaultProvisioningSteps : stepsFromException,
            ProvisioningStatus.FAILED,
            throwable.getMessage()),
        null,
        List.of(throwable.getMessage()));
  }

  public CompletionStage<Void> failed(
      final Command cmd,
      final CommandType commandType,
      final ProvisioningStep defaultProvisioningStep,
      final Throwable throwable) {
    Log.errorf(throwable, "Failed to process command %s:", cmd);
    var stepsFromException = extractFailedProvisioningSteps(throwable);

    return this.updateProvisioningStatus(
        cmd,
        commandType,
        createDataPlatformObjectStatuses(
            defaultProvisioningStep == null ? stepsFromException : List.of(defaultProvisioningStep),
            ProvisioningStatus.FAILED,
            throwable.getMessage()),
        null,
        List.of(throwable.getMessage()));
  }

  private static List<ProvisioningStep> extractFailedProvisioningSteps(Throwable throwable) {
    var steps = new ArrayList<ProvisioningStep>();
    if (throwable instanceof CompositeException compositeException) {
      compositeException
          .getCauses()
          .forEach(cause -> steps.addAll(extractStepsFromThrowable(cause)));
    } else {
      steps.addAll(extractStepsFromThrowable(throwable));
    }
    return steps;
  }

  private static List<ProvisioningStep> extractStepsFromThrowable(Throwable throwable) {
    if (throwable instanceof DataPlatformObjectException dpoException) {
      return dpoException.getProvisioningSteps();
    }
    return List.of();
  }

  public CompletionStage<Void> initializeWithCompletedObjectRegistration(
      final Command cmd,
      final CommandType commandType,
      final List<ProvisioningStep> provisioningSteps) {
    return initializeWithCompletedSteps(
        cmd, commandType, List.of(ProvisioningStep.OBJECT_REGISTRATION), provisioningSteps);
  }

  public CompletionStage<Void> initializeWithCompletedSteps(
      final Command cmd,
      final CommandType commandType,
      final List<ProvisioningStep> completedSteps,
      final List<ProvisioningStep> notStartedSteps) {
    var dataPlatformObjectStatuses = new ArrayList<DataPlatformObjectStatus>();
    var completedDpo =
        createDataPlatformObjectStatuses(completedSteps, ProvisioningStatus.COMPLETED);
    var notStartedDpos =
        createDataPlatformObjectStatuses(notStartedSteps, ProvisioningStatus.NOT_STARTED);
    dataPlatformObjectStatuses.addAll(completedDpo);
    dataPlatformObjectStatuses.addAll(notStartedDpos);
    return this.updateProvisioningStatus(
        cmd, commandType, dataPlatformObjectStatuses, null, List.of());
  }

  public CompletionStage<Void> provisioning(
      final Command cmd, final CommandType commandType, final ProvisioningStep provisioningStep) {
    return this.updateProvisioningStatus(
        cmd,
        commandType,
        createDataPlatformObjectStatuses(provisioningStep, ProvisioningStatus.PROVISIONING),
        null,
        List.of());
  }

  public CompletionStage<Void> provisioning(
      final Command cmd,
      final CommandType commandType,
      final List<ProvisioningStep> provisioningSteps) {
    return this.updateProvisioningStatus(
        cmd,
        commandType,
        createDataPlatformObjectStatuses(provisioningSteps, ProvisioningStatus.PROVISIONING),
        null,
        List.of());
  }

  public CompletionStage<Void> completed(
      final Command cmd,
      final CommandType commandType,
      final ProvisioningStep provisioningStep,
      final Object result) {
    return this.updateProvisioningStatus(
        cmd,
        commandType,
        createDataPlatformObjectStatuses(provisioningStep, ProvisioningStatus.COMPLETED, result),
        JsonObject.mapFrom(result),
        List.of());
  }

  public CompletionStage<Void> completed(
      final Command cmd,
      final CommandType commandType,
      final List<ProvisioningStep> provisioningSteps,
      final Object result) {
    return this.updateProvisioningStatus(
        cmd,
        commandType,
        createDataPlatformObjectStatuses(provisioningSteps, ProvisioningStatus.COMPLETED, result),
        JsonObject.mapFrom(result),
        List.of());
  }

  public CompletionStage<Void> completed(
      final Command cmd,
      final CommandType commandType,
      final List<ProvisioningStep> provisioningSteps,
      final ProvisioningStep provisioningStepToHoldResult,
      final Object result) {
    var dataPlatformObjectStatuses =
        new ArrayList<>(
            createDataPlatformObjectStatuses(provisioningSteps, ProvisioningStatus.COMPLETED));
    dataPlatformObjectStatuses.add(
        createDataPlatformObjectStatus(
            provisioningStepToHoldResult, ProvisioningStatus.COMPLETED, null, result));
    return this.updateProvisioningStatus(
        cmd, commandType, dataPlatformObjectStatuses, JsonObject.mapFrom(result), List.of());
  }

  public CompletionStage<Void> deleting(
      final Command cmd, final CommandType commandType, final ProvisioningStep provisioningStep) {
    return this.updateProvisioningStatus(
        cmd,
        commandType,
        createDataPlatformObjectStatuses(provisioningStep, ProvisioningStatus.DELETING),
        null,
        List.of());
  }

  public CompletionStage<Void> deleted(
      final Command cmd, final CommandType commandType, final ProvisioningStep provisioningStep) {
    return this.updateProvisioningStatus(
        cmd,
        commandType,
        createDataPlatformObjectStatuses(provisioningStep, ProvisioningStatus.DELETED),
        null,
        List.of());
  }

  public CompletionStage<Void> deleted(
      final Command cmd,
      final CommandType commandType,
      final ProvisioningStep provisioningStep,
      final Object result) {
    return this.updateProvisioningStatus(
        cmd,
        commandType,
        createDataPlatformObjectStatuses(provisioningStep, ProvisioningStatus.DELETED, result),
        JsonObject.mapFrom(result),
        List.of());
  }
}
