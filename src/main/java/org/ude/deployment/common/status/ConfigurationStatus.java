package org.ude.deployment.common.status;

import com.fasterxml.jackson.annotation.JsonValue;

public enum ConfigurationStatus {
  IN_PROVISIONING("InProvisioning"),
  ACTIVE("Active"),
  DISABLED("Disabled"),
  DELETED("Deleted");

  private final String value;

  ConfigurationStatus(String value) {
    this.value = value;
  }

  public static ConfigurationStatus fromValue(String value) {
    for (ConfigurationStatus e : ConfigurationStatus.values()) {
      if (e.value.equals(value) || e.name().equals(value)) {
        return e;
      }
    }
    return ConfigurationStatus.IN_PROVISIONING;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return value;
  }
}
