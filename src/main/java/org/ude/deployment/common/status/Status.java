package org.ude.deployment.common.status;

import io.vertx.core.json.JsonObject;
import java.util.List;
import java.util.UUID;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.domain.ConfigurationType;

public record Status(
    String commandId,
    CommandType commandType,
    RequestJobStatus status,
    UUID objectId,
    ConfigurationType type,
    JsonObject properties,
    JsonObject result,
    List<String> errors) {}
