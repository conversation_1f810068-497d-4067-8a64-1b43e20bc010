package org.ude.deployment.common.status;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.NonNull;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectException;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectProvisionException;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectValidationException;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStepType;

public class StatusUtils {
  public static final Map<ProvisioningStep, ProvisioningStepType> PROVISIONING_STEP_TO_TYPE_MAP =
      new HashMap<>() {
        {
          put(ProvisioningStep.OBJECT_REGISTRATION, ProvisioningStepType.POSTGRES_DB_OBJECT);
          put(ProvisioningStep.KAFKA_TOPIC_PROVISIONING, ProvisioningStepType.REPOSITORY);
          put(ProvisioningStep.KAFKA_TOPIC_CONFIGURATION_UPDATE, ProvisioningStepType.REPOSITORY);
          put(ProvisioningStep.KAFKA_TOPIC_DELETION, ProvisioningStepType.REPOSITORY);
          put(ProvisioningStep.STORAGE_DELETION, ProvisioningStepType.REPOSITORY);
          put(ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING, ProvisioningStepType.REPOSITORY);
          put(ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING, ProvisioningStepType.SCHEMA_REGISTRY);
          put(ProvisioningStep.SECURITY_GROUP_PROVISIONING, ProvisioningStepType.SECURITY_GROUP);
          put(
              ProvisioningStep.CONFIGURATION_OBJECT_UPDATE,
              ProvisioningStepType.POSTGRES_DB_OBJECT);
          put(
              ProvisioningStep.UPDATE_PIPELINES_MAPPING_LIST,
              ProvisioningStepType.POSTGRES_DB_OBJECT);
        }
      };

  public static @NonNull DataPlatformObjectException toDataPlatformObjectProvisionException(
      Throwable throwable, List<ProvisioningStep> steps) {
    return throwable instanceof DataPlatformObjectException e
        ? e
        : new DataPlatformObjectProvisionException(steps, throwable);
  }

  public static @NonNull DataPlatformObjectException toDataPlatformObjectProvisionException(
      Throwable throwable, ProvisioningStep step) {
    return throwable instanceof DataPlatformObjectException e
        ? e
        : new DataPlatformObjectProvisionException(List.of(step), throwable);
  }

  public static @NonNull DataPlatformObjectValidationException
      toDataPlatformObjectValidationException(List<String> errors, ProvisioningStep step) {
    return new DataPlatformObjectValidationException(errors, List.of(step));
  }

  public static @NonNull List<DataPlatformObjectStatus> createDataPlatformObjectStatuses(
      List<ProvisioningStep> steps, ProvisioningStatus status, String message) {
    return steps.stream()
        .map(step -> createDataPlatformObjectStatus(step, status, message, null))
        .toList();
  }

  public static @NonNull List<DataPlatformObjectStatus> createDataPlatformObjectStatuses(
      List<ProvisioningStep> steps, ProvisioningStatus status, Object additionalInfo) {
    return steps.stream()
        .map(step -> createDataPlatformObjectStatus(step, status, null, additionalInfo))
        .toList();
  }

  public static @NonNull List<DataPlatformObjectStatus> createDataPlatformObjectStatuses(
      List<ProvisioningStep> steps, ProvisioningStatus status) {
    return steps.stream()
        .map(step -> createDataPlatformObjectStatus(step, status, null, null))
        .toList();
  }

  public static @NonNull List<DataPlatformObjectStatus> createDataPlatformObjectStatuses(
      ProvisioningStep step, ProvisioningStatus status, String message) {
    return List.of(createDataPlatformObjectStatus(step, status, message, null));
  }

  public static @NonNull List<DataPlatformObjectStatus> createDataPlatformObjectStatuses(
      ProvisioningStep step, ProvisioningStatus status) {
    return createDataPlatformObjectStatuses(step, status, null);
  }

  public static @NonNull List<DataPlatformObjectStatus> createDataPlatformObjectStatuses(
      ProvisioningStep step, ProvisioningStatus status, Object additionalInfo) {
    return List.of(createDataPlatformObjectStatus(step, status, null, additionalInfo));
  }

  public static @NonNull DataPlatformObjectStatus createDataPlatformObjectStatus(
      ProvisioningStep step, ProvisioningStatus status, String message, Object additionalInfo) {
    return Optional.ofNullable(PROVISIONING_STEP_TO_TYPE_MAP.get(step))
        .map(type -> DataPlatformObjectStatus.of(step, type, status, message, additionalInfo))
        .orElseThrow(getDpoStepMissingExceptionSupplier(step));
  }

  private static @NonNull Supplier<DataPlatformObjectProvisionException>
      getDpoStepMissingExceptionSupplier(ProvisioningStep step) {
    return () ->
        new DataPlatformObjectProvisionException(
            List.of("Unknown provisioning step '%s'".formatted(step)),
            List.of(ProvisioningStep.OBJECT_REGISTRATION));
  }
}
