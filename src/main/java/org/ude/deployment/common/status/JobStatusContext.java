package org.ude.deployment.common.status;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;

public record JobStatusContext(
    List<DataPlatformObjectStatus> dpoStatuses,
    ConfigurationStatus configurationStatus,
    RequestJobStatus requestJobStatus) {
  public static JobStatusContext fromCommandProperties(Command command) {
    var props = command.properties();
    return new JobStatusContext(
        Optional.ofNullable(props.getJsonArray("dataPlatformObjectStatus"))
            .map(
                jsonArray ->
                    new ObjectMapper()
                        .registerModule(new JavaTimeModule())
                        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                        .convertValue(
                            jsonArray.getList(),
                            new TypeReference<List<DataPlatformObjectStatus>>() {}))
            .orElseGet(ArrayList::new),
        Optional.ofNullable(props.getString("configurationStatus"))
            .map(ConfigurationStatus::fromValue)
            .orElse(ConfigurationStatus.IN_PROVISIONING),
        RequestJobStatus.SUBMITTED);
  }

  public void mergeWith(List<DataPlatformObjectStatus> toMerge) {
    var existingStatus = new ArrayList<>(dpoStatuses());
    dpoStatuses.clear();
    dpoStatuses.addAll(JobStateUtils.mergeDataPlatformObjectStatus(existingStatus, toMerge));
  }

  public JobStatusContext withUpdatedStatusFor(CommandType targetStatus) {
    var calculatedJobStatus = JobStateUtils.calculateJobStatus(dpoStatuses, targetStatus);
    return new JobStatusContext(
        dpoStatuses,
        JobStateUtils.determineConfigurationStatus(calculatedJobStatus, targetStatus),
        calculatedJobStatus);
  }

  public void saveToCommand(Command command) {
    command.properties().put("dataPlatformObjectStatus", dpoStatuses);
    command.properties().put("configurationStatus", configurationStatus);
  }
}
