package org.ude.deployment.common.status;

import com.fasterxml.jackson.annotation.JsonValue;

public enum RequestJobStatus {
  SUBMITTED("Submitted"),
  PROVISIONING("Provisioning"),
  FAILED("Failed"),
  PARTIALLY_FAILED("Partially Failed"),
  COMPLETED("Completed"),
  DELETING("Deleting"),
  DELETED("Deleted"),
  IN_PROGRESS("InProgress"),
  CANCELLED("Cancelled");

  @JsonValue private final String value;

  RequestJobStatus(String value) {
    this.value = value;
  }

  @Override
  public String toString() {
    return value;
  }
}
