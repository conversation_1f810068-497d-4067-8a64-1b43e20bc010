package org.ude.deployment.common.status;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStepType;

public class JobStateUtils {
  public static List<DataPlatformObjectStatus> mergeDataPlatformObjectStatus(
      List<DataPlatformObjectStatus> existingSteps,
      List<DataPlatformObjectStatus> newStepsToUpdate) {
    if (newStepsToUpdate == null || newStepsToUpdate.isEmpty()) {
      return existingSteps;
    }

    var stepToStatusMap =
        existingSteps.stream()
            .collect(Collectors.toMap(DataPlatformObjectStatus::step, dpoStatus -> dpoStatus));

    newStepsToUpdate.forEach(newStep -> stepToStatusMap.put(newStep.step(), newStep));

    return new ArrayList<>(stepToStatusMap.values());
  }

  public static ConfigurationStatus determineConfigurationStatus(
      RequestJobStatus jobStatus, CommandType commandType) {
    return switch (jobStatus) {
      case COMPLETED ->
          commandType.equals(CommandType.CREATE) || commandType.equals(CommandType.UPDATE)
              ? ConfigurationStatus.ACTIVE
              : ConfigurationStatus.DISABLED;
      case FAILED, PARTIALLY_FAILED -> ConfigurationStatus.DISABLED;
      case DELETED ->
          commandType.equals(CommandType.DELETE)
              ? ConfigurationStatus.DELETED
              : ConfigurationStatus.DISABLED;
      default -> ConfigurationStatus.IN_PROVISIONING;
    };
  }

  /**
   * Calculate the overall job status
   *
   * @param dpoStatus The list of data platform object statuses for the request
   * @param commandType The type of the overall provisioning request (e.g., is it provisioning, i.e.
   *     CREATE / UPDATE, or deleting, i.e. DELETE)
   * @return The overall status of the request
   */
  public static RequestJobStatus calculateJobStatus(
      List<DataPlatformObjectStatus> dpoStatus, CommandType commandType) {
    var jobStatus = calculateJobStatus(dpoStatus);
    var targetStatus = getTargetJobStatus(commandType);

    if (jobStatus.equals(RequestJobStatus.COMPLETED)
        && targetStatus.equals(RequestJobStatus.DELETED)) {
      return RequestJobStatus.DELETED;
    }

    return jobStatus;
  }

  static RequestJobStatus getTargetJobStatus(CommandType targetStatus) {
    return targetStatus == null || targetStatus.equals(CommandType.DELETE)
        ? RequestJobStatus.DELETED
        : RequestJobStatus.COMPLETED;
  }

  private static RequestJobStatus calculateJobStatus(List<DataPlatformObjectStatus> steps) {
    if (steps == null || steps.isEmpty()) {
      return RequestJobStatus.SUBMITTED; // TODO: Consider creating UNKNOWN as a new status
    }

    var acc = new JobStatusAccumulator();
    for (var step : steps) {
      acc.accumulate(step);
    }

    if (acc.hasFailed && !acc.someCompleted) return RequestJobStatus.FAILED;
    if (acc.allCompleted) return RequestJobStatus.COMPLETED;
    if (acc.isProvisioning || (!acc.hasFailed && acc.someHaveNotStarted))
      return RequestJobStatus.PROVISIONING;
    if (acc.isDeleting) return RequestJobStatus.DELETING;
    if (!acc.hasFailed && !acc.someCompleted) return RequestJobStatus.SUBMITTED;

    return RequestJobStatus.PARTIALLY_FAILED;
  }

  static class JobStatusAccumulator {
    boolean hasFailed = false;
    boolean allCompleted = true;
    boolean someCompleted = false;
    boolean isProvisioning = false;
    boolean isDeleting = false;
    boolean someHaveNotStarted = false;

    void accumulate(DataPlatformObjectStatus dpoStatus) {
      var status = dpoStatus.status();
      if (status.equals(ProvisioningStatus.FAILED)) {
        hasFailed = true;
        allCompleted = false;
      } else if (status.equals(ProvisioningStatus.COMPLETED)
          || status.equals(ProvisioningStatus.DELETED)
          || status.equals(ProvisioningStatus.SKIPPED)) {
        someCompleted |= !dpoStatus.type().equals(ProvisioningStepType.POSTGRES_DB_OBJECT);
      } else if (status.equals(ProvisioningStatus.PROVISIONING)) {
        isProvisioning = true;
        allCompleted = false;
      } else if (status.equals(ProvisioningStatus.DELETING)) {
        isDeleting = true;
        allCompleted = false;
      } else if (status.equals(ProvisioningStatus.NOT_STARTED)) {
        someHaveNotStarted = true;
        allCompleted = false;
      } else {
        allCompleted = false;
      }
    }
  }
}
