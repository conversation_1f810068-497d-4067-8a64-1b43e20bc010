package org.ude.deployment.common.util;

import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class HttpUtils {

  public static Map<String, String> parseQueryParams(String url) {
    String query = URI.create(url).getQuery(); // Extract query string

    if (query == null || query.isEmpty()) {
      return Collections.emptyMap(); // Return empty map if no query parameters exist
    }

    return Arrays.stream(query.split("&"))
        .map(param -> param.split("=", 2)) // Split into key-value pairs
        .collect(
            Collectors.groupingBy(
                kv -> decodeQueryParam(kv[0]), // Decode key
                LinkedHashMap::new, // Preserve order
                Collectors.mapping(
                    kv -> kv.length > 1 ? decodeQueryParam(kv[1]) : "",
                    Collectors.joining(",")) // Decode values
                ));
  }

  private static String decodeQueryParam(String value) {
    return URLDecoder.decode(value, StandardCharsets.UTF_8);
  }
}
