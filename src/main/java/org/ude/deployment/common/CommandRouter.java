package org.ude.deployment.common;

import static org.ude.deployment.common.infrastructure.kafka.util.Constants.COMMAND_HANDLER_NOT_IMPLEMENTED;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.logging.Log;
import io.smallrye.reactive.messaging.kafka.api.OutgoingKafkaRecordMetadata;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.concurrent.CompletionStage;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.reactive.messaging.Message;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.infrastructure.exception.CommandRouterException.MessageSerializationException;
import org.ude.deployment.common.infrastructure.kafka.DeadLetterProducer;
import org.ude.deployment.common.infrastructure.kafka.util.MessageMetadata;

@ApplicationScoped
public class CommandRouter {
  private final CommandHandlerFactory commandHandlerFactory;
  private final DeadLetterProducer deadLetterProducer;
  private final ObjectMapper objectMapper;

  @ConfigProperty(name = "mp.messaging.incoming.commands.topic")
  String commandsTopic;

  @Inject
  public CommandRouter(
      CommandHandlerFactory commandHandlerFactory,
      DeadLetterProducer deadLetterProducer,
      ObjectMapper objectMapper) {
    this.commandHandlerFactory = commandHandlerFactory;
    this.deadLetterProducer = deadLetterProducer;
    this.objectMapper = objectMapper;
  }

  public CompletionStage<Void> route(Command command) {
    Log.info(
        "Received command to be routed based on Configuration Type: '%s' and Command Type: '%s'"
            .formatted(command.type(), command.commandType()));

    var optionalHandler = commandHandlerFactory.getHandler(command.type(), command.commandType());
    return optionalHandler
        .map(handler -> handler.handle(command))
        .orElseGet(() -> sendDeadLetterMessage(command));
  }

  private CompletionStage<Void> sendDeadLetterMessage(Command command)
      throws MessageSerializationException {

    final var msg =
        "Command handler for command type '%s' and configuration type '%s' is not implemented."
            .formatted(command.commandType(), command.type());

    Log.warn(msg);

    byte[] serializedCommand;
    try {
      serializedCommand = objectMapper.writeValueAsBytes(command);
    } catch (JsonProcessingException e) {
      throw new MessageSerializationException(e);
    }
    var metadata =
        OutgoingKafkaRecordMetadata.<String>builder()
            .withHeaders(
                MessageMetadata.deadLetterHeaders(
                    commandsTopic, false, COMMAND_HANDLER_NOT_IMPLEMENTED, msg))
            .build();
    return deadLetterProducer.sendMessage(
        Message.of(serializedCommand).addMetadata(metadata), commandsTopic);
  }
}
