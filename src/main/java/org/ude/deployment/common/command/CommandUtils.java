package org.ude.deployment.common.command;

import java.util.UUID;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.common.domain.ConfigurationType;

public class CommandUtils {

  public static @NotNull Command mapToType(Command originalCommand, ConfigurationType type) {
    return mapToTypeWithNewObjectId(originalCommand, type, null);
  }

  public static @NotNull Command mapToTypeWithNewObjectId(
      Command originalCommand, ConfigurationType type, UUID objectId) {
    return new Command(
        originalCommand.commandId(),
        originalCommand.commandType(),
        objectId != null ? objectId : originalCommand.objectId(),
        type,
        originalCommand.properties());
  }
}
