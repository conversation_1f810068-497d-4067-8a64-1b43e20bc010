package org.ude.deployment.common.command;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.vertx.core.json.JsonObject;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.UUID;
import lombok.Builder;
import org.ude.deployment.common.domain.ConfigurationType;

@Builder
public record Command(
    @NotBlank(message = "commandId is mandatory") String commandId,
    @NotNull(message = "commandType is mandatory") CommandType commandType,
    UUID objectId,
    @NotNull(message = "type is mandatory") ConfigurationType type,
    @NotNull(message = "properties is mandatory") JsonObject properties) {
  public <T> T convertTo(final ObjectMapper objectMapper, final Class<T> clazz) {
    return objectMapper.convertValue(this.properties(), clazz);
  }
}
