package org.ude.deployment.common;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.Map;
import java.util.Optional;
import org.ude.deployment.azureblobstorage.handler.CreateAzureBlobStorageHandler;
import org.ude.deployment.azureblobstorage.handler.DeleteAzureBlobStorageHandler;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.dao.CreateDaoHandler;
import org.ude.deployment.dao.DeleteDaoHandler;
import org.ude.deployment.dataorder.CreateDataOrderHandler;
import org.ude.deployment.dataorder.DeleteDataOrderHandler;
import org.ude.deployment.dataorder.DeleteDataOrderTopicsHandler;
import org.ude.deployment.dataorder.UpdateDataOrderHandler;
import org.ude.deployment.flink.CreatePipelineHandler;
import org.ude.deployment.flink.DeletePipelineHandler;
import org.ude.deployment.flink.UpdatePipelineHandler;
import org.ude.deployment.project.CreateProjectHandler;
import org.ude.deployment.schema.CreateSchemaHandler;
import org.ude.deployment.schema.UpdateSchemaHandler;
import org.ude.deployment.streamingpipeline.CreateStreamingPipelineHandler;
import org.ude.deployment.streamingpipeline.DeleteStreamingPipelineHandler;

@ApplicationScoped
public class CommandHandlerFactory {

  private final Map<ConfigurationType, Map<CommandType, CommandHandler>> commandHandlerMap;

  @Inject
  public CommandHandlerFactory(
      CreateProjectHandler createProjectHandler,
      CreateSchemaHandler createSchemaHandler,
      UpdateSchemaHandler updateSchemaHandler,
      CreateDataOrderHandler createDataOrderHandler,
      CreateStreamingPipelineHandler createStreamingPipelineHandler,
      CreatePipelineHandler createPipelineHandler,
      UpdatePipelineHandler updatePipelineHandler,
      DeletePipelineHandler deletePipelineHandler,
      UpdateDataOrderHandler updateDataOrderHandler,
      CreateDaoHandler createDaoHandler,
      DeleteDaoHandler deleteDaoHandler,
      DeleteDataOrderHandler deleteDataOrderHandler,
      DeleteDataOrderTopicsHandler deleteDataOrderTopicsHandler,
      DeleteStreamingPipelineHandler deleteStreamingPipelineHandler,
      CreateAzureBlobStorageHandler createAzureBlobStorageHandler,
      DeleteAzureBlobStorageHandler deleteAzureBlobStorageHandler) {
    commandHandlerMap =
        buildCommandHandlerMap(
            createProjectHandler,
            createSchemaHandler,
            updateSchemaHandler,
            createDataOrderHandler,
            updateDataOrderHandler,
            createStreamingPipelineHandler,
            createPipelineHandler,
            updatePipelineHandler,
            deletePipelineHandler,
            createDaoHandler,
            deleteDaoHandler,
            deleteDataOrderHandler,
            deleteDataOrderTopicsHandler,
            deleteStreamingPipelineHandler,
            createAzureBlobStorageHandler,
            deleteAzureBlobStorageHandler);
  }

  private Map<ConfigurationType, Map<CommandType, CommandHandler>> buildCommandHandlerMap(
      CreateProjectHandler createProjectHandler,
      CreateSchemaHandler createSchemaHandler,
      UpdateSchemaHandler updateSchemaHandler,
      CreateDataOrderHandler createDataOrderHandler,
      UpdateDataOrderHandler updateDataOrderHandler,
      CreateStreamingPipelineHandler createStreamingPipelineHandler,
      CreatePipelineHandler createPipelineHandler,
      UpdatePipelineHandler updatePipelineHandler,
      DeletePipelineHandler deletePipelineHandler,
      CreateDaoHandler createDaoHandler,
      DeleteDaoHandler deleteDaoHandler,
      DeleteDataOrderHandler deleteDataOrderHandler,
      DeleteDataOrderTopicsHandler deleteTopicHandler,
      DeleteStreamingPipelineHandler deleteStreamingPipelineHandler,
      CreateAzureBlobStorageHandler createAzureBlobStorageHandler,
      DeleteAzureBlobStorageHandler deleteAzureBlobStorageHandler) {

    var dataOrderHandlers =
        Map.of(
            CommandType.CREATE,
            createDataOrderHandler,
            CommandType.UPDATE,
            updateDataOrderHandler,
            CommandType.DELETE,
            deleteDataOrderHandler);

    var pipelineHandlers =
        Map.of(
            CommandType.CREATE, createPipelineHandler,
            CommandType.UPDATE, updatePipelineHandler,
            CommandType.DELETE, deletePipelineHandler);

    var streamingPipelineHandlers =
        Map.of(
            CommandType.CREATE,
            createStreamingPipelineHandler,
            CommandType.DELETE,
            deleteStreamingPipelineHandler);

    return Map.of(
        ConfigurationType.DEPARTMENT,
        Map.of(CommandType.CREATE, createProjectHandler),
        ConfigurationType.PROJECT,
        Map.of(CommandType.CREATE, createProjectHandler),
        ConfigurationType.SCHEMA,
        Map.of(
            CommandType.CREATE, createSchemaHandler,
            CommandType.UPDATE, updateSchemaHandler),
        ConfigurationType.USE_CASE,
        dataOrderHandlers,
        ConfigurationType.DATA_ORDER,
        dataOrderHandlers,
        ConfigurationType.STREAMING_PIPELINE,
        streamingPipelineHandlers,
        ConfigurationType.FLINK_JOB_PIPELINE,
        pipelineHandlers,
        ConfigurationType.DATA_ACCESS_OBJECT,
        Map.of(
            CommandType.CREATE, createDaoHandler,
            CommandType.DELETE, deleteDaoHandler),
        ConfigurationType.DATA_ORDER_TOPIC,
        Map.of(CommandType.DELETE, deleteTopicHandler),
        ConfigurationType.AZURE_BLOB_STORAGE,
        Map.of(
            CommandType.CREATE,
            createAzureBlobStorageHandler,
            CommandType.DELETE,
            deleteAzureBlobStorageHandler));
  }

  public Optional<CommandHandler> getHandler(
      ConfigurationType configurationType, CommandType commandType) {
    return Optional.ofNullable(commandHandlerMap.get(configurationType))
        .map(m -> m.get(commandType));
  }
}
