package org.ude.deployment.common.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

public enum ConfigurationType {
  @JsonProperty("AnonymizedMethod")
  ANONYMIZED_METHOD("AnonymizedMethod"),

  @Deprecated
  @<PERSON>son<PERSON>roperty("Department")
  DEPARTMENT("Department"),

  @JsonProperty("Project")
  PROJECT("Project"),

  @JsonProperty("BatchConfiguration")
  BATCH_CONFIGURATION("BatchConfiguration"),

  @JsonProperty("BatchPipeline")
  BATCH_PIPELINE("BatchPipeline"),

  @Deprecated
  @JsonProperty("UseCase")
  USE_CASE("UseCase"),

  @JsonProperty("DataOrder")
  DATA_ORDER("DataOrder"),

  @JsonProperty("Schema")
  SCHEMA("Schema"),

  @JsonProperty("Storage")
  STORAGE("Storage"),

  @JsonProperty("StreamingPipeline")
  STREAMING_PIPELINE("StreamingPipeline"),

  @JsonProperty("FlinkJobPipeline")
  FLINK_JOB_PIPELINE("FlinkJobPipeline"),

  @JsonProperty("PipelineChannelGroup")
  PIPELINE_CHANNEL_GROUP("PipelineChannelGroup"),

  @JsonProperty("PipelineMap")
  PIPELINE_MAP("PipelineMap"),

  @JsonProperty("ReceptorMessageFilter")
  RECEPTOR_MESSAGE_FILTER("ReceptorMessageFilter"),

  @JsonProperty("DistributorMessageFilter")
  DISTRIBUTOR_MESSAGE_FILTER("DistributorMessageFilter"),

  @JsonProperty("Workbench")
  WORKBENCH("Workbench"),

  @JsonProperty("WorkbenchTemplate")
  WORKBENCH_TEMPLATE("WorkbenchTemplate"),

  @JsonProperty("StreamingHostTemplate")
  STREAMING_HOST_TEMPLATE("StreamingHostTemplate"),

  @JsonProperty("InternalRepositoryGroup")
  INTERNAL_REPOSITORY_GROUP("InternalRepositoryGroup"),

  @JsonProperty("ColdRepositoryGroup")
  COLD_REPOSITORY_GROUP("ColdRepositoryGroup"),

  @JsonProperty("ColdRepositoryCluster")
  COLD_REPOSITORY_CLUSTER("ColdRepositoryCluster"),

  @JsonProperty("ColdRepositoryETLApp")
  COLD_REPOSITORY_ETL_APP("ColdRepositoryETLApp"),

  @JsonProperty("WarmRepositoryGroup")
  WARM_REPOSITORY_GROUP("WarmRepositoryGroup"),

  @JsonProperty("StreamingHost")
  STREAMING_HOST("StreamingHost"),

  @JsonProperty("StagingRepositoryGroup")
  STAGING_REPOSITORY_GROUP("StagingRepositoryGroup"),

  @JsonProperty("AzureResourceTemplate")
  AZURE_RESOURCE_TEMPLATE("AzureResourceTemplate"),

  @JsonProperty("ScaleSetting")
  SCALE_SETTING("ScaleSetting"),

  @JsonProperty("DataOrderTopic")
  DATA_ORDER_TOPIC("DataOrderTopic"),

  @JsonProperty("DataAccessObject")
  DATA_ACCESS_OBJECT("DataAccessObject"),

  @JsonProperty("AzureBlobStorage")
  AZURE_BLOB_STORAGE("AzureBlobStorage");

  private final String value;

  ConfigurationType(String value) {
    this.value = value;
  }

  @Override
  public String toString() {
    return value;
  }
}
