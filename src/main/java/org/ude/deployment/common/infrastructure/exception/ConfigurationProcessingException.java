package org.ude.deployment.common.infrastructure.exception;

import static org.ude.deployment.common.infrastructure.exception.ExceptionUtilsExtension.concatExceptionCause;

public class ConfigurationProcessingException extends CustomException {
  public ConfigurationProcessingException(String message, Throwable cause) {
    super(message, cause);
  }

  public static class ConfigurationSerializationException extends CustomException {
    public ConfigurationSerializationException(Throwable cause) {
      super(
          concatExceptionCause(
              "Error serializing the deployment configuration due to the cause: ", cause),
          cause);
    }
  }
}
