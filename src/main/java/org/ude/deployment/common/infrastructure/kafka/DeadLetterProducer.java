package org.ude.deployment.common.infrastructure.kafka;

import io.smallrye.reactive.messaging.MutinyEmitter;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.eclipse.microprofile.reactive.messaging.Channel;
import org.eclipse.microprofile.reactive.messaging.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ApplicationScoped
public class DeadLetterProducer {

  private static final Logger LOGGER = LoggerFactory.getLogger(DeadLetterProducer.class);

  private final MutinyEmitter<byte[]> deadLetterEmitter;

  public DeadLetterProducer(@Channel("dead-letter") MutinyEmitter<byte[]> deadLetterEmitter) {
    this.deadLetterEmitter = deadLetterEmitter;
  }

  public CompletionStage<Void> sendMessage(Message<byte[]> message, String topicOrigin) {
    return deadLetterEmitter
        .sendMessage(
            message
                .withAck(
                    () -> {
                      LOGGER.info(
                          "Successfully sent message originating from"
                              + " topic '{}' to dead letter queue.",
                          topicOrigin);
                      return CompletableFuture.completedFuture(null);
                    })
                .withNack(
                    throwable -> {
                      LOGGER.error(
                          "Failed to send message originating from topic"
                              + " '{}' to dead letter queue.",
                          topicOrigin,
                          throwable);
                      return CompletableFuture.completedFuture(null);
                    }))
        .subscribeAsCompletionStage();
  }
}
