package org.ude.deployment.common.infrastructure.confluent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class ConfluentListResponse<T> {

  @JsonProperty("api_version")
  private String apiVersion;

  @JsonProperty("kind")
  private String kind;

  @JsonProperty("metadata")
  private ConfluentListResponseMetadata metadata;

  @JsonProperty("data")
  List<T> data;
}
