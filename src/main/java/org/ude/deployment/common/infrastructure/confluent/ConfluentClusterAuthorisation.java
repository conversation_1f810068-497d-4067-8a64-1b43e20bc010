package org.ude.deployment.common.infrastructure.confluent;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.MultivaluedHashMap;
import jakarta.ws.rs.core.MultivaluedMap;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.ext.ClientHeadersFactory;

@ApplicationScoped
public class ConfluentClusterAuthorisation implements ClientHeadersFactory {

  @ConfigProperty(name = "api.confluent.cluster-api-key")
  public String apiKey;

  @ConfigProperty(name = "api.confluent.cluster-api-key-secret")
  public String apiKeySecret;

  @Override
  public MultivaluedMap<String, String> update(
      MultivaluedMap<String, String> incomingHeaders,
      MultivaluedMap<String, String> clientOutgoingHeaders) {

    final var auth =
        "Basic "
            + new String(
                Base64.getEncoder()
                    .encode(
                        "%s:%s".formatted(apiKey, apiKeySecret).getBytes(StandardCharsets.UTF_8)));
    return new MultivaluedHashMap<String, String>(Map.of("Authorization", auth));
  }
}
