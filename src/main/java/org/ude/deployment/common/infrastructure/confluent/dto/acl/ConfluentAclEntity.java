package org.ude.deployment.common.infrastructure.confluent.dto.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.ToString;

@ToString
@Getter
public class ConfluentAclEntity {
  @JsonProperty("cluster_id")
  @NotBlank private String clusterId;

  @JsonProperty("resource_type")
  @NotBlank private ConfluentAclResourceType resourceType;

  @JsonProperty("resource_name")
  @NotBlank private String resourceName;

  @JsonProperty("pattern_type")
  @NotBlank private ConfluentAclPatternType patternType;

  @JsonProperty("principal")
  @NotBlank private String principal;

  @JsonProperty("host")
  @NotBlank private String host;

  @JsonProperty("operation")
  @NotBlank private ConfluentAclOperation operation;

  @JsonProperty("permission")
  @NotBlank private ConfluentAclPermission permission;
}
