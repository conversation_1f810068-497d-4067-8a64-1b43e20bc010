package org.ude.deployment.common.infrastructure.exception;

import java.util.List;
import lombok.Getter;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;

@Getter
public class DataPlatformObjectValidationException extends DataPlatformObjectException {
  public DataPlatformObjectValidationException(
      List<String> errors, List<ProvisioningStep> provisioningSteps) {
    super(
        "Data platform object validation failed.\n",
        provisioningSteps,
        new RuntimeException(String.join(", ", errors)));
  }
}
