package org.ude.deployment.common.infrastructure.confluent;

import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestPath;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListRequest;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListResponse;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclEntity;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentCreateAclRequest;

// https://<cluster>.<region>.<provider>.confluent.cloud/
@Path("kafka/v3/clusters/")
@RegisterRestClient(configKey = "confluent-cluster-service")
@RegisterClientHeaders(ConfluentClusterAuthorisation.class)
public interface ConfluentClusterClient {

  /*
   * GET {cluster_id}/acls
   *
   * https://docs.confluent.io/cloud/current/api.html#tag/ACL-(v3)/operation/getKafkaAcls
   */
  @GET
  @Path("{clusterId}/acls")
  Uni<ConfluentListResponse<ConfluentAclEntity>> getKafkaAcls(
      @RestPath String clusterId,
      @QueryParam("resource_type") String resourceType,
      @QueryParam("resource_name") String resourceName);

  /*
   * POST {cluster_id}/acls:batch
   *
   * https://docs.confluent.io/cloud/current/api.html#tag/ACL-(v3)/operation/batchCreateKafkaAcls
   */
  @POST
  @Path("{clusterId}/acls:batch")
  Uni<Response> batchCreateKafkaAcls(
      @RestPath String clusterId, ConfluentListRequest<ConfluentCreateAclRequest> request);

  /*
   * DELETE {cluster_id}/acls
   *
   * https://docs.confluent.io/cloud/current/api.html#tag/ACL-(v3)/operation/deleteKafkaAcls
   */
  @DELETE
  @Path("{clusterId}/acls")
  Uni<ConfluentListResponse<ConfluentAclEntity>> deleteKafkaAcls(
      @RestPath String clusterId,
      @QueryParam("resource_name") String resourceName,
      @QueryParam("resource_type") String resourceType,
      @QueryParam("pattern_type") String patternType,
      @QueryParam("operation") String operation,
      @QueryParam("permission") String permission,
      @QueryParam("principal") String principal);
}
