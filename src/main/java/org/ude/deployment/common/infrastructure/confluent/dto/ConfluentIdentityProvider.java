package org.ude.deployment.common.infrastructure.confluent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@AllArgsConstructor
public class ConfluentIdentityProvider extends ConfluentBaseEntity {
  @JsonProperty("state")
  private String state;

  @JsonProperty("issuer")
  private String issuer;

  @JsonProperty("jwks_uri")
  private String jwksUri;

  @JsonProperty("identity_claim")
  private String identityClaim;

  @JsonProperty("keys")
  private List<Map<String, JsonNode>> keys;
}
