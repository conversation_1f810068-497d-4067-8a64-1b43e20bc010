package org.ude.deployment.common.infrastructure.kafka;

import io.confluent.kafka.schemaregistry.client.rest.entities.requests.RegisterSchemaResponse;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.kafka.clients.admin.AlterConfigOp;
import org.apache.kafka.clients.admin.ConfigEntry;
import org.apache.kafka.clients.admin.NewPartitions;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.common.KafkaException;
import org.apache.kafka.common.config.ConfigResource;
import org.apache.kafka.common.errors.TopicExistsException;
import org.apache.kafka.common.errors.UnknownTopicOrPartitionException;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.common.infrastructure.confluent.ConfluentClusterClient;
import org.ude.deployment.common.infrastructure.confluent.ConfluentIdentityProvidersService;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityPool;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListRequest;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclOperation;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclPatternType;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclPermission;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentAclResourceType;
import org.ude.deployment.common.infrastructure.confluent.dto.acl.ConfluentCreateAclRequest;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateDataOrderTopicsResult;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateTopicsRequest;
import org.ude.deployment.common.infrastructure.kafka.dto.DataOrderConnectionInfo;
import org.ude.deployment.dao.DaoGroupRole;
import org.ude.deployment.dao.dto.DataAccessObject;
import org.ude.deployment.dataorder.dto.DataOrder;

@ApplicationScoped
public class TopicService {

  public static final String RETENTION_MS_KEY = "retention.ms";

  @ConfigProperty(name = "api.confluent.identity-provider")
  private String identityProviderId;

  @ConfigProperty(name = "api.confluent.cluster")
  private String clusterId;

  @ConfigProperty(name = "api.confluent.connection-tenant-id")
  private String connectionTenantid;

  @ConfigProperty(name = "api.confluent.client-consumer-group-prefix")
  private String clientConsumerGroupPrefix;

  private final KafkaAdminClientProvider kafkaAdminClientProvider;
  private final KafkaConfig kafkaConfig;
  private final ConfluentIdentityProvidersService confluentIdentityProvidersService;
  private final ConfluentClusterClient confluentClusterClient;
  private final KafkaSchemaRegistryClient kafkaSchemaRegistryClient;

  @Inject
  public TopicService(
      KafkaAdminClientProvider kafkaAdminClientProvider,
      KafkaConfig kafkaConfig,
      ConfluentIdentityProvidersService confluentIdentityProvidersService,
      @RestClient ConfluentClusterClient confluentClusterClient,
      KafkaSchemaRegistryClient kafkaSchemaRegistryClient) {
    this.kafkaAdminClientProvider = kafkaAdminClientProvider;
    this.kafkaConfig = kafkaConfig;
    this.confluentIdentityProvidersService = confluentIdentityProvidersService;
    this.confluentClusterClient = confluentClusterClient;
    this.kafkaSchemaRegistryClient = kafkaSchemaRegistryClient;
  }

  public Uni<List<String>> createDataOrderTopics(UUID dataOrderId, DataOrder dataOrder) {
    var request =
        new CreateTopicsRequest(
            dataOrderId.toString(),
            dataOrder.configuration().partitionCount(),
            kafkaConfig.getDefaultReplicationFactor());

    return processDataOrderTopicsCreation(request);
  }

  public Uni<List<String>> processDataOrderTopicsCreation(CreateTopicsRequest request) {
    var topics = formatDataOrderTopicsList(request);
    var adminClient = kafkaAdminClientProvider.getAdmin();
    var topicNames = topics.stream().map(NewTopic::name).toList();

    return Uni.createFrom()
        .completionStage(adminClient.createTopics(topics).all().toCompletionStage())
        .onItem()
        .transformToUni(r -> Uni.createFrom().item(topicNames))
        .onFailure()
        .recoverWithUni(
            throwable -> {
              if (throwable instanceof TopicExistsException) {
                Log.infof(
                    "Topic for dataorder %s already exists: %s",
                    request.dataOrderId(), throwable.toString());
                return Uni.createFrom().item(topicNames);
              }

              return Uni.createFrom().failure(throwable);
            });
  }

  public List<NewTopic> formatDataOrderTopicsList(CreateTopicsRequest request) {
    var replicationFactor = (short) request.replicationFactor();
    var topicsSharedConfig =
        Map.of("max.message.bytes", String.valueOf(kafkaConfig.getDefaultMaxMessageBytes()));
    return List.of(
        new NewTopic(
                kafkaConfig.getInputTopicName(request.dataOrderId()),
                request.partitions(),
                replicationFactor)
            .configs(topicsSharedConfig),
        new NewTopic(
                kafkaConfig.getOutputTopicName(request.dataOrderId()),
                request.partitions(),
                replicationFactor)
            .configs(topicsSharedConfig),
        new NewTopic(
                kafkaConfig.getNonRetryableDlqTopicName(request.dataOrderId()),
                request.partitions(),
                replicationFactor)
            .configs(topicsSharedConfig));
  }

  public Uni<List<TopicDescription>> getTopicDescriptions(List<String> topicNames) {
    var adminClient = kafkaAdminClientProvider.getAdmin();
    return Uni.createFrom()
        .completionStage(adminClient.describeTopics(topicNames).allTopicNames().toCompletionStage())
        .map(topicDescriptionMap -> topicDescriptionMap.values().stream().toList());
  }

  public Uni<List<Integer>> updatePartitionCountForTopics(
      List<String> topicNames, int newPartitionCount) {
    var adminClient = kafkaAdminClientProvider.getAdmin();

    var newPartitions =
        topicNames.stream()
            .collect(
                Collectors.toMap(
                    topicName -> topicName,
                    topicName -> NewPartitions.increaseTo(newPartitionCount)));
    return Uni.createFrom()
        .completionStage(adminClient.createPartitions(newPartitions).all().toCompletionStage())
        .map(unused -> newPartitions.values().stream().map(NewPartitions::totalCount).toList());
  }

  public Uni<Map<String, Integer>> updateRetentionPolicyForTopics(
      List<String> topicNames, int retentionPeriodInDays) {

    var update = formatRetentionUpdate(topicNames, retentionPeriodInDays);
    var adminClient = kafkaAdminClientProvider.getAdmin();
    return Uni.createFrom()
        .completionStage(adminClient.incrementalAlterConfigs(update).all().toCompletionStage())
        .map(
            unused ->
                topicNames.stream()
                    .collect(Collectors.toMap(topic -> topic, topic -> retentionPeriodInDays)));
  }

  public long retentionInDaysToMs(int retentionInDays) {
    return TimeUnit.DAYS.toMillis(retentionInDays);
  }

  @NotNull private Map<ConfigResource, Collection<AlterConfigOp>> formatRetentionUpdate(
      List<String> topicNames, int retention) {
    var retentionMs = retentionInDaysToMs(retention);
    return topicNames.stream()
        .collect(
            Collectors.toMap(
                topic -> new ConfigResource(ConfigResource.Type.TOPIC, topic),
                topic ->
                    List.of(
                        new AlterConfigOp(
                            new ConfigEntry(RETENTION_MS_KEY, Long.toString(retentionMs)),
                            AlterConfigOp.OpType.SET))));
  }

  public Uni<CreateDataOrderTopicsResult> handleTopicAclSetup(
      DataAccessObject dao, String dataOrderId, List<String> topicNamesList) {
    return Uni.createFrom()
        .item(topicNamesList)
        .onItem()
        .transformToUni(
            topicNames -> {
              if (dao == null) {
                Log.infof("No DAO received for data order %s", dataOrderId);
                return Uni.createFrom().item(new CreateDataOrderTopicsResult(topicNames, null));
              }

              return setupAclForTopics(dao, dataOrderId)
                  .onItem()
                  .transformToUni(
                      info ->
                          Uni.createFrom().item(new CreateDataOrderTopicsResult(topicNames, info)));
            });
  }

  public Uni<DataOrderConnectionInfo> setupAclForTopics(DataAccessObject dao, String dataOrderId) {
    var daoId = dao.id().toString();
    return confluentIdentityProvidersService
        .findDaoIdentityPools(daoId)
        .onItem()
        .transformToUni(
            (pools) -> {
              if (pools.size() < 4) {
                var message =
                    String.format(
                        "Expected 4 pools for DAO %s" + " but found %s",
                        daoId,
                        !pools.isEmpty()
                            ? pools.stream()
                                .map(ConfluentIdentityPool::getDisplayName)
                                .collect(Collectors.joining(","))
                            : "none");
                Log.error(message);

                throw new RuntimeException(message);
              }

              return deleteDataOrderTopicsAcls(dataOrderId)
                  .onItem()
                  .transformToUni(unused -> applyAclForTopics(dao, dataOrderId, pools));
            });
  }

  private Uni<DataOrderConnectionInfo> applyAclForTopics(
      DataAccessObject dao, String dataOrderId, List<ConfluentIdentityPool> pools) {
    var daoId = dao.id().toString();
    var outputTopic = kafkaConfig.getOutputTopicName(dataOrderId);

    var acls = generateAcls(daoId, dataOrderId, pools);
    var request = new ConfluentListRequest<ConfluentCreateAclRequest>(acls);

    return confluentClusterClient
        .batchCreateKafkaAcls(clusterId, request)
        .onItem()
        .transformToUni(
            r -> {
              Log.infof("Created ACLs for data order %s", dataOrderId);
              return Uni.createFrom()
                  .item(
                      new DataOrderConnectionInfo(
                          outputTopic,
                          clientConsumerGroupPrefix,
                          formatConnectionInfo(
                              getIdentityPoolForDaoGroup(
                                  daoId, pools, DaoGroupRole.DATA_CONSUMER))));
            });
  }

  private ConfluentIdentityPool getIdentityPoolForDaoGroup(
      String daoId, List<ConfluentIdentityPool> pools, DaoGroupRole groupRole) {
    return pools.stream()
        .filter(
            p ->
                p.getDisplayName()
                    .equals(
                        confluentIdentityProvidersService.formatIdentityPoolName(daoId, groupRole)))
        .findFirst()
        .orElseThrow(() -> new RuntimeException("Data %s pool not found".formatted(groupRole)));
  }

  private List<ConfluentCreateAclRequest> getConsumerAcls(
      String dataOrderId, ConfluentIdentityPool dataConsumerPool) {
    var outputTopic = kafkaConfig.getOutputTopicName(dataOrderId);
    return List.of(
        generateAclRequest(
            outputTopic,
            dataConsumerPool,
            ConfluentAclResourceType.TOPIC,
            ConfluentAclOperation.READ),
        generateAclRequest(
            clientConsumerGroupPrefix,
            dataConsumerPool,
            ConfluentAclResourceType.GROUP,
            ConfluentAclOperation.CREATE,
            ConfluentAclPatternType.PREFIXED),
        generateAclRequest(
            clientConsumerGroupPrefix,
            dataConsumerPool,
            ConfluentAclResourceType.GROUP,
            ConfluentAclOperation.READ,
            ConfluentAclPatternType.PREFIXED));
  }

  private List<ConfluentCreateAclRequest> getProducerAcls(
      String dataOrderId, ConfluentIdentityPool dataProducerPool) {
    var inputTopic = kafkaConfig.getInputTopicName(dataOrderId);
    return List.of(
        generateAclRequest(
            inputTopic,
            dataProducerPool,
            ConfluentAclResourceType.TOPIC,
            ConfluentAclOperation.WRITE));
  }

  private List<ConfluentCreateAclRequest> getDataOrderReaderAcls(
      String dataOrderId,
      ConfluentIdentityPool dataOrderReaderPool,
      ConfluentIdentityPool dataConsumerPool) {
    var inputTopic = kafkaConfig.getInputTopicName(dataOrderId);
    var outputTopic = kafkaConfig.getOutputTopicName(dataOrderId);

    return List.of(
        generateAclRequest(
            inputTopic,
            dataOrderReaderPool,
            ConfluentAclResourceType.TOPIC,
            ConfluentAclOperation.READ),
        generateAclRequest(
            clientConsumerGroupPrefix,
            dataOrderReaderPool,
            ConfluentAclResourceType.GROUP,
            ConfluentAclOperation.CREATE,
            ConfluentAclPatternType.PREFIXED),
        generateAclRequest(
            clientConsumerGroupPrefix,
            dataOrderReaderPool,
            ConfluentAclResourceType.GROUP,
            ConfluentAclOperation.READ,
            ConfluentAclPatternType.PREFIXED),
        generateAclRequest(
            inputTopic,
            dataOrderReaderPool,
            ConfluentAclResourceType.TOPIC,
            ConfluentAclOperation.DESCRIBE),
        generateAclRequest(
            inputTopic,
            dataOrderReaderPool,
            ConfluentAclResourceType.TOPIC,
            ConfluentAclOperation.DESCRIBE_CONFIGS),
        generateAclRequest(
            outputTopic,
            dataOrderReaderPool,
            ConfluentAclResourceType.TOPIC,
            ConfluentAclOperation.READ),
        generateAclRequest(
            outputTopic,
            dataOrderReaderPool,
            ConfluentAclResourceType.TOPIC,
            ConfluentAclOperation.DESCRIBE),
        generateAclRequest(
            outputTopic,
            dataOrderReaderPool,
            ConfluentAclResourceType.TOPIC,
            ConfluentAclOperation.DESCRIBE_CONFIGS));
  }

  private List<ConfluentCreateAclRequest> getDataOrderManagerAcls(
      String dataOrderId, ConfluentIdentityPool dataOrderManagerPool) {
    var inputTopic = kafkaConfig.getInputTopicName(dataOrderId);
    var outputTopic = kafkaConfig.getOutputTopicName(dataOrderId);
    return List.of(
        generateAclRequest(
            inputTopic,
            dataOrderManagerPool,
            ConfluentAclResourceType.TOPIC,
            ConfluentAclOperation.ALL),
        generateAclRequest(
            outputTopic,
            dataOrderManagerPool,
            ConfluentAclResourceType.TOPIC,
            ConfluentAclOperation.ALL),
        generateAclRequest(
            clientConsumerGroupPrefix,
            dataOrderManagerPool,
            ConfluentAclResourceType.GROUP,
            ConfluentAclOperation.ALL,
            ConfluentAclPatternType.PREFIXED));
  }

  public List<ConfluentCreateAclRequest> generateAcls(
      String daoId, String dataOrderId, List<ConfluentIdentityPool> pools) {

    var dataConsumerPool = getIdentityPoolForDaoGroup(daoId, pools, DaoGroupRole.DATA_CONSUMER);
    var dataProducerPool = getIdentityPoolForDaoGroup(daoId, pools, DaoGroupRole.DATA_PRODUCER);
    var dataOrderReaderPool =
        getIdentityPoolForDaoGroup(daoId, pools, DaoGroupRole.DATA_ORDER_READER);
    var dataOrderManagerPool =
        getIdentityPoolForDaoGroup(daoId, pools, DaoGroupRole.DATA_ORDER_MANAGER);

    return Stream.of(
            getConsumerAcls(dataOrderId, dataConsumerPool),
            getProducerAcls(dataOrderId, dataProducerPool),
            getDataOrderReaderAcls(dataOrderId, dataOrderReaderPool, dataConsumerPool),
            getDataOrderManagerAcls(dataOrderId, dataOrderManagerPool))
        .flatMap(List::stream)
        .collect(Collectors.toList());
  }

  /*
   * Spec:
   * https://docs.confluent.io/cloud/current/api.html#tag/ACL-(v3)/operation/
   * batchCreateKafkaAcls
   */
  private ConfluentCreateAclRequest generateAclRequest(
      String name,
      ConfluentIdentityPool identityPool,
      ConfluentAclResourceType type,
      ConfluentAclOperation operation) {
    return generateAclRequest(name, identityPool, type, operation, ConfluentAclPatternType.LITERAL);
  }

  private ConfluentCreateAclRequest generateAclRequest(
      String name,
      ConfluentIdentityPool identityPool,
      ConfluentAclResourceType type,
      ConfluentAclOperation operation,
      ConfluentAclPatternType patternType) {
    return ConfluentCreateAclRequest.builder()
        .resourceType(type)
        .resourceName(name)
        .patternType(patternType)
        .principal(formatPoolPrincipal(identityPool.getId()))
        .host("*")
        .operation(operation)
        .permission(ConfluentAclPermission.ALLOW)
        .build();
  }

  private Map<String, String> formatConnectionInfo(ConfluentIdentityPool consumerPool) {

    return Map.of(
        "security.protocol",
        "SASL_SSL",
        "sasl.mechanism",
        "OAUTHBEARER",
        "sasl.jaas.config",
        "org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule required clientId=\"<SPN_CLIENT_ID>\" clientSecret=\"<SPN_CLIENT_SECRET>\" scope=\"<SPN_SCOPE>\" extension_logicalCluster=\"%s\" extension_identityPoolId=\"%s\";"
            .formatted(clusterId, consumerPool.getId()),
        "sasl.oauthbearer.token.endpoint.url",
        "https://login.microsoftonline.com/%s/oauth2/v2.0/token".formatted(connectionTenantid),
        "client.dns.lookup",
        "use_all_dns_ips",
        "compression.type",
        "none",
        "sasl.login.callback.handler.class",
        "org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginCallbackHandler",
        "group.id",
        "%s<CONSUMER_GROUP_NAME>".formatted(clientConsumerGroupPrefix),
        // Confluent extensions
        "extension.logical_cluster",
        clusterId,
        "extension.identity_pool_id",
        consumerPool.getId());
  }

  public Uni<List<String>> deleteTopics(List<String> topicNames) {
    var adminClient = kafkaAdminClientProvider.getAdmin();
    Log.infof("Deleting topics: %s", topicNames);
    return Uni.createFrom()
        .completionStage(adminClient.deleteTopics(topicNames).all().toCompletionStage())
        .onFailure(UnknownTopicOrPartitionException.class)
        .recoverWithItem(
            error -> {
              Log.warnf("Failed to delete topics: %s", error.getMessage());
              return null;
            })
        .onFailure()
        .transform(
            error -> {
              // Handle other potential errors
              Log.errorf("Failed to delete topics: {}", error.getMessage());
              throw new KafkaException("Failed to delete topics", error);
            })
        .map(unused -> topicNames);
  }

  public Uni<List<String>> deleteDataOrderTopicsAcls(String dataOrderId) {
    var topicNames =
        List.of(
            kafkaConfig.getInputTopicName(dataOrderId),
            kafkaConfig.getOutputTopicName(dataOrderId));
    return Multi.createFrom()
        .iterable(topicNames)
        .onItem()
        .transformToUniAndMerge(
            topic ->
                confluentClusterClient.deleteKafkaAcls(
                    clusterId,
                    topic,
                    ConfluentAclResourceType.TOPIC.name(),
                    ConfluentAclPatternType.LITERAL.name(),
                    ConfluentAclOperation.ANY.name(),
                    ConfluentAclPermission.ANY.name(),
                    null))
        .collect()
        .asList()
        .onItem()
        .transform(
            responses -> {
              Log.infof("Deleted ACLs for topics: %s", topicNames);
              return topicNames;
            });
  }

  public Uni<Void> deleteIdentityPoolAcls(String poolId) {
    return confluentClusterClient
        .deleteKafkaAcls(
            clusterId,
            null,
            ConfluentAclResourceType.ANY.name(),
            ConfluentAclPatternType.ANY.name(),
            ConfluentAclOperation.ANY.name(),
            ConfluentAclPermission.ANY.name(),
            formatPoolPrincipal(poolId))
        .onItem()
        .transformToUni(r -> Uni.createFrom().voidItem());
  }

  private String formatPoolPrincipal(String poolId) {
    return "User:%s".formatted(poolId);
  }

  public Uni<RegisterSchemaResponse> associateSchemaToInputTopic(
      String dataOrderId, String schemaId) {
    try {
      var inputTopicName = kafkaConfig.getInputTopicName(dataOrderId);
      var meta = kafkaSchemaRegistryClient.associateSchemaToTopic(schemaId, inputTopicName);
      Log.infof(
          "Associated schema %s to input topic %s",
          kafkaConfig.getSchemaSubjectName(schemaId), inputTopicName);

      return Uni.createFrom().item(meta);
    } catch (Exception ex) {
      Log.errorf(ex, "Failed to associate topic %s to schema %s", dataOrderId, schemaId);
      throw new RuntimeException(ex);
    }
  }
}
