package org.ude.deployment.common.infrastructure.kafka;

import io.quarkus.logging.Log;
import io.quarkus.runtime.Startup;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.eclipse.microprofile.reactive.messaging.Incoming;
import org.eclipse.microprofile.reactive.messaging.Message;
import org.ude.deployment.common.CommandRouter;
import org.ude.deployment.common.command.Command;

@Startup
@ApplicationScoped
@AllArgsConstructor
public class CommandConsumer {
  @NonNull private final CommandRouter commandRouter;

  @Incoming("commands")
  public Uni<Void> consumeCommand(@Valid Message<Command> command) {
    Log.info("Received command: '%s'".formatted(command.getPayload()));
    return Uni.createFrom()
        .item(command.getPayload())
        .onItem()
        .ifNotNull()
        .transform(commandRouter::route)
        .eventually(() -> command.ack())
        .onFailure()
        .invoke(e -> Log.error("Unable to route the message", e))
        .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
        .replaceWithVoid();
  }
}
