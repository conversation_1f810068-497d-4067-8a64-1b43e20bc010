package org.ude.deployment.common.infrastructure.kafka.util;

import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeaders;

public class MessageMetadata {

  public static Headers deadLetterHeaders(
      String topicOrigin, boolean isKey, String failureType, String errorDetails) {
    return deadLetterHeaders(new RecordHeaders(), topicOrigin, isKey, failureType, errorDetails);
  }

  public static Headers deadLetterHeaders(
      Headers headers, String topicOrigin, boolean isKey, String failureType, String errorDetails) {
    return new RecordHeaders(headers)
        .add("topic", topicOrigin.getBytes())
        .add("isKey", isKey ? "true".getBytes() : "false".getBytes())
        .add("failureType", failureType.getBytes())
        .add("errorDetails", errorDetails.getBytes());
  }
}
