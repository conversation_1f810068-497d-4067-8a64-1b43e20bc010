package org.ude.deployment.common.infrastructure.confluent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Getter
public class ConfluentBaseEntity {
  @JsonProperty("api_version")
  protected String apiVersion;

  @JsonProperty("kind")
  protected String kind;

  @JsonProperty("id")
  protected String id;

  @JsonProperty("display_name")
  protected String displayName;

  @JsonProperty("description")
  protected String description;

  public ConfluentBaseEntity(String id, String name, String description) {
    this.id = id;
    this.displayName = name;
    this.description = description;
  }
}
