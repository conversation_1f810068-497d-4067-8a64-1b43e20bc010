package org.ude.deployment.common.infrastructure.kafka;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.confluent.kafka.schemaregistry.client.CachedSchemaRegistryClient;
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient;
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClientConfig;
import io.confluent.kafka.schemaregistry.client.rest.RestService;
import io.confluent.kafka.schemaregistry.client.rest.entities.requests.RegisterSchemaResponse;
import io.confluent.kafka.schemaregistry.client.rest.exceptions.RestClientException;
import io.confluent.kafka.schemaregistry.client.security.basicauth.UserInfoCredentialProvider;
import io.confluent.kafka.schemaregistry.json.JsonSchema;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.json.JsonObject;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;

@ApplicationScoped
public class KafkaSchemaRegistryClient {
  private static final int MAX_SCHEMAS_PER_SUBJECT_DEFAULT = 1000;

  private final ObjectMapper objectMapper;
  private SchemaRegistryClient schemaRegistry;
  private final String schemaRegistryUrl;
  private final String schemaRegistryUserInfo;
  private final KafkaConfig kafkaNamingConfig;

  @Inject
  public KafkaSchemaRegistryClient(
      ObjectMapper objectMapper,
      @ConfigProperty(name = "kafka.schema.registry.url") String schemaRegistryUrl,
      @ConfigProperty(name = "kafka.schema.registry.basic.auth.user.info")
          String schemaRegistryUserInfo,
      KafkaConfig namingConfig) {

    this.objectMapper = objectMapper;
    this.schemaRegistryUrl = schemaRegistryUrl;
    this.schemaRegistryUserInfo = schemaRegistryUserInfo;
    this.kafkaNamingConfig = namingConfig;
  }

  @PostConstruct
  void init() {
    var restService = new RestService(schemaRegistryUrl);
    // https://github.com/confluentinc/schema-registry/issues/1759
    if (StringUtils.isNotBlank(schemaRegistryUserInfo)) {
      var props =
          Map.of(
              SchemaRegistryClientConfig.BASIC_AUTH_CREDENTIALS_SOURCE,
              "USER_INFO",
              SchemaRegistryClientConfig.USER_INFO_CONFIG,
              schemaRegistryUserInfo);
      var provider = new UserInfoCredentialProvider();
      provider.configure(props);
      restService.setBasicAuthCredentialProvider(provider);
    }

    this.schemaRegistry =
        new CachedSchemaRegistryClient(restService, MAX_SCHEMAS_PER_SUBJECT_DEFAULT);
  }

  @PreDestroy
  void destroy() throws IOException {
    if (Objects.nonNull(this.schemaRegistry)) {
      this.schemaRegistry.close();
    }
  }

  /**
   * Creates the Confluent JSON schema for the given Streaming Schema.
   *
   * @param streamingSchemaId the ID of the streaming schema
   * @param schema the JSON schema to register
   * @return the response from the schema registry
   * @throws RestClientException if there is an error communicating with the schema registry
   * @throws IOException if there is an error reading the schema
   */
  public RegisterSchemaResponse registerSchema(String streamingSchemaId, JsonObject schema)
      throws IOException, RestClientException {
    final var subject = kafkaNamingConfig.getSchemaSubjectName(streamingSchemaId);
    final var schemaNode = objectMapper.readTree(schema.toString());
    return schemaRegistry.registerWithResponse(subject, new JsonSchema(schemaNode), true);
  }

  /**
   * Checks if a schema exists in the schema registry.
   *
   * @param subject the subject name
   * @return true if the schema exists, false otherwise
   * @throws IOException if there is an error reading the schema
   * @throws RestClientException if there is an error communicating with the schema registry
   */
  public boolean doesSchemaExist(String subject) throws IOException, RestClientException {
    try {
      schemaRegistry.getLatestSchemaMetadata(subject);
      return true;
    } catch (RestClientException ex) {
      if (ex.getStatus() == HttpResponseStatus.NOT_FOUND.code()) {
        return false;
      }
      throw ex;
    }
  }

  /**
   * Associates a topic with a schema in the schema registry.
   *
   * @param schemaId the ID of the schema
   * @param topicName the name of the topic
   * @return the response from the schema registry
   * @throws IOException if there is an error reading the schema
   * @throws RestClientException if there is an error communicating with the schema registry
   */
  public RegisterSchemaResponse associateSchemaToTopic(String schemaId, String topicName)
      throws IOException, RestClientException {

    final var existingSubject = kafkaNamingConfig.getSchemaSubjectName(schemaId);
    final var newSubject = kafkaNamingConfig.getTopicSchemaSubjectName(topicName);

    var schemaMetadata = schemaRegistry.getLatestSchemaMetadata(existingSubject);
    var schema = new JsonObject(schemaMetadata.getSchema());
    final var schemaNode = objectMapper.readTree(schema.toString());
    return schemaRegistry.registerWithResponse(newSubject, new JsonSchema(schemaNode), true);
  }

  /**
   * Retrieves the client instance used to trigger native calls to the Schema Registry.
   *
   * @return The SchemaRegistryClient instance.
   */
  public SchemaRegistryClient client() {
    return this.schemaRegistry;
  }

  /**
   * Deletes a schema from the schema registry.
   *
   * @param subject the subject name
   * @param isPermanent whether to permanently delete the schema
   * @return the response from the schema registry
   * @throws IOException if there is an error reading the schema
   * @throws RestClientException if there is an error communicating with the schema registry
   */
  public List<Integer> deleteSchema(String subject, boolean isPermanent)
      throws IOException, RestClientException {
    return schemaRegistry.deleteSubject(subject, isPermanent);
  }
}
