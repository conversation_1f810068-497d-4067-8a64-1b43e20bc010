package org.ude.deployment.common.infrastructure.kafka;

import io.smallrye.common.annotation.Identifier;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Inject;
import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.KafkaAdminClient;

@ApplicationScoped
public class KafkaAdminClientProvider {

  private final Map<String, Object> config;

  @Inject
  public KafkaAdminClientProvider(@Identifier("default-kafka-broker") Map<String, Object> config) {
    this.config = config;
  }

  @Produces
  @ApplicationScoped
  public AdminClient getAdmin() {
    Map<String, Object> copy = new HashMap<>();
    for (Map.Entry<String, Object> entry : config.entrySet()) {
      if (AdminClientConfig.configNames().contains(entry.getKey())) {
        copy.put(entry.getKey(), entry.getValue());
      }
    }
    return KafkaAdminClient.create(copy);
  }
}
