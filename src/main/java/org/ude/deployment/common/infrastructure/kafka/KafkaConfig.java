package org.ude.deployment.common.infrastructure.kafka;

import jakarta.inject.Singleton;
import lombok.Getter;
import org.eclipse.microprofile.config.inject.ConfigProperty;

@Singleton
public class KafkaConfig {
  public static final String DATA_ORDER_ID_PLACEHOLDER = "{dataOrder}";
  public static final String TOPIC_NAME_PLACEHOLDER = "{topicName}";
  public static final String SCHEMA_ID_PLACEHOLDER = "{schemaId}";

  @ConfigProperty(name = "api.messaging.topic-name-template-input")
  private String inputTopicNameTemplate;

  @ConfigProperty(name = "api.messaging.topic-name-template-output")
  private String outputTopicNameTemplate;

  @ConfigProperty(name = "api.messaging.topic-name-template-dlq-non-retryable")
  private String nonRetryableDlqTopicNameTemplate;

  @ConfigProperty(name = "api.messaging.schema-subject-name-template")
  private String topicSchemaSubjectNameTemplate;

  @ConfigProperty(name = "api.messaging.register-schema-subject-name-template")
  private String schemaSubjectNameTemplate;

  @Getter
  @ConfigProperty(name = "api.messaging.topics.default-replication-factor")
  private int defaultReplicationFactor;

  @Getter
  @ConfigProperty(name = "api.messaging.topics.default-max-message-bytes")
  private int defaultMaxMessageBytes;

  public String getInputTopicName(String dataOrderId) {
    return inputTopicNameTemplate.replace(DATA_ORDER_ID_PLACEHOLDER, dataOrderId);
  }

  public String getOutputTopicName(String dataOrderId) {
    return outputTopicNameTemplate.replace(DATA_ORDER_ID_PLACEHOLDER, dataOrderId);
  }

  public String getNonRetryableDlqTopicName(String dataOrderId) {
    return nonRetryableDlqTopicNameTemplate.replace(DATA_ORDER_ID_PLACEHOLDER, dataOrderId);
  }

  public String getTopicSchemaSubjectName(String topicName) {
    return topicSchemaSubjectNameTemplate.replace(TOPIC_NAME_PLACEHOLDER, topicName);
  }

  public String getSchemaSubjectName(String schemaId) {
    return schemaSubjectNameTemplate.replace(SCHEMA_ID_PLACEHOLDER, schemaId);
  }
}
