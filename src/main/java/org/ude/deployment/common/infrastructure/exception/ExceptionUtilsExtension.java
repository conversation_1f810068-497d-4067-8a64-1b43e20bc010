package org.ude.deployment.common.infrastructure.exception;

import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCause;

public class ExceptionUtilsExtension {
  public static String concatExceptionCause(String message, Throwable cause) {
    var rootCauseMessage = getRootCause(cause).getLocalizedMessage();
    if (rootCauseMessage == null) {
      rootCauseMessage = "root cause could not be determined";
    }

    return message + " " + rootCauseMessage;
  }
}
