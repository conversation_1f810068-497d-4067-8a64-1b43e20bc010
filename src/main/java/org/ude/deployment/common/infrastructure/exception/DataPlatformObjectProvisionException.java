package org.ude.deployment.common.infrastructure.exception;

import java.util.List;
import lombok.Getter;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;

@Getter
public class DataPlatformObjectProvisionException extends DataPlatformObjectException {
  private static final String DATA_PLATFORM_OBJECT_PROVISIONING_FAILED =
      "Data platform object provisioning failed.\n";

  public DataPlatformObjectProvisionException(
      List<String> errors, List<ProvisioningStep> provisioningSteps) {
    this(provisioningSteps, new RuntimeException(String.join(", ", errors)));
  }

  public DataPlatformObjectProvisionException(
      List<ProvisioningStep> provisioningSteps, Throwable cause) {
    super(DATA_PLATFORM_OBJECT_PROVISIONING_FAILED, provisioningSteps, cause);
  }
}
