package org.ude.deployment.common.infrastructure.kafka;

import static org.ude.deployment.common.infrastructure.kafka.util.Constants.BEAN_VALIDATION_FAILURE;
import static org.ude.deployment.common.infrastructure.kafka.util.Constants.DESERIALIZATION_FAILURE;

import io.smallrye.common.annotation.Identifier;
import io.smallrye.mutiny.Uni;
import io.smallrye.reactive.messaging.kafka.DeserializationFailureHandler;
import io.smallrye.reactive.messaging.kafka.api.OutgoingKafkaRecordMetadata;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.validation.Validator;
import java.time.Duration;
import org.apache.kafka.common.header.Headers;
import org.eclipse.microprofile.reactive.messaging.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.infrastructure.kafka.util.MessageMetadata;

@ApplicationScoped
@Identifier("deserialization-failure-handler") public class JsonDeserializationFailureHandler implements DeserializationFailureHandler<Command> {

  private static final Logger LOGGER =
      LoggerFactory.getLogger(JsonDeserializationFailureHandler.class);

  private final DeadLetterProducer deadLetterProducer;
  private final Validator validator;

  @Inject
  public JsonDeserializationFailureHandler(
      DeadLetterProducer deadLetterProducer, Validator validator) {
    this.deadLetterProducer = deadLetterProducer;
    this.validator = validator;
  }

  @Override
  public Command decorateDeserialization(
      Uni<Command> deserialization,
      String topic,
      boolean isKey,
      String deserializer,
      byte[] data,
      Headers headers) {
    int numberOfAttempts = 3;
    try {
      var deserializedCommand =
          deserialization
              .onFailure()
              .retry()
              .atMost(numberOfAttempts)
              .await()
              .atMost(Duration.ofMillis(200));

      if (deserializedCommand != null) {
        var violations = validator.validate(deserializedCommand);
        if (!violations.isEmpty()) {
          LOGGER.error(
              "Validation failed for message '{}' from topic '{}'. Violations:",
              new String(data),
              topic);
          violations.forEach(
              violation ->
                  LOGGER.error(
                      "- Violated property '{}': {}",
                      violation.getPropertyPath(),
                      violation.getMessage()));

          sendToDeadLetterQueue(
              data,
              headers,
              topic,
              isKey,
              deserializer,
              BEAN_VALIDATION_FAILURE,
              violations.stream()
                  .map(v -> "'%s': %s".formatted(v.getPropertyPath(), v.getMessage()))
                  .toList()
                  .toString());
          return null;
        }
        return deserializedCommand;
      }
    } catch (Exception e) {
      LOGGER.error(
          "Using deserializer {}, failed to deserialize message: {} with headers: {}",
          deserializer,
          new String(data),
          headers.toString());
      LOGGER.error(
          "Error deserializing message after %d attempts. Rerouting to dead letter queue"
              .formatted(numberOfAttempts),
          e);

      sendToDeadLetterQueue(
          data, headers, topic, isKey, deserializer, DESERIALIZATION_FAILURE, e.getMessage());
    }

    return null;
  }

  private void sendToDeadLetterQueue(
      byte[] data,
      Headers headers,
      String topic,
      boolean isKey,
      String deserializer,
      String failureType,
      String errorDetails) {
    var metadata =
        OutgoingKafkaRecordMetadata.<String>builder()
            .withHeaders(
                MessageMetadata.deadLetterHeaders(headers, topic, isKey, failureType, errorDetails)
                    .add("deserializer", deserializer.getBytes()))
            .build();

    LOGGER.info("Sending message with a size of {} bytes to the dead letter queue.", data.length);

    deadLetterProducer.sendMessage(Message.of(data).addMetadata(metadata), topic);
  }
}
