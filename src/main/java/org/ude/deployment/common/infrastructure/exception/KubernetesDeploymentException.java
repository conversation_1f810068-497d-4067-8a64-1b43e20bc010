package org.ude.deployment.common.infrastructure.exception;

import static org.ude.deployment.common.infrastructure.exception.ExceptionUtilsExtension.concatExceptionCause;

public class KubernetesDeploymentException extends CustomException {
  public KubernetesDeploymentException(String message, Throwable cause) {
    super(message, cause);
  }

  public static class KubernetesFlinkDeploymentException extends CustomException {
    public KubernetesFlinkDeploymentException(Throwable cause) {
      super(
          concatExceptionCause("Error deploying the kubernetes cluster due to the cause: ", cause),
          cause);
    }
  }

  public static class KubernetesDeploymentNotFoundException extends CustomException {
    public KubernetesDeploymentNotFoundException(String message, Throwable cause) {
      super(concatExceptionCause(message, cause), cause);
    }
  }
}
