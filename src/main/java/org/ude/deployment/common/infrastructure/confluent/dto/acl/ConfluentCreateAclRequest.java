package org.ude.deployment.common.infrastructure.confluent.dto.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

@Builder
@Getter
@AllArgsConstructor
@ToString
public class ConfluentCreateAclRequest {
  @JsonProperty("resource_type")
  @NotBlank private ConfluentAclResourceType resourceType;

  @JsonProperty("resource_name")
  @NotBlank private String resourceName;

  @JsonProperty("pattern_type")
  @NotBlank private ConfluentAclPatternType patternType;

  @JsonProperty("principal")
  @NotBlank private String principal;

  @JsonProperty("host")
  @NotBlank private String host;

  @JsonProperty("operation")
  @NotBlank private ConfluentAclOperation operation;

  @JsonProperty("permission")
  @NotBlank private ConfluentAclPermission permission;
}
