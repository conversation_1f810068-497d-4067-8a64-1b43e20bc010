package org.ude.deployment.common.infrastructure.confluent;

import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestPath;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityPool;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityProvider;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListResponse;

// https://api.confluent.cloud/
@Path("/iam/v2/identity-providers")
@RegisterRestClient(configKey = "confluent-identity-providers-service")
@RegisterClientHeaders(ConfluentApiAuthorisation.class)
public interface ConfluentIdentityProvidersClient {

  @GET
  Uni<ConfluentListResponse<ConfluentIdentityProvider>> getIdentityProviders();

  @Path("/{providerId}")
  @GET
  Uni<ConfluentIdentityProvider> getIdentityProvider(@RestPath String providerId);

  @Path("/{providerId}/identity-pools")
  @GET
  Uni<ConfluentListResponse<ConfluentIdentityPool>> getIdentityPools(
      @RestPath String providerId,
      @QueryParam("page_size") int pageSize,
      @QueryParam("page_token") String pageToken);

  @Path("/{providerId}/identity-pools")
  @POST
  Uni<ConfluentIdentityPool> createIdentityPool(
      @RestPath String providerId, ConfluentIdentityPool pool);

  @Path("/{providerId}/identity-pools/{poolId}")
  @PATCH
  Uni<ConfluentIdentityPool> updateIdentityPool(
      @RestPath String providerId, @RestPath String poolId, ConfluentIdentityPool pool);

  @Path("/{providerId}/identity-pools/{poolId}")
  @DELETE
  Uni<Response> deleteIdentityPool(@RestPath String providerId, @RestPath String poolId);
}
