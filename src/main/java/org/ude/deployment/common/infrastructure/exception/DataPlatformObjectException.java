package org.ude.deployment.common.infrastructure.exception;

import static org.ude.deployment.common.infrastructure.exception.ExceptionUtilsExtension.concatExceptionCause;

import java.util.List;
import lombok.Getter;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;

@Getter
public class DataPlatformObjectException extends CustomException {
  private final List<ProvisioningStep> provisioningSteps;

  public DataPlatformObjectException(
      String message, List<ProvisioningStep> statuses, Throwable throwable) {
    super(concatExceptionCause(message, throwable), throwable);
    this.provisioningSteps = statuses;
  }
}
