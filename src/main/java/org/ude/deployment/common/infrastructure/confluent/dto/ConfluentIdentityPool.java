package org.ude.deployment.common.infrastructure.confluent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class ConfluentIdentityPool extends ConfluentBaseEntity {
  @JsonProperty("identity_claim")
  private String identityClaim;

  @JsonProperty("filter")
  @Size(max = 300) private String filter;

  @JsonProperty("principal")
  private String principal;

  @JsonProperty("state")
  private String state;

  public ConfluentIdentityPool(
      String id, String identityClaim, String filter, String name, String description) {
    super(id, name, description);
    this.identityClaim = identityClaim;
    this.filter = filter;
  }
}
