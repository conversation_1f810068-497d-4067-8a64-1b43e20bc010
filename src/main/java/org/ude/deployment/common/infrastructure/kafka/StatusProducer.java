package org.ude.deployment.common.infrastructure.kafka;

import io.smallrye.reactive.messaging.MutinyEmitter;
import io.smallrye.reactive.messaging.kafka.api.OutgoingKafkaRecordMetadata;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.concurrent.CompletionStage;
import org.eclipse.microprofile.reactive.messaging.Channel;
import org.eclipse.microprofile.reactive.messaging.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.ude.deployment.common.status.Status;

@ApplicationScoped
public class StatusProducer {

  private static final Logger LOGGER = LoggerFactory.getLogger(StatusProducer.class);

  private final MutinyEmitter<Status> emitter;

  @Inject
  public StatusProducer(@Channel("status") MutinyEmitter<Status> emitter) {
    this.emitter = emitter;
  }

  public CompletionStage<Void> sendStatusUpdate(Status status) {
    LOGGER.info("Sending status update: '{}'", status);
    return emitter
        .sendMessage(
            Message.of(status)
                .addMetadata(
                    OutgoingKafkaRecordMetadata.<String>builder()
                        .with<PERSON>ey(status.commandId())
                        .build()))
        .subscribeAsCompletionStage();
  }
}
