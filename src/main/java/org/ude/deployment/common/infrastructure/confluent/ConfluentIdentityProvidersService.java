package org.ude.deployment.common.infrastructure.confluent;

import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityPool;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityProvider;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentListResponse;
import org.ude.deployment.common.util.HttpUtils;
import org.ude.deployment.dao.DaoGroupRole;
import org.ude.deployment.dao.dto.DataAccessObject;

@ApplicationScoped
public class ConfluentIdentityProvidersService {

  @ConfigProperty(name = "api.confluent.identity-provider")
  public String identityProviderId;

  @ConfigProperty(name = "api.confluent.identity-pool-name-template")
  public String identityPoolNameTemplate;

  @ConfigProperty(name = "api.confluent.identity-pool-description-template")
  public String identityPoolDescriptionTemplate;

  public static final String IDENTITY_CLAIM = "claims.sub";

  private final int POOLS_PAGE_SIZE = 100;

  private final ConfluentIdentityProvidersClient confluentIdentityProvidersClient;

  @Inject
  public ConfluentIdentityProvidersService(
      @RestClient ConfluentIdentityProvidersClient confluentIdentityProvidersClient) {
    this.confluentIdentityProvidersClient = confluentIdentityProvidersClient;
  }

  public Uni<ConfluentIdentityProvider> getIdentityProvider() {
    return confluentIdentityProvidersClient.getIdentityProvider(identityProviderId);
  }

  public String formatIdentityPoolName(String daoId, DaoGroupRole role) {
    return identityPoolNameTemplate.replace("{daoId}", daoId).replace("{role}", role.toString());
  }

  public ConfluentIdentityPool formatDataConsumerPool(DataAccessObject dao) {
    return new ConfluentIdentityPool(
        null,
        IDENTITY_CLAIM,
        "'%s' in claims.groups".formatted(dao.dataConsumerEntraGroupId()),
        formatIdentityPoolName(dao.id().toString(), DaoGroupRole.DATA_CONSUMER),
        identityPoolDescriptionTemplate
            .replace("{daoName}", dao.name())
            .replace("{role}", "Data Consumer"));
  }

  public ConfluentIdentityPool formatDataProducerPool(DataAccessObject dao) {
    return new ConfluentIdentityPool(
        null,
        IDENTITY_CLAIM,
        "'%s' in claims.groups".formatted(dao.dataProducerEntraGroupId()),
        formatIdentityPoolName(dao.id().toString(), DaoGroupRole.DATA_PRODUCER),
        identityPoolDescriptionTemplate
            .replace("{daoName}", dao.name())
            .replace("{role}", "Data Producer"));
  }

  public ConfluentIdentityPool formatDataOrderReaderPool(DataAccessObject dao) {
    return new ConfluentIdentityPool(
        null,
        IDENTITY_CLAIM,
        "'%s' in claims.groups".formatted(dao.dataOrderReaderEntraGroupId()),
        formatIdentityPoolName(dao.id().toString(), DaoGroupRole.DATA_ORDER_READER),
        identityPoolDescriptionTemplate
            .replace("{daoName}", dao.name())
            .replace("{role}", "Data Order Reader"));
  }

  public ConfluentIdentityPool formatDataOrderManagerPool(DataAccessObject dao) {
    return new ConfluentIdentityPool(
        null,
        IDENTITY_CLAIM,
        "'%s' in claims.groups".formatted(dao.dataOrderManagerEntraGroupId()),
        formatIdentityPoolName(dao.id().toString(), DaoGroupRole.DATA_ORDER_MANAGER),
        identityPoolDescriptionTemplate
            .replace("{daoName}", dao.name())
            .replace("{role}", "Data Order Manager"));
  }

  public Uni<List<ConfluentIdentityPool>> findDaoIdentityPools(String daoId) {
    var multi =
        Multi.createBy()
            .repeating()
            .<AtomicReference<String>, ConfluentListResponse<ConfluentIdentityPool>>uni(
                () -> new AtomicReference<String>(""),
                token -> {
                  var res =
                      confluentIdentityProvidersClient.getIdentityPools(
                          identityProviderId, POOLS_PAGE_SIZE, token.get());

                  return res.call(
                      result -> {
                        if (result.getMetadata() != null
                            && result.getMetadata().getNext() != null) {
                          var params = HttpUtils.parseQueryParams(result.getMetadata().getNext());
                          var pageToken = params.get("page_token");
                          if (pageToken == null) {
                            throw new RuntimeException(
                                "Invalid page_token in Confluent"
                                    + " getIdentityPools response: "
                                    + result.getMetadata().getNext());
                          }
                          token.set(pageToken);
                        }

                        var pools =
                            result.getData().stream()
                                .filter(pool -> pool.getDisplayName().contains(daoId))
                                .collect(Collectors.toList());
                        return Uni.createFrom()
                            .item(
                                new ConfluentListResponse<ConfluentIdentityPool>(
                                    result.getApiVersion(),
                                    result.getKind(),
                                    result.getMetadata(),
                                    pools));
                      });
                })
            .whilst(
                r -> {
                  return r != null && r.getMetadata() != null && r.getMetadata().getNext() != null;
                })
            .onItem()
            .transformToMultiAndConcatenate(
                r -> {
                  return Multi.createFrom().iterable(r.getData());
                });

    return multi.collect().asList();
  }

  public Uni<ConfluentIdentityPool> createIdentityPool(ConfluentIdentityPool pool) {
    return confluentIdentityProvidersClient.createIdentityPool(identityProviderId, pool);
  }

  public Uni<ConfluentIdentityPool> updateIdentityPool(String poolId, ConfluentIdentityPool pool) {
    return confluentIdentityProvidersClient.updateIdentityPool(identityProviderId, poolId, pool);
  }

  public Uni<Response> deleteIdentityPool(String poolId) {
    return confluentIdentityProvidersClient.deleteIdentityPool(identityProviderId, poolId);
  }
}
