package org.ude.deployment.common.infrastructure.kafka;

import io.smallrye.reactive.messaging.MutinyEmitter;
import io.smallrye.reactive.messaging.kafka.api.OutgoingKafkaRecordMetadata;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.concurrent.CompletableFuture;
import org.eclipse.microprofile.reactive.messaging.Channel;
import org.eclipse.microprofile.reactive.messaging.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.ude.deployment.common.command.Command;

@ApplicationScoped
public class CommandProducer {

  private static final Logger LOGGER = LoggerFactory.getLogger(CommandProducer.class);

  private final MutinyEmitter<Command> commandEmitter;

  @Inject
  public CommandProducer(@Channel("commands-out") MutinyEmitter<Command> commandEmitter) {
    this.commandEmitter = commandEmitter;
  }

  public CompletableFuture<Void> sendCommand(Command command) {
    LOGGER.info("Sending command: '{}'", command);

    return commandEmitter
        .sendMessage(
            Message.of(command)
                .addMetadata(
                    OutgoingKafkaRecordMetadata.<String>builder()
                        .with<PERSON><PERSON>(command.commandId())
                        .build()))
        .subscribeAsCompletionStage();
  }
}
