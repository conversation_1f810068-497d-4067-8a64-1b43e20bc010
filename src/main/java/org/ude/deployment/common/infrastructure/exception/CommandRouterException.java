package org.ude.deployment.common.infrastructure.exception;

import static org.ude.deployment.common.infrastructure.exception.ExceptionUtilsExtension.concatExceptionCause;

public class CommandRouterException extends CustomException {
  public CommandRouterException(String message, Throwable cause) {
    super(message, cause);
  }

  public static class MessageSerializationException extends CommandRouterException {
    public MessageSerializationException(Throwable cause) {
      super(
          concatExceptionCause("Error writing message as bytes due to the cause: ", cause), cause);
    }
  }
}
