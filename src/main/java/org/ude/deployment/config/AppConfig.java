package org.ude.deployment.config;

import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.config.inject.ConfigProperty;

@ApplicationScoped
public class AppConfig {

  public static final String SKIPPING_CONFLUENT_IDENTITY_POOL_MESSAGE =
      "confluent identity pool is not enabled";

  @ConfigProperty(name = "api.confluent.use-identity-pool")
  private boolean useConfluentIdentityPool;

  public boolean useConfluentIdentityPool() {
    return useConfluentIdentityPool;
  }
}
