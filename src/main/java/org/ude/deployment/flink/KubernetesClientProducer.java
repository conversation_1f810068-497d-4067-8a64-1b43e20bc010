package org.ude.deployment.flink;

import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClientBuilder;
import io.quarkus.logging.Log;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Singleton;

@ApplicationScoped
public class KubernetesClientProducer {

  @Produces
  @Singleton
  public KubernetesClient kubernetesClient() {
    var kubernetesClient = new KubernetesClientBuilder().build();
    Log.infof(
        "Kubernetes client created with master URL: %s",
        kubernetesClient.authentication().getMasterUrl());
    return kubernetesClient;
  }
}
