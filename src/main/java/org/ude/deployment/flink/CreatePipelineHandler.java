package org.ude.deployment.flink;

import static org.ude.deployment.common.command.CommandUtils.mapToType;
import static org.ude.deployment.common.status.StatusUtils.toDataPlatformObjectProvisionException;
import static org.ude.deployment.flink.dto.DeploymentType.GDC_PIPELINE;
import static org.ude.deployment.flink.dto.DeploymentType.PASSTHROUGH_PIPELINE;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionStage;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.flink.dto.DeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.service.FlinkDeploymentService;

@ApplicationScoped
public class CreatePipelineHandler implements CommandHandler {
  public static final CommandType COMMAND_TYPE = CommandType.CREATE;

  private final ObjectMapper objectMapper;
  private final FlinkDeploymentService flinkDeploymentService;
  private final StatusUpdateService statusUpdateService;
  private final FlinkEnvVariableConfig flinkEnvVariableConfig;
  private final Map<DeploymentType, String> deploymentTypeMap =
      Map.of(
          GDC_PIPELINE, "gdc-flink-deployment.yaml",
          PASSTHROUGH_PIPELINE, "passthrough-flink-deployment.yaml");

  @Inject
  public CreatePipelineHandler(
      ObjectMapper objectMapper,
      FlinkDeploymentService flinkDeploymentService,
      StatusUpdateService statusUpdateService,
      FlinkEnvVariableConfig flinkEnvVariableConfig) {
    this.objectMapper = objectMapper;
    this.flinkDeploymentService = flinkDeploymentService;
    this.statusUpdateService = statusUpdateService;
    this.flinkEnvVariableConfig = flinkEnvVariableConfig;
  }

  @Override
  public CompletionStage<Void> handle(Command command) {
    var deploymentConfig = objectMapper.convertValue(command.properties(), DeploymentConfig.class);
    var deploymentMap =
        deploymentConfig
            .flinkDeploymentConfig()
            .getPropertiesMap(flinkEnvVariableConfig, deploymentConfig);
    var streamingPipelineCommand = mapToType(command, ConfigurationType.STREAMING_PIPELINE);
    var defaultProvisioningStep = ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING;
    var provisioningSteps = determineProvisioningSteps();

    return Uni.createFrom()
        .completionStage(
            () ->
                statusUpdateService.initializeWithCompletedSteps(
                    streamingPipelineCommand,
                    COMMAND_TYPE,
                    createCompletedDbPipelineRegistrationSteps(),
                    provisioningSteps))
        .chain(
            () ->
                Uni.createFrom()
                    .completionStage(
                        () ->
                            statusUpdateService.provisioning(
                                streamingPipelineCommand, COMMAND_TYPE, defaultProvisioningStep)))
        .chain(
            () ->
                Uni.createFrom()
                    .optional(
                        () ->
                            flinkDeploymentService.createIfNotExists(
                                deploymentTypeMap.get(deploymentConfig.deploymentType()),
                                deploymentMap))
                    .runSubscriptionOn(Infrastructure.getDefaultWorkerPool()))
        .chain(
            () ->
                Uni.createFrom()
                    .completionStage(
                        () ->
                            statusUpdateService.completed(
                                streamingPipelineCommand,
                                COMMAND_TYPE,
                                defaultProvisioningStep,
                                null)))
        .onFailure()
        .transform(e -> toDataPlatformObjectProvisionException(e, defaultProvisioningStep))
        .onFailure()
        .recoverWithUni(
            throwable ->
                Uni.createFrom()
                    .completionStage(
                        () ->
                            statusUpdateService.failed(
                                streamingPipelineCommand,
                                COMMAND_TYPE,
                                defaultProvisioningStep,
                                throwable)))
        .subscribeAsCompletionStage();
  }

  private List<ProvisioningStep> determineProvisioningSteps() {
    return List.of(ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING);
  }

  private List<ProvisioningStep> createCompletedDbPipelineRegistrationSteps() {
    return List.of(
        ProvisioningStep.OBJECT_REGISTRATION, ProvisioningStep.UPDATE_PIPELINES_MAPPING_LIST);
  }
}
