package org.ude.deployment.flink;

import io.fabric8.kubernetes.client.KubernetesClient;
import io.quarkus.rest.client.reactive.QuarkusRestClientBuilder;
import jakarta.inject.Singleton;
import java.net.URI;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.service.FlinkRestApiService;

@Singleton
@AllArgsConstructor
public final class FlinkRestApi {
  @NonNull private final KubernetesClient kubernetesClient;
  @NonNull private final DeploymentNameResolver deploymentNameResolver;

  public Optional<FlinkRestApiService> service(
      final String namespace, final DeploymentType type, final String dataOrderId) {
    return Optional.ofNullable(
            this.kubernetesClient
                .services()
                .inNamespace(namespace)
                .withName(
                    "%s-rest"
                        .formatted(deploymentNameResolver.resolve(type.getPrefix(), dataOrderId)))
                .get())
        .map(
            service ->
                QuarkusRestClientBuilder.newBuilder()
                    .baseUri(
                        URI.create(
                            "http://%s:%d"
                                .formatted(
                                    service.getSpec().getClusterIP(),
                                    service.getSpec().getPorts().getFirst().getPort())))
                    .build(FlinkRestApiService.class));
  }
}
