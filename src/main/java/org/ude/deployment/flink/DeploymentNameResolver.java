package org.ude.deployment.flink;

import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.config.inject.ConfigProperty;

@ApplicationScoped
public class DeploymentNameResolver {
  private static final String DEPLOYMENT_TYPE_PLACEHOLDER = "{deploymentType}";
  private static final String DATA_ORDER_ID_PLACEHOLDER = "{dataOrderId}";

  private final String deploymentNameTemplate;

  public DeploymentNameResolver(
      @ConfigProperty(name = "flink-deployment-config.deployment-name-template")
          String inputTopicNameTemplate) {
    this.deploymentNameTemplate = inputTopicNameTemplate;
  }

  public String resolve(String jobPrefix, String dataOrderId) {
    return deploymentNameTemplate
        .replace(DEPLOYMENT_TYPE_PLACEHOLDER, jobPrefix)
        .replace(DATA_ORDER_ID_PLACEHOLDER, dataOrderId);
  }
}
