package org.ude.deployment.flink;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import io.smallrye.mutiny.unchecked.Unchecked;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectProvisionException;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.flink.dto.DeploymentConfig;
import org.ude.deployment.flink.dto.DeploymentType;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.dto.RetentionPeriod;
import org.ude.deployment.flink.dto.command.UpdatePipelineCommand;
import org.ude.deployment.flink.service.FlinkDeploymentService;
import org.ude.deployment.streamingpipeline.config.factory.DeploymentConfigFactoryProvider;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig;

@ApplicationScoped
@AllArgsConstructor
public final class UpdatePipelineHandler implements CommandHandler {
  private static final CommandType COMMAND_TYPE = CommandType.UPDATE;

  @NonNull private final CreatePipelineHandler createPipelineHandler;
  @NonNull private final DeploymentConfigFactoryProvider deploymentConfigFactoryProvider;
  @NonNull private final FlinkDeploymentService flinkDeploymentService;
  @NonNull private final FlinkRestApi flinkRestApi;
  @NonNull private final ObjectMapper objectMapper;
  @NonNull private final StatusUpdateService statusUpdateService;
  @NonNull private final FlinkEnvVariableConfig flinkEnvVariableConfig;

  @Override
  public CompletionStage<Void> handle(final Command rawCommand) {
    final var cmdUpdatePipeline =
        rawCommand.convertTo(this.objectMapper, UpdatePipelineCommand.class);

    if (cmdUpdatePipeline.metadataProperties().isEmpty()
        && cmdUpdatePipeline.additionalProperties().isEmpty()) {
      Log.info(
          "No data to update pipelineId=%s".formatted(cmdUpdatePipeline.pipeline().pipelineId()));
      return CompletableFuture.completedFuture(null);
    }

    final var category = cmdUpdatePipeline.pipeline().category().getDeploymentType();
    final var dataOrderId = cmdUpdatePipeline.dataOrderId().toString();

    final var flinkRestApiService =
        this.flinkRestApi.service(this.flinkEnvVariableConfig.namespace, category, dataOrderId);
    var defaultProvisioningStep =
        DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING;
    return Uni.createFrom()
        .completionStage(
            () -> statusUpdateService.deleting(rawCommand, COMMAND_TYPE, defaultProvisioningStep))
        .chain(() -> Uni.createFrom().item(() -> flinkRestApiService))
        .chain(
            optionalFlinkApiService -> {
              if (optionalFlinkApiService.isEmpty()) {
                throw new DataPlatformObjectProvisionException(
                    List.of("Unable to find the rest service"), List.of(defaultProvisioningStep));
              }
              return Uni.createFrom()
                  .item(() -> optionalFlinkApiService.get().jobManagerConfig().udeSpecific())
                  .runSubscriptionOn(Infrastructure.getDefaultWorkerPool());
            })
        .chain(
            udeJobManagerConfig ->
                Uni.join()
                    .all(
                        Multi.createFrom()
                            .iterable(flinkRestApiService.get().jobListAll().jobs())
                            .onItem()
                            .invoke(
                                job -> {
                                  Log.info(
                                      "Canceling jobId=%s via Flink Rest API".formatted(job.id()));
                                  flinkRestApiService.get().jobCancelById(job.id());
                                })
                            .collect()
                            .asList())
                    .andCollectFailures()
                    .onFailure()
                    .retry()
                    .withBackOff(Duration.ofSeconds(3))
                    .atMost(3)
                    .onFailure()
                    .recoverWithNull()
                    .chain(
                        () ->
                            Uni.createFrom()
                                .item(
                                    () ->
                                        this.flinkDeploymentService.delete(
                                            this.flinkEnvVariableConfig.namespace,
                                            category,
                                            dataOrderId)))
                    .map(
                        Unchecked.function(
                            unused ->
                                this.deploymentConfigFactoryProvider
                                    .getDeploymentConfigFactory(category)
                                    .createDeploymentConfig(
                                        PipelineDataOrderPropertiesConfig.builder()
                                            .dataOrderId(dataOrderId)
                                            .metadataProperties(
                                                cmdUpdatePipeline.metadataProperties().orElse(null))
                                            .additionalProperties(
                                                cmdUpdatePipeline
                                                    .additionalProperties()
                                                    .orElse(null))
                                            .name(udeJobManagerConfig.pipeline().name())
                                            .schemaIds(cmdUpdatePipeline.schemaIds())
                                            .dataOrder(Optional.empty())
                                            .retentionPeriod(
                                                cmdUpdatePipeline.configuration().retentionPeriod())
                                            .build())))
                    .map(
                        baseDeploymentConfig ->
                            DeploymentConfig.builder()
                                .projectId(udeJobManagerConfig.pipeline().projectId())
                                .deploymentType(category)
                                .dataOrderId(dataOrderId)
                                .pipelineName(udeJobManagerConfig.pipeline().name())
                                .retentionPeriod(
                                    RetentionPeriod.ofDays(
                                        cmdUpdatePipeline.configuration().retentionPeriod()))
                                .flinkDeploymentConfig(baseDeploymentConfig)
                                .build())
                    .chain(
                        deploymentConfig ->
                            Uni.createFrom()
                                .completionStage(
                                    this.createPipelineHandler.handle(
                                        new Command(
                                            UUID.randomUUID().toString(),
                                            CommandType.CREATE,
                                            rawCommand.objectId(),
                                            ConfigurationType.FLINK_JOB_PIPELINE,
                                            JsonObject.mapFrom(deploymentConfig)))))
                    .chain(
                        () ->
                            category.equals(DeploymentType.GDC_PIPELINE)
                                ? Uni.createFrom()
                                    .completionStage(
                                        this.statusUpdateService.completed(
                                            rawCommand,
                                            COMMAND_TYPE,
                                            defaultProvisioningStep,
                                            null))
                                : Uni.createFrom().voidItem()))
        .onFailure()
        .recoverWithUni(
            throwable ->
                Uni.createFrom()
                    .completionStage(
                        this.statusUpdateService.failed(
                            rawCommand, COMMAND_TYPE, defaultProvisioningStep, throwable)))
        .convert()
        .toCompletionStage();
  }
}
