package org.ude.deployment.flink.service.dto.spec;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public final class FlinkJobManagerConfig extends ArrayList<FlinkJsonKeyValue> {
  private static final String UDE_SPECIFIC_PREFIX = "_ude.";

  public FlinkJobManagerConfigUde udeSpecific() {
    final var lst =
        Arrays.stream(this.toArray(FlinkJsonKeyValue[]::new))
            .filter(e -> e.key().startsWith(UDE_SPECIFIC_PREFIX))
            .toList();
    return new FlinkJobManagerConfigUde(
        new FlinkJobManagerConfigUde.FlinkJobManagerConfigUdePipeline(
            this.findUdeValue(lst, "pipeline.name").orElseThrow(),
            this.findUdeValue(lst, "pipeline.dataOrderId").orElseThrow(),
            this.findUdeValue(lst, "pipeline.projectId").orElseThrow(),
            Integer.valueOf(this.findUdeValue(lst, "pipeline.revision").orElse("1"))));
  }

  private Optional<String> findUdeValue(final List<FlinkJsonKeyValue> lst, final String key) {
    return lst.stream()
        .filter(e -> e.key().equals(UDE_SPECIFIC_PREFIX + key))
        .map(FlinkJsonKeyValue::value)
        .findAny();
  }
}
