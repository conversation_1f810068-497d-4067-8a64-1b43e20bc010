package org.ude.deployment.flink.service;

import com.github.jknack.handlebars.EscapingStrategy;
import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.helper.ConditionalHelpers;
import io.fabric8.kubernetes.api.model.DeletionPropagation;
import io.fabric8.kubernetes.api.model.HasMetadata;
import io.fabric8.kubernetes.api.model.StatusDetails;
import io.fabric8.kubernetes.client.KubernetesClient;
import jakarta.enterprise.context.ApplicationScoped;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import org.apache.flink.kubernetes.operator.api.FlinkDeployment;
import org.ude.deployment.flink.DeploymentNameResolver;
import org.ude.deployment.flink.dto.DeploymentType;

@ApplicationScoped
@AllArgsConstructor
public class FlinkDeploymentService {
  private final KubernetesClient kubernetesClient;
  private final DeploymentNameResolver deploymentNameResolver;

  public Optional<HasMetadata> createIfNotExists(
      final String tplName, final Map<String, String> tplValues) {
    final var resource = this.kubernetesClient.resource(this.template(tplName, tplValues));
    return Objects.isNull(resource.get()) ? Optional.of(resource.create()) : Optional.empty();
  }

  public List<StatusDetails> delete(
      final String namespace, final DeploymentType deploymentType, final String dataOrderId) {
    final var flinkDeployment =
        this.findByOrderId(namespace, deploymentType, dataOrderId).orElseThrow();
    return this.kubernetesClient
        .resources(flinkDeployment.getClass())
        .inNamespace(namespace)
        .withName(deploymentNameResolver.resolve(deploymentType.getPrefix(), dataOrderId))
        .withPropagationPolicy(DeletionPropagation.FOREGROUND)
        .withTimeout(2, TimeUnit.MINUTES)
        .delete();
  }

  public Optional<FlinkDeployment> findByOrderId(
      final String namespace, final DeploymentType deploymentType, final String dataOrderId) {
    return Optional.ofNullable(
        this.kubernetesClient
            .resources(FlinkDeployment.class)
            .inNamespace(namespace)
            .withName(deploymentNameResolver.resolve(deploymentType.getPrefix(), dataOrderId))
            .get());
  }

  String template(final String templateName, final Map<String, String> values) {
    final var handlebars = new Handlebars();
    handlebars.with(EscapingStrategy.NOOP);
    handlebars.registerHelpers(ConditionalHelpers.class);
    handlebars.registerHelperMissing(
        (context, options) -> {
          throw new IllegalArgumentException(
              "No value found for '%s'".formatted(options.helperName));
        });

    try (final InputStream templateStream =
        this.getClass().getResourceAsStream("/kubernetes/" + templateName)) {
      final var tpl =
          new String(Objects.requireNonNull(templateStream).readAllBytes(), StandardCharsets.UTF_8);
      return handlebars.compileInline(tpl).apply(values);
    } catch (final IOException e) {
      throw new RuntimeException("Unable to load the '%s' template".formatted(templateName), e);
    }
  }
}
