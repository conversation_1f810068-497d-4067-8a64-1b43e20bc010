package org.ude.deployment.flink.service;

import jakarta.validation.Valid;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.ude.deployment.flink.service.dto.request.JobStopRequest;
import org.ude.deployment.flink.service.dto.spec.FlinkJobList;
import org.ude.deployment.flink.service.dto.spec.FlinkJobManagerConfig;

@RegisterRestClient
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface FlinkRestApiService {

  @GET
  @Path("/jobs")
  FlinkJobList jobListAll();

  @PATCH
  @Path("/jobs/{jobId}")
  void jobCancelById(@PathParam("jobId") String jobId);

  @POST
  @Path("/jobs/{jobId}/stop")
  void jobStopById(@PathParam("jobId") String jobId, @Valid JobStopRequest request);

  @GET
  @Path("/jobmanager/config")
  FlinkJobManagerConfig jobManagerConfig();
}
