package org.ude.deployment.flink.service.dto.spec;

public enum FlinkJobState {
  // Job is initializing (initial state when job is being set up)
  INITIALIZING,

  // Job has been created but not yet submitted to the JobManager
  CREATED,

  // Job has been submitted and is currently running
  RUNNING,

  // <PERSON> is failing and producing a stack trace
  FAILING,

  // <PERSON> has failed with an exception
  FAILED,

  // Job is being canceled
  CANCELLING,

  // <PERSON> has been canceled
  CANCELED,

  // Job has finished successfully
  FINISHED,

  // Job is being restarted after a failure
  RESTARTING,

  // Job has been suspended (stopped but can be resumed)
  SUSPENDED,

  // Job is in reconciliation state (used by Flink Kubernetes Operator)
  RECONCILING
}
