package org.ude.deployment.flink.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NonNull;

public record ColdStorageConfiguration(
    @NonNull String format,
    @NonNull @JsonProperty("batchConfiguration")
        ColdStorageRollingConfiguration rollingConfiguration,
    int retentionPeriod) {
  public record ColdStorageRollingConfiguration(short batchFrequency, int maxBatchSize) {}
}
