package org.ude.deployment.flink.dto;

import jakarta.inject.Singleton;
import org.eclipse.microprofile.config.inject.ConfigProperty;

@Singleton
public class FlinkEnvVariableConfig {

  @ConfigProperty(name = "flink-deployment-config.namespace")
  public String namespace;

  @ConfigProperty(name = "flink-deployment-config.flink-gdc-job-image-uri")
  public String flinkGdcJobName;

  @ConfigProperty(name = "flink-deployment-config.flink-passthrough-job-image-uri")
  public String flinkPassthroughJobName;

  @ConfigProperty(name = "flink-deployment-config.flink-init-container-image-uri")
  public String flinkInitContainerImageName;

  @ConfigProperty(name = "flink-deployment-config.schema-registry-url")
  public String schemaRegistry;

  @ConfigProperty(name = "flink-deployment-config.bootstrap-server")
  public String bootstrapServer;

  @ConfigProperty(name = "flink-deployment-config.database-url")
  public String databaseUrl;

  @ConfigProperty(name = "flink-deployment-config.database-user-name")
  public String databaseUserName;

  @ConfigProperty(name = "flink-deployment-config.claims-database-name")
  public String claimsDatabaseName;

  @ConfigProperty(name = "flink-deployment-config.claims-table-name")
  public String claimsTableName;

  @ConfigProperty(name = "flink-deployment-config.vdc-database-name")
  public String vdcDatabaseName;

  @ConfigProperty(name = "flink-deployment-config.vdc-rules-table-name")
  public String vdcRulesTableName;

  @ConfigProperty(name = "flink-deployment-config.vms-input-topic-name")
  public String vmsInputTopicName;

  @ConfigProperty(name = "flink-deployment-config.job-manager-memory", defaultValue = "1Gi")
  public String jobManagerMemory;

  @ConfigProperty(name = "flink-deployment-config.job-manager-cpu", defaultValue = "1")
  public int jobManagerCpu;

  @ConfigProperty(name = "flink-deployment-config.task-manager-memory", defaultValue = "4Gi")
  public String taskManagerMemory;

  @ConfigProperty(name = "flink-deployment-config.task-manager-cpu", defaultValue = "6")
  public int taskManagerCpu;
}
