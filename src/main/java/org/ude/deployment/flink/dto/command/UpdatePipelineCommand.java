package org.ude.deployment.flink.dto.command;

import io.vertx.core.json.JsonObject;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.Builder;
import lombok.NonNull;
import org.ude.deployment.dataorder.dto.DataOrder;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig.MetadataProperties;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineRequest;

@Builder
public record UpdatePipelineCommand(
    @NonNull UUID dataOrderId,
    @NonNull StreamingPipelineRequest pipeline,
    @NonNull List<String> schemaIds,
    @NonNull DataOrder.DataOrderConfiguration configuration,
    Optional<JsonObject> additionalProperties,
    Optional<MetadataProperties> metadataProperties) {
  public static UpdatePipelineCommandBuilder builder(
      final UUID dataOrderId,
      final StreamingPipelineRequest pipeline,
      final List<String> schemaIds,
      final DataOrder.DataOrderConfiguration configuration) {
    return new UpdatePipelineCommandBuilder()
        .dataOrderId(dataOrderId)
        .pipeline(pipeline)
        .schemaIds(schemaIds)
        .configuration(configuration);
  }
}
