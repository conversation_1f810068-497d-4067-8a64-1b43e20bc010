package org.ude.deployment.flink.dto;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "deploymentType")
@JsonSubTypes({
  @JsonSubTypes.Type(value = PassthroughDeploymentConfig.class, name = "PASSTHROUGH_PIPELINE"),
  @JsonSubTypes.Type(value = GdcFlinkDeploymentConfig.class, name = "GDC_PIPELINE")
})
public interface BaseDeploymentConfig extends Serializable {

  Map<String, String> getPropertiesMap(
      FlinkEnvVariableConfig flinkEnvVariableConfig, DeploymentConfig deploymentConfig);

  default Map<String, String> getMapFromDeploymentConfig(
      FlinkEnvVariableConfig flinkEnvVariableConfig, DeploymentConfig deploymentConfig) {
    var propertiesMap = new HashMap<String, String>();
    propertiesMap.put("NAMESPACE", flinkEnvVariableConfig.namespace);
    propertiesMap.put("BOOTSTRAP_SERVERS", flinkEnvVariableConfig.bootstrapServer);
    propertiesMap.put("SCHEMA_REGISTRY_URL", flinkEnvVariableConfig.schemaRegistry);
    propertiesMap.put("DB_URL", flinkEnvVariableConfig.databaseUrl);
    propertiesMap.put("DB_USER_NAME", flinkEnvVariableConfig.databaseUserName);
    propertiesMap.put("CLAIMS_DATABASE_NAME", flinkEnvVariableConfig.claimsDatabaseName);
    propertiesMap.put("CLAIMS_PROCESSING_TABLE_NAME", flinkEnvVariableConfig.claimsTableName);
    propertiesMap.put("USE_CASE_ID", deploymentConfig.dataOrderId());
    propertiesMap.put("PROJECT_ID", deploymentConfig.projectId());
    propertiesMap.put("DATA_ORDER_ID", deploymentConfig.dataOrderId());
    propertiesMap.put(
        "DATA_ORDER_RETENTION_DAYS", String.valueOf(deploymentConfig.retentionPeriod().toDays()));
    var pipelineName = deploymentConfig.pipelineName();
    propertiesMap.put(
        "PIPELINE_NAME", pipelineName.substring(0, Math.min(pipelineName.length(), 63)));
    return propertiesMap;
  }
}
