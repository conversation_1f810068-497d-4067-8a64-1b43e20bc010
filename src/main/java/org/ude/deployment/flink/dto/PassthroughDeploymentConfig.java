package org.ude.deployment.flink.dto;

import java.util.Map;

public record PassthroughDeploymentConfig(
    String deploymentName,
    String jobImageName,
    String inputTopicName,
    String outputTopicName,
    String nonRetryableErrorTopicName)
    implements BaseDeploymentConfig {

  @Override
  public Map<String, String> getPropertiesMap(
      FlinkEnvVariableConfig flinkEnvVariableConfig, DeploymentConfig deploymentConfig) {
    var propertiesMap = getMapFromDeploymentConfig(flinkEnvVariableConfig, deploymentConfig);
    propertiesMap.put("FLINK_APPLICATION_NAME", deploymentName);
    propertiesMap.put("FLINK_IMAGE_NAME", jobImageName);
    propertiesMap.put("FLINK_INPUT_TOPIC", inputTopicName);
    propertiesMap.put("FLINK_OUTPUT_TOPIC", outputTopicName);
    propertiesMap.put("NON_RETRYABLE_DLQ_TOPIC", nonRetryableErrorTopicName);
    propertiesMap.put("FLINK_GROUP_ID", deploymentName);
    return propertiesMap;
  }
}
