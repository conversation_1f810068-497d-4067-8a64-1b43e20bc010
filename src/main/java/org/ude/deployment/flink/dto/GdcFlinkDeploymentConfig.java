package org.ude.deployment.flink.dto;

import java.util.Map;
import java.util.Optional;
import lombok.Builder;
import lombok.NonNull;

@Builder(toBuilder = true)
public record GdcFlinkDeploymentConfig(
    String deploymentName,
    String jobImageName,
    String flinkInitContainerImageName,
    String inputTopicName,
    String outputTopicName,
    String vdcDatabaseName,
    String vdcRulesTableName,
    String nonRetryableErrorTopicName,
    String storageSchemaId,
    String enrichmentConfig,
    String fctRules,
    @NonNull Optional<TopologyFeatureSignalPackaging> featureSignalPackaging,
    @NonNull Optional<TopologyFeatureSinkColdStorage> featureSinkColdStorage)
    implements BaseDeploymentConfig {

  @Override
  public Map<String, String> getPropertiesMap(
      FlinkEnvVariableConfig flinkEnvVariableConfig, DeploymentConfig deploymentConfig) {
    var propertiesMap = getMapFromDeploymentConfig(flinkEnvVariableConfig, deploymentConfig);
    propertiesMap.put("ENRICHMENT_CONFIG", enrichmentConfig);
    propertiesMap.put("FCT_RULES", fctRules);
    propertiesMap.put("FLINK_APPLICATION_NAME", deploymentName);
    propertiesMap.put("FLINK_IMAGE_NAME", jobImageName);
    propertiesMap.put("FLINK_INIT_CONTAINER_IMAGE_NAME", flinkInitContainerImageName);
    propertiesMap.put("FLINK_INPUT_TOPIC", inputTopicName);
    propertiesMap.put("FLINK_OUTPUT_TOPIC", outputTopicName);
    propertiesMap.put("NON_RETRYABLE_DLQ_TOPIC", nonRetryableErrorTopicName);
    propertiesMap.put("STORAGE_SCHEMA_ID", storageSchemaId);
    propertiesMap.put("VDC_DATABASE_NAME", vdcDatabaseName);
    propertiesMap.put("VDC_RULES_TABLE_NAME", vdcRulesTableName);

    this.featureSignalPackaging.ifPresent(p -> propertiesMap.putAll(p.toMap()));

    // for now two sinks are not allowed (but must be possible in future)
    if (this.featureSinkColdStorage.isPresent()) {
      propertiesMap.putAll(this.featureSinkColdStorage.get().toMap());
      propertiesMap.putAll(new TopologyFeatureSinkApacheKafka(false).toMap());
    } else {
      propertiesMap.putAll(new TopologyFeatureSinkApacheKafka(true).toMap());
    }

    return propertiesMap;
  }

  @Builder
  public record TopologyFeatureSinkApacheKafka(boolean isEnabled) {
    public Map<String, String> toMap() {
      return Map.of("FEATURE_SINK_APACHE_KAFKA_ENABLED", Boolean.toString(this.isEnabled()));
    }
  }

  @Builder
  public record TopologyFeatureSignalPackaging(boolean isEnabled) {
    public Map<String, String> toMap() {
      return Map.of("FEATURE_SIGNAL_PACKAGING_ENABLED", Boolean.toString(this.isEnabled()));
    }
  }

  @Builder
  public record TopologyFeatureSinkColdStorage(
      boolean isEnabled,
      int maxBatchSize,
      short aggregationTimeoutS,
      @NonNull RetentionPeriod retentionPeriod,
      @NonNull Optional<String> containerNameTpl) {
    public Map<String, String> toMap() {
      return Map.of(
          "FEATURE_SINK_COLD_STORAGE_ENABLED", Boolean.toString(this.isEnabled()),
          "FEATURE_SINK_COLD_STORAGE_MAX_BATCH_SIZE", String.valueOf(this.maxBatchSize()),
          "FEATURE_SINK_COLD_STORAGE_AGGREGATION_TIMEOUT",
              String.valueOf(this.aggregationTimeoutS()),
          "FEATURE_SINK_COLD_STORAGE_RETENTION_DAYS",
              String.valueOf(this.retentionPeriod().toDays()),
          "FEATURE_SINK_COLD_STORAGE_CONTAINER_NAME_TPL",
              this.containerNameTpl()
                  .orElse("{retentionDays}d-private/{schemaId}/{vehicleId}/{userId}/{yyyyMMdd}"));
    }
  }
}
