package org.ude.deployment.flink.dto;

import java.util.Map;
import lombok.Builder;

@Builder
public record GdcFlinkDeploymentConfig(
    String deploymentName,
    String jobImageName,
    String flinkInitContainerImageName,
    String inputTopicName,
    String outputTopicName,
    String vmsTopicName,
    String vdcDatabaseName,
    String vdcRulesTableName,
    String nonRetryableErrorTopicName,
    String storageSchemaId,
    String enrichmentConfig,
    String fctRules,
    boolean featureSignalPackagingEnabled)
    implements BaseDeploymentConfig {

  @Override
  public Map<String, String> getPropertiesMap(
      FlinkEnvVariableConfig flinkEnvVariableConfig, DeploymentConfig deploymentConfig) {
    var propertiesMap = getMapFromDeploymentConfig(flinkEnvVariableConfig, deploymentConfig);
    propertiesMap.put("ENRICHMENT_CONFIG", enrichmentConfig);
    propertiesMap.put("FCT_RULES", fctRules);
    propertiesMap.put("FLINK_APPLICATION_NAME", deploymentName);
    propertiesMap.put("FLINK_IMAGE_NAME", jobImageName);
    propertiesMap.put("FLINK_INIT_CONTAINER_IMAGE_NAME", flinkInitContainerImageName);
    propertiesMap.put("FLINK_INPUT_TOPIC", inputTopicName);
    propertiesMap.put("FLINK_OUTPUT_TOPIC", outputTopicName);
    propertiesMap.put("NON_RETRYABLE_DLQ_TOPIC", nonRetryableErrorTopicName);
    propertiesMap.put("STORAGE_SCHEMA_ID", storageSchemaId);
    propertiesMap.put("VDC_DATABASE_NAME", vdcDatabaseName);
    propertiesMap.put("VDC_RULES_TABLE_NAME", vdcRulesTableName);
    propertiesMap.put("VMS_INPUT_TOPIC", vmsTopicName);
    propertiesMap.put(
        "FEATURE_SIGNAL_PACKAGING_ENABLED", Boolean.toString(this.featureSignalPackagingEnabled));
    return propertiesMap;
  }
}
