package org.ude.deployment.flink;

import static org.ude.deployment.common.command.CommandUtils.mapToTypeWithNewObjectId;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.fabric8.kubernetes.api.model.StatusDetails;
import io.fabric8.kubernetes.client.KubernetesClientException;
import io.quarkus.logging.Log;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.CommandProducer;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.dataorder.dto.ModifyDataOrderCommand;
import org.ude.deployment.flink.dto.FlinkEnvVariableConfig;
import org.ude.deployment.flink.service.FlinkDeploymentService;
import org.ude.deployment.streamingpipeline.dto.DeleteStreamingPipelineProperties;
import org.ude.deployment.streamingpipeline.dto.StreamingPipelineRequest;

@ApplicationScoped
public class DeletePipelineHandler implements CommandHandler {
  private static final CommandType COMMAND_TYPE = CommandType.DELETE;

  private final ObjectMapper objectMapper;
  private final FlinkDeploymentService flinkDeploymentService;
  private final FlinkRestApi flinkRestApi;
  private final StatusUpdateService statusUpdateService;
  private final FlinkEnvVariableConfig flinkEnvVariableConfig;
  private final CommandProducer commandProducer;

  @Inject
  public DeletePipelineHandler(
      ObjectMapper objectMapper,
      FlinkDeploymentService flinkDeploymentService,
      FlinkRestApi flinkRestApi,
      CommandProducer commandProducer,
      StatusUpdateService statusUpdateService,
      FlinkEnvVariableConfig flinkEnvVariableConfig) {
    this.objectMapper = objectMapper;
    this.flinkDeploymentService = flinkDeploymentService;
    this.flinkRestApi = flinkRestApi;
    this.statusUpdateService = statusUpdateService;
    this.flinkEnvVariableConfig = flinkEnvVariableConfig;
    this.commandProducer = commandProducer;
  }

  @Override
  public CompletionStage<Void> handle(Command command) {
    DeleteStreamingPipelineProperties deletePipelineProperties;
    try {
      deletePipelineProperties =
          objectMapper.convertValue(command.properties(), DeleteStreamingPipelineProperties.class);

      return statusUpdateService
          .deleting(
              command,
              COMMAND_TYPE,
              DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING)
          .thenCompose(v -> cancelFlinkJobs(deletePipelineProperties.request()))
          .thenCompose(v -> deleteFlinkDeployments(deletePipelineProperties.request()))
          .thenCompose(
              result ->
                  statusUpdateService.deleted(
                      mapToTypeWithNewObjectId(
                          command,
                          ConfigurationType.STREAMING_PIPELINE,
                          deletePipelineProperties.request().pipelineId()),
                      COMMAND_TYPE,
                      DataPlatformObjectStatus.ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING))
          .thenCompose(
              result -> sendDataOrderDeletionCommandIfRequested(command, deletePipelineProperties))
          .exceptionally(
              e -> {
                reportFailedStatus(command, e);
                return null;
              });
    } catch (KubernetesClientException e) {
      return reportFailedStatus(command, e);
    }
  }

  private CompletionStage<Void> reportFailedStatus(Command command, Throwable e) {
    return statusUpdateService.failed(
        command, COMMAND_TYPE, ProvisioningStep.PIPELINE_CHANNEL_PROVISIONING, e);
  }

  private CompletionStage<Void> cancelFlinkJobs(StreamingPipelineRequest pipelineRequest) {
    return CompletableFuture.runAsync(
        () ->
            pipelineRequest
                .dataOrders()
                .forEach(
                    dataOrderId -> {
                      final var optionalFlinkRestApiService =
                          flinkRestApi.service(
                              flinkEnvVariableConfig.namespace,
                              pipelineRequest.category().getDeploymentType(),
                              dataOrderId.toString());

                      optionalFlinkRestApiService.ifPresentOrElse(
                          flinkRestApiService ->
                              flinkRestApiService
                                  .jobListAll()
                                  .jobs()
                                  .forEach(
                                      job -> {
                                        if (job == null) {
                                          Log.warnf(
                                              "Invalid job encountered while canceling Flink job for dataOrderId: %s",
                                              dataOrderId);
                                          return;
                                        }
                                        flinkRestApiService.jobCancelById(job.id());
                                      }),
                          () ->
                              Log.warnf(
                                  "No Flink service available for dataOrderId: %s", dataOrderId));
                    }));
  }

  private @NotNull CompletionStage<List<List<StatusDetails>>> deleteFlinkDeployments(
      StreamingPipelineRequest deleteRequest) {
    var futures =
        deleteRequest.dataOrders().stream()
            .map(
                dataOrderId ->
                    deleteFlinkDeployment(deleteRequest, dataOrderId.toString())
                        .toCompletableFuture())
            .toList();

    return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
        .thenApply(v -> futures.stream().map(CompletableFuture::join).toList());
  }

  private CompletionStage<List<StatusDetails>> deleteFlinkDeployment(
      StreamingPipelineRequest deleteRequest, String dataOrderId) {
    return CompletableFuture.supplyAsync(
        () ->
            flinkDeploymentService.delete(
                flinkEnvVariableConfig.namespace,
                deleteRequest.category().getDeploymentType(),
                dataOrderId));
  }

  private CompletionStage<Void> sendDataOrderDeletionCommandIfRequested(
      Command command, DeleteStreamingPipelineProperties deleteRequest) {
    if (!deleteRequest.deleteDataOrder()) {
      return CompletableFuture.completedFuture(null);
    }

    return commandProducer.sendCommand(
        new Command(
            command.commandId(),
            command.commandType(),
            deleteRequest.request().dataOrders().getFirst(),
            ConfigurationType.DATA_ORDER,
            JsonObject.mapFrom(
                new ModifyDataOrderCommand(
                    deleteRequest.originalDataOrderProperties().request(),
                    deleteRequest.originalDataOrderProperties().dao(),
                    null))));
  }
}
