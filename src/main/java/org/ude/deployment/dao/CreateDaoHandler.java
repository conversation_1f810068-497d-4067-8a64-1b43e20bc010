package org.ude.deployment.dao;

import static org.ude.deployment.common.status.StatusUtils.toDataPlatformObjectProvisionException;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionStage;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.infrastructure.confluent.ConfluentIdentityProvidersService;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityPool;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityProvider;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectValidationException;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.config.AppConfig;
import org.ude.deployment.dao.dto.DataAccessObject;

@ApplicationScoped
public class CreateDaoHandler implements CommandHandler {
  public static final CommandType COMMAND_TYPE = CommandType.CREATE;

  private final StatusUpdateService statusUpdateService;
  private final ConfluentIdentityProvidersService confluentIdentityProvidersService;
  private final ObjectMapper objectMapper;
  private final AppConfig appConfig;

  @Inject
  public CreateDaoHandler(
      StatusUpdateService statusUpdateService,
      ConfluentIdentityProvidersService confluentIdentityProvidersService,
      ObjectMapper objectMapper,
      AppConfig appConfig) {
    this.statusUpdateService = statusUpdateService;
    this.confluentIdentityProvidersService = confluentIdentityProvidersService;
    this.objectMapper = objectMapper;
    this.appConfig = appConfig;
  }

  @Override
  public CompletionStage<Void> handle(Command command) {
    var defaultProvisioningStep = ProvisioningStep.SECURITY_GROUP_PROVISIONING;
    if (!appConfig.useConfluentIdentityPool()) {
      return statusUpdateService.completed(command, COMMAND_TYPE, defaultProvisioningStep, null);
    }
    if (command.properties() == null) {
      return statusUpdateService.failed(
          command,
          COMMAND_TYPE,
          defaultProvisioningStep,
          new DataPlatformObjectValidationException(
              List.of("No 'properties' provided in the command for data access object creation."),
              List.of(defaultProvisioningStep)));
    }

    return Uni.createFrom()
        .completionStage(
            statusUpdateService.provisioning(command, COMMAND_TYPE, defaultProvisioningStep))
        .chain(confluentIdentityProvidersService::getIdentityProvider)
        .onItem()
        .transformToUni(provider -> handleDao(provider, command))
        .onItem()
        .transformToUni(
            result ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.completed(
                            command, COMMAND_TYPE, defaultProvisioningStep, result)))
        .onFailure()
        .transform(e -> toDataPlatformObjectProvisionException(e, defaultProvisioningStep))
        .onFailure()
        .recoverWithUni(
            throwable ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.failed(
                            command, COMMAND_TYPE, defaultProvisioningStep, throwable)))
        .subscribeAsCompletionStage();
  }

  protected Uni<JsonObject> handleDao(ConfluentIdentityProvider provider, Command command) {
    final var dao = objectMapper.convertValue(command.properties(), DataAccessObject.class);
    final var daoId = command.objectId().toString();

    var poolsToCreate =
        List.of(
            confluentIdentityProvidersService.formatDataConsumerPool(dao),
            confluentIdentityProvidersService.formatDataProducerPool(dao),
            confluentIdentityProvidersService.formatDataOrderReaderPool(dao),
            confluentIdentityProvidersService.formatDataOrderManagerPool(dao));

    return confluentIdentityProvidersService
        .findDaoIdentityPools(daoId)
        .onItem()
        .transformToUni(
            poolsInfo ->
                Multi.createFrom()
                    .iterable(poolsToCreate)
                    .onItem()
                    .transformToUniAndMerge(
                        pool -> {
                          var existing =
                              poolsInfo.stream()
                                  .filter(p -> p.getDisplayName().equals(pool.getDisplayName()))
                                  .findFirst();

                          return existing
                              .map(
                                  p -> {
                                    if (p.getFilter().equals(pool.getFilter())) {
                                      return Uni.createFrom().item(p);
                                    } else {
                                      return confluentIdentityProvidersService.updateIdentityPool(
                                          p.getId(), pool);
                                    }
                                  })
                              .orElseGet(
                                  () ->
                                      confluentIdentityProvidersService
                                          .createIdentityPool(pool)
                                          .onItem()
                                          .transformToUni(
                                              createdPool -> Uni.createFrom().item(createdPool)));
                        })
                    .collect()
                    .asList()
                    .onItem()
                    .transformToUni(
                        list -> {
                          // sort the list by display name to get
                          // consistent results always
                          var pools =
                              list.stream()
                                  .sorted(
                                      Comparator.comparing(ConfluentIdentityPool::getDisplayName))
                                  .toList();

                          return Uni.createFrom().item(JsonObject.mapFrom(Map.of("pools", pools)));
                        }));
  }
}
