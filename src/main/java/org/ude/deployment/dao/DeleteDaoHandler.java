package org.ude.deployment.dao;

import static org.ude.deployment.common.status.StatusUtils.toDataPlatformObjectProvisionException;

import io.quarkus.logging.Log;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletionStage;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.infrastructure.confluent.ConfluentIdentityProvidersService;
import org.ude.deployment.common.infrastructure.confluent.dto.ConfluentIdentityProvider;
import org.ude.deployment.common.infrastructure.kafka.TopicService;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.config.AppConfig;

@ApplicationScoped
public class DeleteDaoHandler implements CommandHandler {
  public static final CommandType COMMAND_TYPE = CommandType.DELETE;

  private final StatusUpdateService statusUpdateService;
  private final ConfluentIdentityProvidersService confluentIdentityProvidersService;
  private final TopicService topicService;
  private final AppConfig appConfig;

  @Inject
  public DeleteDaoHandler(
      StatusUpdateService statusUpdateService,
      ConfluentIdentityProvidersService confluentIdentityProvidersService,
      TopicService topicService,
      AppConfig appConfig) {
    this.statusUpdateService = statusUpdateService;
    this.confluentIdentityProvidersService = confluentIdentityProvidersService;
    this.topicService = topicService;
    this.appConfig = appConfig;
  }

  @Override
  public CompletionStage<Void> handle(Command command) {
    var defaultProvisioningStep = ProvisioningStep.SECURITY_GROUP_PROVISIONING;
    if (!appConfig.useConfluentIdentityPool()) {
      return statusUpdateService.deleted(command, COMMAND_TYPE, defaultProvisioningStep, null);
    }
    return Uni.createFrom()
        .completionStage(
            statusUpdateService.deleting(command, COMMAND_TYPE, defaultProvisioningStep))
        .chain(confluentIdentityProvidersService::getIdentityProvider)
        .onItem()
        .transformToUni(provider -> handleDaoDelete(provider, command))
        .onItem()
        .transformToUni(
            result ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.deleted(
                            command, COMMAND_TYPE, defaultProvisioningStep, result)))
        .onFailure()
        .transform(e -> toDataPlatformObjectProvisionException(e, defaultProvisioningStep))
        .onFailure()
        .recoverWithUni(
            throwable ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.failed(
                            command, COMMAND_TYPE, defaultProvisioningStep, throwable)))
        .subscribeAsCompletionStage();
  }

  protected Uni<JsonObject> handleDaoDelete(ConfluentIdentityProvider provider, Command command) {
    final var daoId = command.objectId().toString();

    var poolsToRemove =
        List.of(
            confluentIdentityProvidersService.formatIdentityPoolName(
                daoId, DaoGroupRole.DATA_CONSUMER),
            confluentIdentityProvidersService.formatIdentityPoolName(
                daoId, DaoGroupRole.DATA_PRODUCER),
            confluentIdentityProvidersService.formatIdentityPoolName(
                daoId, DaoGroupRole.DATA_ORDER_READER),
            confluentIdentityProvidersService.formatIdentityPoolName(
                daoId, DaoGroupRole.DATA_ORDER_MANAGER));

    return confluentIdentityProvidersService
        .findDaoIdentityPools(daoId)
        .onItem()
        .transformToUni(
            poolsInfo ->
                Multi.createFrom()
                    .iterable(poolsToRemove)
                    .onItem()
                    .transformToUniAndMerge(
                        poolName -> {
                          var existing =
                              poolsInfo.stream()
                                  .filter(p -> p.getDisplayName().equals(poolName))
                                  .findFirst();

                          return existing
                              .map(
                                  p ->
                                      topicService
                                          .deleteIdentityPoolAcls(p.getId())
                                          .call(
                                              r ->
                                                  confluentIdentityProvidersService
                                                      .deleteIdentityPool(p.getId()))
                                          .onItem()
                                          .transform(r -> poolName))
                              .orElseGet(
                                  () -> {
                                    Log.infof("Pool %s not found, skipping delete", poolName);
                                    return Uni.createFrom().item("");
                                  });
                        })
                    .collect()
                    .asList()
                    .onItem()
                    .transformToUni(
                        list -> {
                          // sort the list by display name to get consistent results always
                          var pools = list.stream().filter(Objects::nonNull).sorted().toList();
                          return Uni.createFrom()
                              .item(
                                  JsonObject.mapFrom(
                                      Map.of("removedPools", pools, "provider", provider)));
                        }));
  }
}
