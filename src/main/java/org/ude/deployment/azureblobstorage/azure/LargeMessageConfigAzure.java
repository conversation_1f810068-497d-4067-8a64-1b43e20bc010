package org.ude.deployment.azureblobstorage.azure;

import io.smallrye.config.ConfigMapping;
import io.smallrye.config.WithName;
import java.util.Optional;
import java.util.Set;
import org.ude.deployment.dao.DaoGroupRole;

@ConfigMapping(prefix = "large-message.azure")
public interface LargeMessageConfigAzure {

  BlobServiceClientConfig in();

  BlobServiceClientConfig out();

  Credential credential();

  interface BlobServiceClientConfig {
    @WithName("container-name-template")
    String containerNameTemplate();

    String endpoint();

    @WithName("read-roles")
    Optional<Set<DaoGroupRole>> readRoles();

    @WithName("write-roles")
    Optional<Set<DaoGroupRole>> writeRoles();
  }

  interface Credential {
    @WithName("sas-token")
    Optional<String> sasToken();

    @WithName("connection-string")
    Optional<String> connectionString();

    @WithName("tenant-id")
    Optional<String> tenantId();

    @WithName("client-id")
    Optional<String> clientId();

    @WithName("client-secret")
    Optional<String> clientSecret();
  }
}
