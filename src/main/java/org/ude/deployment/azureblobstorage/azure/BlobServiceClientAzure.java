package org.ude.deployment.azureblobstorage.azure;

import com.azure.identity.ClientSecretCredentialBuilder;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import com.azure.storage.file.datalake.DataLakeServiceClient;
import com.azure.storage.file.datalake.DataLakeServiceClientBuilder;
import io.quarkus.logging.Log;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.ude.deployment.azureblobstorage.azure.qualifier.BlobServiceClientInQualifier;
import org.ude.deployment.azureblobstorage.azure.qualifier.BlobServiceClientOutQualifier;

@AllArgsConstructor
public final class BlobServiceClientAzure {
  @NonNull private final LargeMessageConfigAzure config;

  @Produces
  @ApplicationScoped
  @BlobServiceClientInQualifier
  public BlobServiceClientConnector instanceIn() {
    return this.instance(this.config.in());
  }

  @Produces
  @ApplicationScoped
  @BlobServiceClientOutQualifier
  public BlobServiceClientConnector instanceOut() {
    return this.instance(this.config.out());
  }

  private BlobServiceClientConnector instance(
      final LargeMessageConfigAzure.BlobServiceClientConfig endpointConfig) {
    final var blobClientBuilder =
        new BlobServiceClientBuilder().endpoint(endpointConfig.endpoint());
    final var dataLakeClienbtBuilder =
        new DataLakeServiceClientBuilder().endpoint(endpointConfig.endpoint());
    final var credential = this.config.credential();

    BlobServiceClient blobServiceClient;
    DataLakeServiceClient dataLakeServiceClient = null;

    if (credential.connectionString().isPresent()) {
      Log.info("Blob service client provider via: connectionString");
      blobServiceClient =
          blobClientBuilder.connectionString(credential.connectionString().get()).buildClient();
      dataLakeServiceClient =
          dataLakeClienbtBuilder
              .connectionString(credential.connectionString().get())
              .buildClient();

    } else if (credential.sasToken().isPresent()) {
      Log.info("Blob service client provider via: sasToken");
      blobServiceClient = blobClientBuilder.sasToken(credential.sasToken().get()).buildClient();
      dataLakeServiceClient =
          dataLakeClienbtBuilder.sasToken(credential.sasToken().get()).buildClient();
    } else {
      Log.info("Blob service client provider via: credential");
      final var creds =
          new ClientSecretCredentialBuilder()
              .tenantId(credential.tenantId().orElseThrow())
              .clientId(credential.clientId().orElseThrow())
              .clientSecret(credential.clientSecret().orElseThrow())
              .build();

      blobServiceClient = blobClientBuilder.credential(creds).buildClient();
      dataLakeServiceClient = dataLakeClienbtBuilder.credential(creds).buildClient();
    }

    return new BlobServiceClientConnector(endpointConfig, blobServiceClient, dataLakeServiceClient);
  }
}
