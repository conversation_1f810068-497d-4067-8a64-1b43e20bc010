package org.ude.deployment.azureblobstorage.azure;

import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.file.datalake.DataLakeServiceClient;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.ude.deployment.azureblobstorage.azure.LargeMessageConfigAzure.BlobServiceClientConfig;

@AllArgsConstructor
@Getter
public class BlobServiceClientConnector {
  protected BlobServiceClientConfig config;
  protected BlobServiceClient blobClient;
  protected DataLakeServiceClient dataLakeClient;
}
