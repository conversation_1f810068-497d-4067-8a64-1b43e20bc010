package org.ude.deployment.azureblobstorage.azure.util;

import jakarta.enterprise.context.ApplicationScoped;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.commons.text.StringSubstitutor;
import org.ude.deployment.azureblobstorage.azure.LargeMessageConfigAzure;

@RequiredArgsConstructor
@ApplicationScoped
public class AzureStorageAccountUtils {

  public String containerName(
      final LargeMessageConfigAzure.BlobServiceClientConfig config,
      final String dataOrderId,
      final Integer retentionDays) {
    return new StringSubstitutor(
            Map.of("retentionDays", retentionDays, "dataOrderId", dataOrderId), "{", "}")
        .replace(config.containerNameTemplate());
  }
}
