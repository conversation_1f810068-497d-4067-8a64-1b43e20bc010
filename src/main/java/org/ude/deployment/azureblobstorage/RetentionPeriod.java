package org.ude.deployment.azureblobstorage;

import lombok.NonNull;

public record RetentionPeriod(@NonNull Integer duration, @NonNull RetentionPeriodUnit unit) {
  public enum RetentionPeriodUnit {
    Days
  }

  public static RetentionPeriod ofDays(final int days) {
    return new RetentionPeriod(days, RetentionPeriodUnit.Days);
  }

  public int toDays() {
    if (this.unit == RetentionPeriodUnit.Days) {
      return this.duration;
    }
    throw new UnsupportedOperationException("Only 'Days' is supported as unit");
  }
}
