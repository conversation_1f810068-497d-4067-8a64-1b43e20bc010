package org.ude.deployment.azureblobstorage.handler;

import static org.ude.deployment.common.status.StatusUtils.toDataPlatformObjectProvisionException;

import com.azure.storage.file.datalake.models.AccessControlType;
import com.azure.storage.file.datalake.models.PathAccessControlEntry;
import com.azure.storage.file.datalake.models.RolePermissions;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletionStage;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.ude.deployment.azureblobstorage.azure.BlobServiceClientConnector;
import org.ude.deployment.azureblobstorage.azure.qualifier.BlobServiceClientInQualifier;
import org.ude.deployment.azureblobstorage.azure.qualifier.BlobServiceClientOutQualifier;
import org.ude.deployment.azureblobstorage.azure.util.AzureStorageAccountUtils;
import org.ude.deployment.azureblobstorage.command.CreateAzureBlobStorageCommand;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.dao.DaoGroupRole;
import org.ude.deployment.dao.dto.DataAccessObjectEntraGroupIds;

@ApplicationScoped
@RequiredArgsConstructor
public class CreateAzureBlobStorageHandler implements CommandHandler {
  private static final CommandType COMMAND_TYPE = CommandType.CREATE;

  @NonNull private final StatusUpdateService statusUpdateService;

  @NonNull private final ObjectMapper objectMapper;

  @NonNull private final AzureStorageAccountUtils storageAccountUtils;

  @Inject @BlobServiceClientInQualifier BlobServiceClientConnector blobServiceConnectorIn;

  @Inject @BlobServiceClientOutQualifier BlobServiceClientConnector blobServiceConnectorOut;

  @Override
  public CompletionStage<Void> handle(final Command rawCommand) {
    final var command =
        rawCommand.convertTo(this.objectMapper, CreateAzureBlobStorageCommand.class);

    Log.info("Creating Azure Blob Storage for '%s'".formatted(command.dataOrderId()));

    return Uni.join()
        .all(
            Multi.createFrom()
                .items(this.blobServiceConnectorIn, this.blobServiceConnectorOut)
                .map(blobClient -> this.blobContainerCreate(blobClient, command))
                .collect()
                .asMap(Tuple2::getItem1, Tuple2::getItem2))
        .andFailFast()
        .onFailure()
        .retry()
        .atMost(3)
        .chain(maps -> Multi.createFrom().iterable(maps).toUni())
        .chain(
            lists ->
                Uni.createFrom()
                    .completionStage(
                        this.statusUpdateService.completed(
                            rawCommand,
                            COMMAND_TYPE,
                            ProvisioningStep.KAFKA_TOPIC_PROVISIONING,
                            lists)))
        .onFailure()
        .recoverWithUni(
            throwable ->
                Uni.createFrom()
                    .completionStage(
                        this.statusUpdateService.failed(
                            rawCommand,
                            COMMAND_TYPE,
                            ProvisioningStep.KAFKA_TOPIC_PROVISIONING,
                            throwable))
                    .chain(
                        () ->
                            Uni.createFrom()
                                .failure(
                                    toDataPlatformObjectProvisionException(
                                        throwable, ProvisioningStep.KAFKA_TOPIC_PROVISIONING))))
        .convert()
        .toCompletionStage();
  }

  private Tuple2<String, String> blobContainerCreate(
      final BlobServiceClientConnector connector, final CreateAzureBlobStorageCommand cmd) {

    final String containerName =
        this.storageAccountUtils.containerName(
            connector.getConfig(), cmd.dataOrderId().toString(), cmd.retentionPeriod().toDays());

    final var container = connector.getBlobClient().createBlobContainerIfNotExists(containerName);

    final var result =
        Tuple2.of(connector.getConfig().endpoint(), container.getBlobContainerName());
    Log.info("Created '%s' container on '%s'".formatted(result.getItem2(), result.getItem1()));

    var processAclSetup =
        cmd.entraGroupIds().isPresent()
            && (connector.getConfig().readRoles().isPresent()
                || connector.getConfig().writeRoles().isPresent());

    if (!processAclSetup) {
      return result;
    }

    // Setup ACLs if Entra Group IDs are provided
    try {
      setupAcls(connector, containerName, cmd.entraGroupIds().get());
      Log.info("ACLs configured for container '%s'".formatted(containerName));
    } catch (Exception e) {
      Log.error(
          "Failed to configure ACLs for container '%s': %s"
              .formatted(containerName, e.getMessage()),
          e);

      // Cleanup: delete the container if ACL setup fails
      connector.getBlobClient().deleteBlobContainer(containerName);
      throw new RuntimeException(
          "Failed to configure ACLs for container '%s'".formatted(containerName), e);
    }

    return result;
  }

  /** Set up ACLs on the container using Data Lake Storage API. */
  protected void setupAcls(
      BlobServiceClientConnector connector,
      String containerName,
      DataAccessObjectEntraGroupIds entraGroups) {

    var dataLakeServiceClient = connector.getDataLakeClient();
    var clientConfig = connector.getConfig();

    var fsClient = dataLakeServiceClient.getFileSystemClient(containerName);
    var aclEntries = new ArrayList<PathAccessControlEntry>();

    // Add mandatory entries
    addMandatoryAclEntries(aclEntries);

    if (clientConfig.readRoles().isPresent()) {
      for (var role : clientConfig.readRoles().get()) {

        if (clientConfig.writeRoles().isEmpty()
            || !clientConfig.writeRoles().get().contains(role)) {
          // If the role is not in write roles, add it as a read-only entry
          addRoleAclEntry(aclEntries, role, entraGroups, true, false);
        }
      }
    }

    if (clientConfig.writeRoles().isPresent()) {
      for (var role : clientConfig.writeRoles().get()) {
        addRoleAclEntry(aclEntries, role, entraGroups, true, true);
      }
    }

    Log.info(
        "Apply ACL to container '%s', ACL: %s"
            .formatted(
                containerName,
                aclEntries.stream()
                    .map(PathAccessControlEntry::toString)
                    .reduce((a, b) -> a + ", " + b)
                    .orElse("")));

    // Set the ACLs on the file system's root directory
    // null for group and owner means don't change those values
    fsClient.getDirectoryClient("/").setAccessControlList(aclEntries, null, null);

    Log.info("ACLs applied to container '%s'".formatted(containerName));
  }

  /** Add mandatory ACL entries (user, group, other). */
  private void addMandatoryAclEntries(List<PathAccessControlEntry> aclEntries) {
    // Owner entry with all permissions
    var ownerEntry =
        new PathAccessControlEntry()
            .setAccessControlType(AccessControlType.USER)
            .setDefaultScope(false)
            .setPermissions(
                new RolePermissions()
                    .setReadPermission(true)
                    .setWritePermission(true)
                    .setExecutePermission(true));
    aclEntries.add(ownerEntry);

    // Owner group with read and execute permissions
    var ownerGroupEntry =
        new PathAccessControlEntry()
            .setAccessControlType(AccessControlType.GROUP)
            .setDefaultScope(false)
            .setPermissions(
                new RolePermissions()
                    .setReadPermission(true)
                    .setWritePermission(false)
                    .setExecutePermission(true));
    aclEntries.add(ownerGroupEntry);

    // Others with no permissions
    var otherEntry =
        new PathAccessControlEntry()
            .setAccessControlType(AccessControlType.OTHER)
            .setDefaultScope(false)
            .setPermissions(
                new RolePermissions()
                    .setReadPermission(false)
                    .setWritePermission(false)
                    .setExecutePermission(false));
    aclEntries.add(otherEntry);
  }

  /** Add ACL entry for a specific role. */
  private void addRoleAclEntry(
      List<PathAccessControlEntry> aclEntries,
      DaoGroupRole role,
      org.ude.deployment.dao.dto.DataAccessObjectEntraGroupIds entraGroups,
      boolean read,
      boolean write) {

    var groupId = getEntraGroupId(role, entraGroups);

    var entry =
        new PathAccessControlEntry()
            .setAccessControlType(AccessControlType.GROUP)
            .setEntityId(groupId)
            .setDefaultScope(false)
            .setPermissions(
                new RolePermissions()
                    .setReadPermission(read)
                    .setWritePermission(write)
                    .setExecutePermission(true));
    aclEntries.add(entry);
  }

  /** Get the Entra Group ID for the specified role. */
  private String getEntraGroupId(
      DaoGroupRole role, org.ude.deployment.dao.dto.DataAccessObjectEntraGroupIds entraGroups) {

    return switch (role) {
      case DATA_CONSUMER -> entraGroups.dataConsumer().toString();
      case DATA_PRODUCER -> entraGroups.dataProducer().toString();
      case DATA_ORDER_MANAGER -> entraGroups.dataOrderManager().toString();
      case DATA_ORDER_READER -> entraGroups.dataOrderReader().toString();
    };
  }
}
