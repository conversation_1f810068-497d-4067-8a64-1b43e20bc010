package org.ude.deployment.azureblobstorage.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.concurrent.CompletionStage;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.ude.deployment.azureblobstorage.azure.BlobServiceClientConnector;
import org.ude.deployment.azureblobstorage.azure.LargeMessageConfigAzure;
import org.ude.deployment.azureblobstorage.azure.qualifier.BlobServiceClientInQualifier;
import org.ude.deployment.azureblobstorage.azure.qualifier.BlobServiceClientOutQualifier;
import org.ude.deployment.azureblobstorage.azure.util.AzureStorageAccountUtils;
import org.ude.deployment.azureblobstorage.command.DeleteAzureBlobStorageCommand;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.service.StatusUpdateService;

@ApplicationScoped
@RequiredArgsConstructor
public class DeleteAzureBlobStorageHandler implements CommandHandler {
  @Inject @BlobServiceClientInQualifier BlobServiceClientConnector blobServiceConnectorIn;

  @Inject @BlobServiceClientOutQualifier BlobServiceClientConnector blobServiceConnectorOut;

  @NonNull private final AzureStorageAccountUtils storageAccountUtils;

  @NonNull private final StatusUpdateService statusUpdateService;

  @NonNull private final ObjectMapper objectMapper;

  @NonNull private final LargeMessageConfigAzure config;
  public static final CommandType COMMAND_TYPE = CommandType.DELETE;

  @Override
  public CompletionStage<Void> handle(Command rawCommand) {
    final var command =
        rawCommand.convertTo(this.objectMapper, DeleteAzureBlobStorageCommand.class);
    var defaultProvisioningStep = DataPlatformObjectStatus.ProvisioningStep.STORAGE_DELETION;
    Log.info("Deleting Azure Blob Storage for '%s'".formatted(command.dataOrderId()));
    return Uni.join()
        .all(
            Multi.createFrom()
                .items(this.blobServiceConnectorIn, this.blobServiceConnectorOut)
                .map(blobClient -> this.blobContainerDelete(blobClient, command))
                .collect()
                .asMap(Tuple2::getItem1, Tuple2::getItem2))
        .andFailFast()
        .onFailure()
        .retry()
        .atMost(3)
        .chain(maps -> Multi.createFrom().iterable(maps).toUni())
        .chain(
            lists ->
                Uni.createFrom()
                    .completionStage(
                        this.statusUpdateService.deleted(
                            rawCommand, COMMAND_TYPE, defaultProvisioningStep, lists)))
        .onFailure()
        .recoverWithUni(
            throwable ->
                Uni.createFrom()
                    .completionStage(
                        this.statusUpdateService.failed(
                            rawCommand, COMMAND_TYPE, defaultProvisioningStep, throwable)))
        .convert()
        .toCompletionStage();
  }

  private Tuple2<String, String> blobContainerDelete(
      final BlobServiceClientConnector connector, final DeleteAzureBlobStorageCommand cmd) {
    final var containerName =
        this.storageAccountUtils.containerName(
            connector.getConfig(), cmd.dataOrderId().toString(), cmd.retentionPeriod().toDays());
    connector.getBlobClient().deleteBlobContainerIfExists(containerName);
    final var result = Tuple2.of(connector.getConfig().endpoint(), containerName);
    Log.info(
        "Deleted '%s' container and all of it's contents on '%s'"
            .formatted(result.getItem2(), result.getItem1()));
    return result;
  }
}
