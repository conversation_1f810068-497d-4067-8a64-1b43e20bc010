package org.ude.deployment.azureblobstorage.command;

import java.util.Optional;
import java.util.UUID;
import lombok.Builder;
import lombok.NonNull;
import org.ude.deployment.dao.dto.DataAccessObjectEntraGroupIds;
import org.ude.deployment.flink.dto.RetentionPeriod;

@Builder
public record CreateAzureBlobStorageCommand(
    @NonNull UUID dataOrderId,
    @NonNull RetentionPeriod retentionPeriod,
    @NonNull Optional<DataAccessObjectEntraGroupIds> entraGroupIds) {}
