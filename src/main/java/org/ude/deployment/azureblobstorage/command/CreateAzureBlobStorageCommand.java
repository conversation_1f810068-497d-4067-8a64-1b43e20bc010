package org.ude.deployment.azureblobstorage.command;

import java.util.Optional;
import java.util.UUID;
import lombok.Builder;
import lombok.NonNull;
import org.ude.deployment.azureblobstorage.RetentionPeriod;
import org.ude.deployment.dao.dto.DataAccessObjectEntraGroupIds;

@Builder
public record CreateAzureBlobStorageCommand(
    @NonNull UUID dataOrderId,
    @NonNull RetentionPeriod retentionPeriod,
    @NonNull Optional<DataAccessObjectEntraGroupIds> entraGroupIds) {}
