package org.ude.deployment.schema;

import static org.ude.deployment.common.status.StatusUtils.toDataPlatformObjectProvisionException;

import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.unchecked.Unchecked;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.List;
import java.util.concurrent.CompletionStage;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectValidationException;
import org.ude.deployment.common.infrastructure.kafka.KafkaSchemaRegistryClient;
import org.ude.deployment.common.status.DataPlatformObjectStatus;
import org.ude.deployment.common.status.service.StatusUpdateService;

@ApplicationScoped
public class CreateSchemaHandler implements CommandHandler {
  public static final CommandType COMMAND_TYPE = CommandType.CREATE;
  public static final String RECEPTOR_SCHEMA_IS_MISSING_IN_THE_COMMAND_PROPERTIES =
      "'receptorSchema' is missing in the command properties";

  private final StatusUpdateService statusUpdateService;
  private final KafkaSchemaRegistryClient kafkaSchemaRegistryClient;

  @Inject
  public CreateSchemaHandler(
      StatusUpdateService statusUpdateService,
      KafkaSchemaRegistryClient kafkaSchemaRegistryClient) {
    this.statusUpdateService = statusUpdateService;
    this.kafkaSchemaRegistryClient = kafkaSchemaRegistryClient;
  }

  @Override
  public CompletionStage<Void> handle(Command command) {
    var defaultProvisioningStep =
        DataPlatformObjectStatus.ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING;
    try {
      var schema = command.properties().getJsonObject("receptorSchema");
      return Uni.createFrom()
          .completionStage(
              () ->
                  statusUpdateService.initializeWithCompletedObjectRegistration(
                      command, COMMAND_TYPE, List.of(defaultProvisioningStep)))
          .chain(() -> Uni.createFrom().item(() -> hasReceptorSchema(command)))
          .chain(
              hasReceptorSchema ->
                  Boolean.FALSE.equals(hasReceptorSchema)
                      ? Uni.createFrom()
                          .failure(
                              new DataPlatformObjectValidationException(
                                  List.of(RECEPTOR_SCHEMA_IS_MISSING_IN_THE_COMMAND_PROPERTIES),
                                  List.of(defaultProvisioningStep)))
                      : Uni.createFrom().item(() -> null))
          .chain(
              () ->
                  Uni.createFrom()
                      .completionStage(
                          () ->
                              statusUpdateService.provisioning(
                                  command, COMMAND_TYPE, defaultProvisioningStep)))
          .chain(
              result ->
                  Uni.createFrom()
                      .item(
                          Unchecked.supplier(
                              () ->
                                  kafkaSchemaRegistryClient.registerSchema(
                                      command.objectId().toString(), schema))))
          .onFailure()
          .transform(e -> toDataPlatformObjectProvisionException(e, defaultProvisioningStep))
          .chain(
              metaData ->
                  Uni.createFrom()
                      .completionStage(
                          statusUpdateService.completed(
                              command, COMMAND_TYPE, defaultProvisioningStep, metaData)))
          .onFailure()
          .recoverWithUni(
              throwable ->
                  Uni.createFrom()
                      .completionStage(
                          statusUpdateService.failed(
                              command, COMMAND_TYPE, defaultProvisioningStep, throwable)))
          .subscribeAsCompletionStage();

    } catch (Exception e) {
      return statusUpdateService.failed(command, COMMAND_TYPE, defaultProvisioningStep, e);
    }
  }

  private boolean hasReceptorSchema(Command command) {
    return command.properties() != null
        && command.properties().getJsonObject("receptorSchema") != null;
  }
}
