package org.ude.deployment.schema;

import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.unchecked.Unchecked;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletionStage;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.jetbrains.annotations.NotNull;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectProvisionException;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectValidationException;
import org.ude.deployment.common.infrastructure.kafka.KafkaConfig;
import org.ude.deployment.common.infrastructure.kafka.KafkaSchemaRegistryClient;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;

@ApplicationScoped
@AllArgsConstructor
public class UpdateSchemaHandler implements CommandHandler {
  private static final CommandType COMMAND_TYPE = CommandType.DISABLE;

  @NonNull private final KafkaSchemaRegistryClient schemaRegistryClient;

  @NonNull private final StatusUpdateService statusUpdateService;

  @NonNull private final KafkaConfig kafkaNamingConfig;

  @Override
  public CompletionStage<Void> handle(final Command command) {
    return Uni.createFrom()
        .item(isCommandValid(command))
        .invoke(
            isValid -> {
              if (!isValid) {
                throw new DataPlatformObjectValidationException(
                    List.of("Unsupported command provided"),
                    List.of(ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING));
              }
            })
        .chain(
            () ->
                Uni.createFrom()
                    .item(command.properties().getString("configurationStatus").equals("Disabled")))
        .chain(
            isDisabled ->
                Boolean.TRUE.equals(isDisabled)
                    ? Uni.createFrom()
                        .completionStage(
                            statusUpdateService.deleting(
                                command,
                                COMMAND_TYPE,
                                ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING))
                        .chain(() -> processDisabled(command))
                    : Uni.createFrom()
                        .failure(
                            new DataPlatformObjectValidationException(
                                List.of("Configuration status not supported"),
                                List.of(ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING))))
        .onFailure()
        .recoverWithUni(e -> this.status(command, e))
        .convert()
        .toCompletionStage();
  }

  private Uni<Void> processDisabled(final Command command) {
    final var subjectId =
        this.kafkaNamingConfig.getSchemaSubjectName(command.objectId().toString());
    final var client = this.schemaRegistryClient.client();
    return this.schemaSoftDeleteAllVersions(client, subjectId)
        .chain(
            versions -> {
              Log.info(
                  "All the versions for '%s' schema have been soft-deleted".formatted(subjectId));
              return this.status(command, versions);
            });
  }

  private Uni<List<Integer>> schemaSoftDeleteAllVersions(
      final SchemaRegistryClient client, final String subjectId) {
    return Multi.createFrom()
        .items(Unchecked.supplier(() -> client.getAllVersions(subjectId).stream()))
        .onFailure()
        .transform(UpdateSchemaHandler::toDataPlatformObjectProvisionException)
        .map(
            v ->
                Uni.createFrom()
                    .item(
                        Unchecked.supplier(
                            () -> client.deleteSchemaVersion(subjectId, String.valueOf(v))))
                    .onFailure()
                    .transform(UpdateSchemaHandler::toDataPlatformObjectProvisionException))
        .collect()
        .asList()
        .flatMap(unis -> Uni.join().all(unis).andCollectFailures());
  }

  private static @NotNull DataPlatformObjectProvisionException
      toDataPlatformObjectProvisionException(Throwable throwable) {
    return new DataPlatformObjectProvisionException(
        List.of(ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING), throwable);
  }

  private boolean isCommandValid(final Command command) {
    return Objects.nonNull(command.properties())
        && command.properties().containsKey("configurationStatus")
        && command.properties().containsKey("schema");
  }

  private Uni<Void> status(final Command command, final List<Integer> versions) {
    return Uni.createFrom()
        .completionStage(
            this.statusUpdateService.deleted(
                command,
                COMMAND_TYPE,
                ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING,
                JsonObject.of("softDeletedVersions", versions)));
  }

  private Uni<Void> status(final Command command, final Throwable throwable) {
    var defaultProvisioningStep = ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING;
    return Uni.createFrom()
        .completionStage(
            this.statusUpdateService.failed(
                command, COMMAND_TYPE, defaultProvisioningStep, throwable));
  }
}
