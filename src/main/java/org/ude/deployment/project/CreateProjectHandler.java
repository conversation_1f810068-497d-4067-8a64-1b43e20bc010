package org.ude.deployment.project;

import io.quarkus.logging.Log;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.List;
import java.util.concurrent.CompletionStage;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.status.service.StatusUpdateService;

@ApplicationScoped
public class CreateProjectHandler implements CommandHandler {

  private final StatusUpdateService statusUpdateService;

  @Inject
  public CreateProjectHandler(StatusUpdateService statusUpdateService) {
    this.statusUpdateService = statusUpdateService;
  }

  @Override
  public CompletionStage<Void> handle(Command command) {
    Log.infof("Handling create project command: %s", command);
    return statusUpdateService.initializeWithCompletedObjectRegistration(
        command, CommandType.CREATE, List.of());
  }
}
