package org.ude.deployment.dataorder;

import static org.ude.deployment.common.status.StatusUtils.toDataPlatformObjectProvisionException;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletionStage;
import lombok.AllArgsConstructor;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectValidationException;
import org.ude.deployment.common.infrastructure.kafka.CommandProducer;
import org.ude.deployment.common.infrastructure.kafka.KafkaConfig;
import org.ude.deployment.common.infrastructure.kafka.TopicService;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateDataOrderTopicsResult;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.config.AppConfig;
import org.ude.deployment.dataorder.dto.DataOrder;
import org.ude.deployment.dataorder.dto.ModifyDataOrderCommand;
import org.ude.deployment.dataorder.dto.UpdateDataOrderConfigCommandResult;
import org.ude.deployment.flink.dto.command.UpdatePipelineCommand;

@ApplicationScoped
@AllArgsConstructor
public class UpdateDataOrderHandler implements CommandHandler {
  private static final CommandType COMMAND_TYPE = CommandType.UPDATE;

  private final KafkaConfig kafkaConfig;
  private final ObjectMapper objectMapper;
  private final StatusUpdateService statusUpdateService;
  private final TopicService topicService;
  private final AppConfig appConfig;
  private final CommandProducer commandProducer;

  @Override
  public CompletionStage<Void> handle(Command command) {
    var properties = objectMapper.convertValue(command.properties(), ModifyDataOrderCommand.class);
    var errors = validateUpdateCommandProperties(command, properties.request());
    var dataOrderId = command.objectId().toString();
    var dataOrder = properties.request();
    var configuration = dataOrder.configuration();
    var defaultTopicProvisioningStep = ProvisioningStep.KAFKA_TOPIC_CONFIGURATION_UPDATE;
    var provisioningSteps = determineProvisioningSteps(properties);

    return Uni.createFrom()
        .completionStage(
            statusUpdateService.initializeWithCompletedSteps(
                command,
                COMMAND_TYPE,
                List.of(ProvisioningStep.CONFIGURATION_OBJECT_UPDATE),
                provisioningSteps))
        .flatMap(
            v ->
                !errors.isEmpty()
                    ? Uni.createFrom()
                        .failure(
                            new DataPlatformObjectValidationException(
                                errors, List.of(defaultTopicProvisioningStep)))
                    : Uni.createFrom().item(() -> null))
        .chain(() -> validatePartitionCounts(dataOrderId, configuration.partitionCount()))
        .chain(
            () ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.provisioning(
                            command, COMMAND_TYPE, defaultTopicProvisioningStep)))
        .chain(
            () ->
                updateTopicsConfiguration(
                        command, configuration.partitionCount(), configuration.retentionPeriod())
                    .chain(
                        result -> {
                          if (appConfig.useConfluentIdentityPool()) {
                            var aclProvisioningSteps = createAclProvisioningSteps();
                            return Uni.createFrom()
                                .completionStage(
                                    statusUpdateService.provisioning(
                                        command, COMMAND_TYPE, aclProvisioningSteps))
                                .chain(
                                    () ->
                                        topicService.handleTopicAclSetup(
                                            properties.dao(), dataOrderId, result.topics()))
                                .onFailure()
                                .transform(
                                    e ->
                                        toDataPlatformObjectProvisionException(
                                            e, aclProvisioningSteps))
                                .chain(
                                    aclResult ->
                                        Uni.createFrom()
                                            .completionStage(
                                                statusUpdateService.completed(
                                                    command,
                                                    COMMAND_TYPE,
                                                    aclProvisioningSteps,
                                                    null))
                                            .replaceWith(aclResult));
                          } else {
                            return Uni.createFrom()
                                .item(new CreateDataOrderTopicsResult(result.topics(), null));
                          }
                        })
                    .emitOn(Infrastructure.getDefaultWorkerPool())
                    .chain(
                        result ->
                            Objects.isNull(properties.pipeline())
                                    ||
                                    // details in
                                    // main/java/org/ude/deployment/flink/UpdatePipelineHandler.java
                                    (Objects.isNull(dataOrder.metadataProperties())
                                        && Objects.isNull(dataOrder.additionalProperties()))
                                ? Uni.createFrom().item(result)
                                : Uni.createFrom()
                                    .completionStage(
                                        commandProducer.sendCommand(
                                            this.commandPipelineUpdate(properties)))
                                    .replaceWith(result))
                    .emitOn(Infrastructure.getDefaultExecutor())
                    .chain(
                        result ->
                            Uni.createFrom()
                                .completionStage(
                                    statusUpdateService.completed(
                                        command,
                                        COMMAND_TYPE,
                                        defaultTopicProvisioningStep,
                                        result))))
        .onFailure()
        .recoverWithUni(
            throwable ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.failed(
                            command, COMMAND_TYPE, defaultTopicProvisioningStep, throwable)))
        .subscribeAsCompletionStage();
  }

  private List<ProvisioningStep> determineProvisioningSteps(
      ModifyDataOrderCommand dataOrderCommand) {
    var provisioningSteps = new ArrayList<ProvisioningStep>();

    if (requiresKafkaTopicUpdate(dataOrderCommand)) {
      provisioningSteps.add(ProvisioningStep.KAFKA_TOPIC_CONFIGURATION_UPDATE);
    }
    if (requiresSecurityGroupProvisioning()) {
      provisioningSteps.add(ProvisioningStep.SECURITY_GROUP_PROVISIONING);
    }

    return Collections.unmodifiableList(provisioningSteps);
  }

  private boolean requiresKafkaTopicUpdate(ModifyDataOrderCommand dataOrderCommand) {
    return Optional.ofNullable(dataOrderCommand.request().configuration())
        .map(config -> config.partitionCount() != 0 || config.retentionPeriod() != 0)
        .orElse(false);
  }

  private boolean requiresSecurityGroupProvisioning() {
    return appConfig.useConfluentIdentityPool();
  }

  private List<ProvisioningStep> createAclProvisioningSteps() {
    return List.of(ProvisioningStep.SECURITY_GROUP_PROVISIONING);
  }

  private Uni<UpdateDataOrderConfigCommandResult> updateTopicsConfiguration(
      Command command, int newPartitionCount, int retentionInDays) {
    var topicsToUpdate = getTopicsToUpdate(command.objectId().toString());

    return updatePartitionCounts(topicsToUpdate, newPartitionCount)
        .chain(() -> updateRetention(topicsToUpdate, retentionInDays))
        .chain(
            () ->
                Uni.createFrom()
                    .item(
                        new UpdateDataOrderConfigCommandResult(
                            topicsToUpdate, newPartitionCount, retentionInDays)))
        .onFailure()
        .transform(
            e ->
                toDataPlatformObjectProvisionException(
                    e, ProvisioningStep.KAFKA_TOPIC_CONFIGURATION_UPDATE));
  }

  private Uni<List<Integer>> updatePartitionCounts(List<String> topics, int newPartitionCount) {
    return topicService
        .getTopicDescriptions(topics)
        .chain(
            topicDescriptions -> {
              // Check first topic's partition count since all topics should have same
              // count
              var currentPartitionCount = topicDescriptions.getFirst().partitions().size();
              if (currentPartitionCount == newPartitionCount) {
                Log.infof(
                    "Skipping partition update as current count %s matches"
                        + " requested count for topics %s",
                    currentPartitionCount, topics);
                return Uni.createFrom()
                    .item(
                        topicDescriptions.stream()
                            .map(topicDescription -> topicDescription.partitions().size())
                            .toList());
              }

              return topicService
                  .updatePartitionCountForTopics(topics, newPartitionCount)
                  .invoke(
                      () ->
                          Log.infof(
                              "Successfully updated partition count"
                                  + " from %s to %s for topics %s",
                              currentPartitionCount, newPartitionCount, topics));
            });
  }

  private Uni<Map<String, Integer>> updateRetention(List<String> topics, int retentionDays) {
    return topicService
        .updateRetentionPolicyForTopics(topics, retentionDays)
        .invoke(
            () ->
                Log.infof(
                    "Successfully updated retention policy to %s for topics %s",
                    retentionDays, topics));
  }

  List<String> getTopicsToUpdate(String dataOrderId) {
    return List.of(
        kafkaConfig.getInputTopicName(dataOrderId), kafkaConfig.getOutputTopicName(dataOrderId));
  }

  List<String> validateUpdateCommandProperties(Command command, DataOrder dataOrder) {
    List<String> errors = new ArrayList<>();
    var configuration = dataOrder.configuration();

    validateObjectId(command, errors);
    validateConfiguration(configuration, errors);

    return errors;
  }

  private void validateObjectId(Command command, List<String> errors) {
    if (command.objectId() == null) {
      errors.add("Invalid update dataorder command properties: objectId missing");
    }
  }

  private void validateConfiguration(
      DataOrder.DataOrderConfiguration configuration, List<String> errors) {
    if (configuration == null) {
      errors.add("Invalid update dataorder command properties: configuration missing");
      return;
    }

    if (configuration.partitionCount() == 0) {
      errors.add("Invalid update dataorder command properties: partitionCount invalid");
    }

    if (configuration.retentionPeriod() == 0) {
      errors.add("Invalid update dataorder command properties: retentionPeriod invalid");
    }
  }

  private Uni<Void> validatePartitionCounts(String dataOrderId, int newPartitionCount) {
    return Uni.createFrom()
        .item(() -> getTopicsToUpdate(dataOrderId))
        .chain(topicService::getTopicDescriptions)
        .map(
            topicDescriptions ->
                topicDescriptions.stream()
                    .filter(topic -> topic.partitions().size() > newPartitionCount)
                    .map(
                        topic ->
                            formatInvalidPartitionError(
                                topic.partitions().size(), newPartitionCount))
                    .toList())
        .onItem()
        .transformToUni(
            errors -> {
              if (!errors.isEmpty()) {
                return Uni.createFrom()
                    .failure(
                        new DataPlatformObjectValidationException(
                            List.of(errors.getFirst()),
                            List.of(ProvisioningStep.KAFKA_TOPIC_CONFIGURATION_UPDATE)));
              }
              return Uni.createFrom().voidItem();
            });
  }

  private String formatInvalidPartitionError(int oldPartitionCount, int newPartitionCount) {
    return String.format(
        "Invalid update dataorder command properties: partitionCount invalid. Current"
            + " partition count: %d. New partition count: %d",
        oldPartitionCount, newPartitionCount);
  }

  private Command commandPipelineUpdate(final ModifyDataOrderCommand dataOrderCommand) {
    final var cmd =
        UpdatePipelineCommand.builder(
                dataOrderCommand.pipeline().dataOrders().getFirst(),
                dataOrderCommand.pipeline(),
                dataOrderCommand.request().schemas(),
                dataOrderCommand.request().configuration())
            .additionalProperties(
                Optional.ofNullable(dataOrderCommand.request().additionalProperties()))
            .metadataProperties(
                Optional.ofNullable(dataOrderCommand.request().metadataProperties()))
            .build();
    return new Command(
        UUID.randomUUID().toString(),
        CommandType.UPDATE,
        dataOrderCommand.pipeline().pipelineId(),
        ConfigurationType.FLINK_JOB_PIPELINE,
        JsonObject.mapFrom(cmd));
  }
}
