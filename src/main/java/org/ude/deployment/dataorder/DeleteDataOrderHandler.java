package org.ude.deployment.dataorder;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletionStage;
import org.ude.deployment.azureblobstorage.command.DeleteAzureBlobStorageCommand;
import org.ude.deployment.azureblobstorage.handler.DeleteAzureBlobStorageHandler;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectValidationException;
import org.ude.deployment.common.infrastructure.kafka.CommandProducer;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.dao.dto.DataAccessObjectEntraGroupIds;
import org.ude.deployment.dataorder.dto.DeleteDataOrderTopicsRequest;
import org.ude.deployment.dataorder.dto.ModifyDataOrderCommand;
import org.ude.deployment.flink.dto.RetentionPeriod;
import org.ude.deployment.streamingpipeline.dto.DeleteStreamingPipelineProperties;

@ApplicationScoped
public class DeleteDataOrderHandler implements CommandHandler {
  private static final CommandType COMMAND_TYPE = CommandType.DELETE;
  public static final String VALIDATION_ERROR_PIPELINE_ID_REQUIRED =
      "Invalid delete dataorder command properties: pipelineId is required if pipeline is present";
  public static final String VALIDATION_ERROR_CATEGORY_REQUIRED =
      "Invalid delete dataorder command properties: category is required if pipeline is not present";

  private final CommandProducer commandProducer;
  private final ObjectMapper objectMapper;
  private final StatusUpdateService statusUpdateService;
  private final DeleteAzureBlobStorageHandler deleteAzureBlobStorageHandler;

  @Inject
  public DeleteDataOrderHandler(
      CommandProducer commandProducer,
      ObjectMapper objectMapper,
      StatusUpdateService statusUpdateService,
      DeleteAzureBlobStorageHandler deleteAzureBlobStorageHandler) {
    this.commandProducer = commandProducer;
    this.objectMapper = objectMapper;
    this.statusUpdateService = statusUpdateService;
    this.deleteAzureBlobStorageHandler = deleteAzureBlobStorageHandler;
  }

  @Override
  public CompletionStage<Void> handle(Command command) {
    var properties = objectMapper.convertValue(command.properties(), ModifyDataOrderCommand.class);
    var errors = validatePipelineProperties(properties);
    var defaultTopicDeletionProvisioningStep = ProvisioningStep.KAFKA_TOPIC_DELETION;

    return (!errors.isEmpty()
            ? Uni.createFrom()
                .failure(
                    new DataPlatformObjectValidationException(
                        errors, List.of(defaultTopicDeletionProvisioningStep)))
            : Uni.createFrom().voidItem())
        .chain(
            () ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.deleting(
                            command, COMMAND_TYPE, defaultTopicDeletionProvisioningStep)))
        .invoke(() -> executeDeleteOperation(command, properties))
        .onFailure()
        .recoverWithUni(
            throwable ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.failed(
                            command,
                            COMMAND_TYPE,
                            defaultTopicDeletionProvisioningStep,
                            throwable)))
        .subscribe()
        .asCompletionStage();
  }

  List<String> validatePipelineProperties(ModifyDataOrderCommand dataOrderCommand) {
    List<String> errors = new ArrayList<>();
    if (dataOrderCommand.pipeline() != null) {
      if (dataOrderCommand.pipeline().pipelineId() == null) {
        errors.add(VALIDATION_ERROR_PIPELINE_ID_REQUIRED);
      }
      if (dataOrderCommand.pipeline().category() == null) {
        errors.add(VALIDATION_ERROR_CATEGORY_REQUIRED);
      }
    }
    return errors;
  }

  private Command createDeleteAzureBlobStorageCommand(
      final UUID dataOrderId, final ModifyDataOrderCommand cmdOrigin) {
    return new Command(
        UUID.randomUUID().toString(),
        CommandType.DELETE,
        dataOrderId,
        ConfigurationType.AZURE_BLOB_STORAGE,
        JsonObject.mapFrom(
            DeleteAzureBlobStorageCommand.builder()
                .dataOrderId(dataOrderId)
                .retentionPeriod(
                    RetentionPeriod.ofDays(cmdOrigin.request().configuration().retentionPeriod()))
                .entraGroupIds(
                    Optional.ofNullable(cmdOrigin.dao())
                        .map(
                            dao ->
                                new DataAccessObjectEntraGroupIds(
                                    dao.dataOrderManagerEntraGroupId(),
                                    dao.dataOrderReaderEntraGroupId(),
                                    dao.dataConsumerEntraGroupId(),
                                    dao.dataProducerEntraGroupId())))
                .build()));
  }

  private CompletionStage<Command> sendDeleteTopicsCommand(
      Command command, ModifyDataOrderCommand dataOrderCommand) {
    var deleteTopicsCommand =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            command.objectId(),
            ConfigurationType.DATA_ORDER_TOPIC,
            JsonObject.mapFrom(new DeleteDataOrderTopicsRequest(command.objectId().toString())));
    return this.commandProducer
        .sendCommand(deleteTopicsCommand)
        .thenApply(unused -> deleteTopicsCommand);
  }

  private CompletionStage<Command> sendDeletePipelineCommand(
      ModifyDataOrderCommand dataOrderCommand) {
    var deletePipelineCommand =
        new Command(
            UUID.randomUUID().toString(),
            CommandType.DELETE,
            dataOrderCommand.pipeline().pipelineId(),
            ConfigurationType.STREAMING_PIPELINE,
            JsonObject.mapFrom(
                new DeleteStreamingPipelineProperties(
                    dataOrderCommand.pipeline(), true, dataOrderCommand)));
    return this.commandProducer
        .sendCommand(deletePipelineCommand)
        .thenCompose(
            unused -> Uni.createFrom().item(deletePipelineCommand).subscribeAsCompletionStage());
  }

  CompletionStage<Command> executeDeleteOperation(
      Command command, ModifyDataOrderCommand dataOrderCommand) {
    if (dataOrderCommand.pipeline() == null) {
      Log.infof(
          "Pipeline information missing or already deleted for %s, skipping dataOrderPipeline delete",
          command);
      return deleteAzureBlobStorageHandler
          .handle(createDeleteAzureBlobStorageCommand(command.objectId(), dataOrderCommand))
          .thenCompose(unused -> sendDeleteTopicsCommand(command, dataOrderCommand));
    } else {
      return this.sendDeletePipelineCommand(dataOrderCommand);
    }
  }
}
