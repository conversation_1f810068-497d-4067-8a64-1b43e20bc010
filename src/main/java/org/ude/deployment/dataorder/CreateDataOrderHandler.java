package org.ude.deployment.dataorder;

import static org.ude.deployment.common.status.StatusUtils.toDataPlatformObjectProvisionException;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import io.smallrye.mutiny.unchecked.Unchecked;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;
import org.ude.deployment.azureblobstorage.command.CreateAzureBlobStorageCommand;
import org.ude.deployment.azureblobstorage.handler.CreateAzureBlobStorageHandler;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.exception.DataPlatformObjectValidationException;
import org.ude.deployment.common.infrastructure.kafka.KafkaConfig;
import org.ude.deployment.common.infrastructure.kafka.KafkaSchemaRegistryClient;
import org.ude.deployment.common.infrastructure.kafka.TopicService;
import org.ude.deployment.common.infrastructure.kafka.dto.CreateDataOrderTopicsResult;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.config.AppConfig;
import org.ude.deployment.dao.dto.DataAccessObjectEntraGroupIds;
import org.ude.deployment.dataorder.dto.CreateDataOrderCommand;
import org.ude.deployment.dataorder.dto.CreateDataOrderCommandResult;
import org.ude.deployment.flink.dto.RetentionPeriod;

@ApplicationScoped
public class CreateDataOrderHandler implements CommandHandler {
  private static final CommandType COMMAND_TYPE = CommandType.CREATE;

  private final TopicService topicService;
  private final StatusUpdateService statusUpdateService;
  private final KafkaSchemaRegistryClient kafkaSchemaRegistryClient;
  private final ObjectMapper objectMapper;
  private final KafkaConfig kafkaConfig;
  private final AppConfig appConfig;
  private final CreateAzureBlobStorageHandler createAzureBlobStorageHandler;

  @Inject
  public CreateDataOrderHandler(
      TopicService topicService,
      StatusUpdateService statusUpdateService,
      KafkaSchemaRegistryClient kafkaSchemaRegistryClient,
      ObjectMapper objectMapper,
      KafkaConfig kafkaConfig,
      AppConfig appConfig,
      CreateAzureBlobStorageHandler createAzureBlobStorageHandler) {
    this.topicService = topicService;
    this.statusUpdateService = statusUpdateService;
    this.kafkaSchemaRegistryClient = kafkaSchemaRegistryClient;
    this.objectMapper = objectMapper;
    this.kafkaConfig = kafkaConfig;
    this.appConfig = appConfig;
    this.createAzureBlobStorageHandler = createAzureBlobStorageHandler;
  }

  @Override
  public CompletionStage<Void> handle(Command command) {
    var properties = parseDataOrderProperties(command);
    var errors = validateCommandProperties(command, properties);
    var dataOrder = properties.request();
    var initialSchemas = dataOrder.schemas();

    return Uni.createFrom()
        .completionStage(
            statusUpdateService.initializeWithCompletedObjectRegistration(
                command, COMMAND_TYPE, createProvisioningSteps()))
        .flatMap(
            v ->
                !errors.isEmpty()
                    ? Uni.createFrom()
                        .failure(
                            new DataPlatformObjectValidationException(
                                errors, createProvisioningSteps()))
                    : Uni.createFrom().item(() -> null))
        .chain(() -> validateTopicSchemas(dataOrder.schemas()))
        .onItem()
        .transformToUni(
            verifiedSchemas ->
                createTopicsIfSchemasAreValid(command, verifiedSchemas, initialSchemas))
        .emitOn(Infrastructure.getDefaultWorkerPool())
        .chain(
            result ->
                Uni.createFrom()
                    .completionStage(
                        this.createAzureBlobStorageHandler.handle(
                            this.commandCreateAzureBlobStorage(command.objectId(), properties)))
                    .replaceWith(result))
        .emitOn(Infrastructure.getDefaultExecutor())
        .flatMap(
            result ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.completed(
                            command,
                            COMMAND_TYPE,
                            createProvisioningStepsWithoutKafkaTopic(),
                            ProvisioningStep.KAFKA_TOPIC_PROVISIONING,
                            result)))
        .onFailure()
        .recoverWithUni(
            throwable -> {
              var defaultProvisioningStep = ProvisioningStep.KAFKA_TOPIC_PROVISIONING;
              return Uni.createFrom()
                  .completionStage(
                      statusUpdateService.failed(
                          command, COMMAND_TYPE, defaultProvisioningStep, throwable));
            })
        .subscribeAsCompletionStage();
  }

  private CreateDataOrderCommand parseDataOrderProperties(Command command) {
    return objectMapper.convertValue(command.properties(), CreateDataOrderCommand.class);
  }

  private List<ProvisioningStep> createProvisioningStepsWithoutKafkaTopic() {
    return createProvisioningSteps().stream()
        .filter(s -> !s.equals(ProvisioningStep.KAFKA_TOPIC_PROVISIONING))
        .toList();
  }

  private List<ProvisioningStep> createProvisioningSteps() {
    return List.of(
        ProvisioningStep.KAFKA_TOPIC_PROVISIONING,
        ProvisioningStep.SECURITY_GROUP_PROVISIONING,
        ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING);
  }

  private Uni<CreateDataOrderCommandResult> createTopicsIfSchemasAreValid(
      Command command, List<String> verifiedSchemas, List<String> initialSchemaList) {
    if (verifiedSchemas.size() == initialSchemaList.size()) {
      return createTopics(command);
    } else {
      var missingSchemas = findMissingSchemas(initialSchemaList, verifiedSchemas);
      var errorMessage = "Schema(s) %s not found".formatted(missingSchemas);
      return Uni.createFrom()
          .failure(
              new DataPlatformObjectValidationException(
                  List.of(errorMessage), createProvisioningSteps()));
    }
  }

  private Uni<CreateDataOrderCommandResult> createTopics(Command command) {
    var dataOrderId = command.objectId();
    var dataOrderProperties = parseDataOrderProperties(command);
    var dataOrder = dataOrderProperties.request();
    var schemaId = dataOrder.schemas().getFirst();
    var dao = dataOrderProperties.dao();

    return Uni.createFrom()
        .completionStage(
            statusUpdateService.provisioning(command, COMMAND_TYPE, createProvisioningSteps()))
        .chain(
            () ->
                topicService
                    .createDataOrderTopics(dataOrderId, dataOrder)
                    .onFailure()
                    .transform(
                        e ->
                            toDataPlatformObjectProvisionException(
                                e, ProvisioningStep.KAFKA_TOPIC_PROVISIONING)))
        .chain(
            topicNames -> {
              if (appConfig.useConfluentIdentityPool()) {
                return topicService
                    .handleTopicAclSetup(dao, dataOrderId.toString(), topicNames)
                    .onFailure()
                    .transform(
                        e ->
                            toDataPlatformObjectProvisionException(
                                e, ProvisioningStep.SECURITY_GROUP_PROVISIONING));
              } else {
                Log.infof("Skipping topic ACL setup");
                return Uni.createFrom().item(new CreateDataOrderTopicsResult(topicNames, null));
              }
            })
        .chain(
            topicsResult ->
                setTopicRetentionPeriod(
                        topicsResult.getTopics(), dataOrder.configuration().retentionPeriod())
                    .invoke(() -> Log.infof("Topic retention period set for %s", dataOrder.name()))
                    .replaceWith(topicsResult))
        .chain(
            topicsResult ->
                topicService
                    .associateSchemaToInputTopic(dataOrderId.toString(), schemaId)
                    .onFailure()
                    .transform(
                        e ->
                            toDataPlatformObjectProvisionException(
                                e, ProvisioningStep.SCHEMA_REGISTRY_PROVISIONING))
                    .chain(
                        meta ->
                            Uni.createFrom()
                                .item(
                                    new CreateDataOrderCommandResult(
                                        topicsResult.getTopics(),
                                        meta,
                                        topicsResult.getConnectionInfo()))))
        .onFailure()
        .recoverWithUni(
            throwable -> {
              Log.error(
                  "Failed to create topics for %s".formatted(dataOrderId.toString()), throwable);
              return Uni.createFrom().failure(throwable);
            });
  }

  private String findMissingSchemas(List<String> initialSchemas, List<String> verifiedSchemas) {
    return initialSchemas.stream()
        .filter(schema -> !verifiedSchemas.contains(schema))
        .collect(Collectors.joining(", "));
  }

  private List<String> validateCommandProperties(
      Command command, CreateDataOrderCommand properties) {
    var errors = new ArrayList<String>();
    var dataOrder = properties.request();

    if (command.objectId() == null) {
      errors.add("Invalid create topic command properties: objectId missing");
    }
    if (dataOrder == null) {
      errors.add("Invalid create topic command properties: request missing");
    } else {
      if (dataOrder.configuration() == null) {
        errors.add("Invalid create topic command properties: configuration missing");
      } else {
        var partitionCount = dataOrder.configuration().partitionCount();
        if (partitionCount <= 0 || partitionCount > 100) {
          errors.add("Invalid create topic command properties: partitionCount invalid");
        }
      }
      if (dataOrder.schemas() == null || dataOrder.schemas().isEmpty()) {
        errors.add("Invalid create topic command properties: schemaIds missing");
      }
    }
    return errors;
  }

  private Uni<List<String>> validateTopicSchemas(List<String> schemaIds) {
    return Multi.createFrom()
        .iterable(schemaIds)
        .onItem()
        .transformToUniAndMerge(
            schemaId ->
                Uni.createFrom()
                    .item(
                        Unchecked.supplier(
                            () ->
                                kafkaSchemaRegistryClient.doesSchemaExist(
                                    kafkaConfig.getSchemaSubjectName(schemaId))))
                    .onFailure()
                    .recoverWithItem(false)
                    .map(exists -> exists ? schemaId : null))
        .collect()
        .asList()
        .onItem()
        .transform(list -> list.stream().filter(Objects::nonNull).collect(Collectors.toList()));
  }

  private Uni<Void> setTopicRetentionPeriod(List<String> topics, int retentionPeriodInDays) {
    return topicService
        .updateRetentionPolicyForTopics(topics, retentionPeriodInDays)
        .invoke(() -> Log.infof("Successfully set retention period for topics %s", topics))
        .onFailure()
        .invoke(e -> Log.errorf(e, "Failed to set retention period for topics: %s", topics))
        .onFailure()
        .transform(
            e ->
                toDataPlatformObjectProvisionException(
                    e, ProvisioningStep.KAFKA_TOPIC_PROVISIONING))
        .replaceWithVoid();
  }

  private Command commandCreateAzureBlobStorage(
      final UUID dataOrderId, final CreateDataOrderCommand cmdOrigin) {
    Log.infof(
        "Creating Azure Blob Storage for Data Order %s with retention period %d days, data access object: %s",
        dataOrderId, cmdOrigin.request().configuration().retentionPeriod(), cmdOrigin.dao());

    return new Command(
        UUID.randomUUID().toString(),
        CommandType.CREATE,
        dataOrderId,
        ConfigurationType.AZURE_BLOB_STORAGE,
        JsonObject.mapFrom(
            CreateAzureBlobStorageCommand.builder()
                .dataOrderId(dataOrderId)
                .retentionPeriod(
                    RetentionPeriod.ofDays(cmdOrigin.request().configuration().retentionPeriod()))
                .entraGroupIds(
                    Optional.ofNullable(cmdOrigin.dao())
                        .map(
                            dao ->
                                new DataAccessObjectEntraGroupIds(
                                    dao.dataOrderManagerEntraGroupId(),
                                    dao.dataOrderReaderEntraGroupId(),
                                    dao.dataConsumerEntraGroupId(),
                                    dao.dataProducerEntraGroupId())))
                .build()));
  }
}
