package org.ude.deployment.dataorder;

import static org.ude.deployment.common.command.CommandUtils.mapToType;

import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.unchecked.Unchecked;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.List;
import java.util.concurrent.CompletionStage;
import org.ude.deployment.common.command.Command;
import org.ude.deployment.common.command.CommandType;
import org.ude.deployment.common.contract.CommandHandler;
import org.ude.deployment.common.domain.ConfigurationType;
import org.ude.deployment.common.infrastructure.kafka.KafkaConfig;
import org.ude.deployment.common.infrastructure.kafka.KafkaSchemaRegistryClient;
import org.ude.deployment.common.infrastructure.kafka.TopicService;
import org.ude.deployment.common.status.DataPlatformObjectStatus.ProvisioningStep;
import org.ude.deployment.common.status.service.StatusUpdateService;
import org.ude.deployment.config.AppConfig;
import org.ude.deployment.dataorder.dto.DeleteTopicCommandResult;

@ApplicationScoped
public class DeleteDataOrderTopicsHandler implements CommandHandler {
  public static final CommandType COMMAND_TYPE = CommandType.DELETE;

  private final TopicService topicService;
  private final KafkaConfig kafkaConfig;
  private final StatusUpdateService statusUpdateService;
  private final KafkaSchemaRegistryClient kafkaSchemaRegistryClient;
  private final AppConfig appConfig;

  @Inject
  public DeleteDataOrderTopicsHandler(
      TopicService topicService,
      KafkaConfig kafkaConfig,
      StatusUpdateService statusUpdateService,
      KafkaSchemaRegistryClient kafkaSchemaRegistryClient,
      AppConfig appConfig) {
    this.topicService = topicService;
    this.kafkaConfig = kafkaConfig;
    this.statusUpdateService = statusUpdateService;
    this.kafkaSchemaRegistryClient = kafkaSchemaRegistryClient;
    this.appConfig = appConfig;
  }

  @Override
  public CompletionStage<Void> handle(Command command) {
    var dataOrderId = command.objectId().toString();
    var topicNames =
        List.of(
            kafkaConfig.getInputTopicName(dataOrderId),
            kafkaConfig.getNonRetryableDlqTopicName(dataOrderId),
            kafkaConfig.getOutputTopicName(dataOrderId));
    return Uni.createFrom()
        .item(topicNames)
        .chain(
            topics -> {
              if (appConfig.useConfluentIdentityPool()) {
                return topicService
                    .deleteDataOrderTopicsAcls(dataOrderId)
                    .replaceWith(topics)
                    .invoke(
                        result ->
                            Log.infof("Successfully deleted ACLs for dataorder: %s", dataOrderId));

              } else {
                Log.infof("Skipping deletion of ACLs for dataorder: %s", dataOrderId);
                return Uni.createFrom().item(topics);
              }
            })
        .chain(() -> topicService.deleteTopics(topicNames))
        .invoke(
            () ->
                Log.infof(
                    "Successfully deleted topics for dataorder %s: %s", dataOrderId, topicNames))
        .invoke(
            Unchecked.consumer(
                topics -> {
                  var inputTopicName = kafkaConfig.getInputTopicName(dataOrderId);
                  var inputTopicSchemaName = kafkaConfig.getTopicSchemaSubjectName(inputTopicName);
                  // https://docs.confluent.io/platform/current/schema-registry/schema-deletion-guidelines.html
                  var softDeleteResults =
                      kafkaSchemaRegistryClient.deleteSchema(inputTopicSchemaName, false);
                  Log.infof(
                      "Successfully soft deleted schema '%s' for topic '%s' with versionIds: %s",
                      inputTopicSchemaName, inputTopicName, softDeleteResults);
                  var hardDeleteResults =
                      kafkaSchemaRegistryClient.deleteSchema(inputTopicSchemaName, true);
                  Log.infof(
                      "Successfully permanently deleted schema '%s' for topic '%s' with versionIds: %s",
                      inputTopicSchemaName, inputTopicName, hardDeleteResults);
                }))
        .onItem()
        .transformToUni(
            topics ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.deleted(
                            mapToType(command, ConfigurationType.DATA_ORDER),
                            COMMAND_TYPE,
                            ProvisioningStep.KAFKA_TOPIC_DELETION,
                            new DeleteTopicCommandResult(topics.stream().sorted().toList()))))
        .onFailure()
        .recoverWithUni(
            throwable ->
                Uni.createFrom()
                    .completionStage(
                        statusUpdateService.failed(
                            mapToType(command, ConfigurationType.DATA_ORDER),
                            COMMAND_TYPE,
                            ProvisioningStep.KAFKA_TOPIC_DELETION,
                            throwable)))
        .subscribeAsCompletionStage();
  }
}
