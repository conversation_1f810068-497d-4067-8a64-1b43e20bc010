package org.ude.deployment.dataorder.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.vertx.core.json.JsonObject;
import java.util.List;
import lombok.Builder;
import org.ude.deployment.streamingpipeline.dto.PipelineDataOrderPropertiesConfig;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public record DataOrder(
    String name,
    String description,
    String projectId,
    boolean requiresDataHold,
    String orderId,
    List<String> schemas,
    DataOrderConfiguration configuration,
    JsonObject additionalProperties,
    PipelineDataOrderPropertiesConfig.MetadataProperties metadataProperties) {

  public record DataOrderConfiguration(int retentionPeriod, int partitionCount) {}
}
