package org.ude.deployment.dataorder.dto;

import io.confluent.kafka.schemaregistry.client.rest.entities.requests.RegisterSchemaResponse;
import java.util.List;
import org.ude.deployment.common.infrastructure.kafka.dto.DataOrderConnectionInfo;

public record CreateDataOrderCommandResult(
    List<String> topics,
    RegisterSchemaResponse inputTopicSchema,
    DataOrderConnectionInfo connectionInfo) {}
