api:
  messaging:
    # must be in sync with "topic-name-template" 
    # from https://github.com/cariad-ude/ude-str-data-ingestion-api/blob/main/src/main/resources/application.yaml
    topic-name-template-input: "private.ude-str.{dataOrder}.input.json"
    topic-name-template-output: "public.ude-str.{dataOrder}.output.json"
    topic-name-template-dlq-non-retryable: "private.ude-str.{dataOrder}.non-retryable.dlq.json"
    # must be in sync with "schema-subject-name-template"
    # from https://github.com/cariad-ude/ude-str-data-ingestion-api/blob/main/src/main/resources/application.yaml
    schema-subject-name-template: "{topicName}-value"
    register-schema-subject-name-template: "ude-str.schema.{schemaId}"
    topics:
      default-replication-factor: 1 # for test/local dev env on Redpanda
      default-max-message-bytes: 7340032 # 7mb (large message threshold - 5MB size plus a bit more)
  confluent:
    api-key: ${API_CONFLUENT_CLOUD_KEY}
    api-key-secret: ${API_CONFLUENT_CLOUD_SECRET}
    cluster-api-key: ${API_CONFLUENT_CLUSTER_KEY}
    cluster-api-key-secret: ${API_CONFLUENT_CLUSTER_SECRET}
    identity-provider: ${API_CONFLUENT_IDENTITY_PROVIDER_ID}
    cluster: ${API_CONFLUENT_CLUSTER_ID}
    connection-tenant-id: ${API_CONFLUENT_CONNECTION_TENANT_ID}
    identity-pool-name-template: "dao-{daoId}-{role}"
    identity-pool-description-template: "Identity pool for {role} of DAO '{daoName}'"
    client-consumer-group-prefix: "public."
    use-identity-pool: true

flink-deployment-config:
  deployment-name-template: "{deploymentType}-{dataOrderId}"
  namespace: ${NAMESPACE}
  flink-gdc-job-image-uri: ${FLINK_GDC_JOB_IMAGE_URI}
  flink-passthrough-job-image-uri: ${FLINK_PASSTHROUGH_JOB_IMAGE_URI}
  flink-init-container-image-uri: ${FLINK_INIT_CONTAINER_IMAGE_URI}
  schema-registry-url: ${API_KAFKA_SCHEMA_REGISTRY_URL}
  bootstrap-server: ${API_KAFKA_BOOTSTRAP_SERVERS}
  database-url: ${DB_URL}
  database-user-name: ${DB_USER_NAME}
  claims-database-name: ${CLAIMS_DB_NAME}
  claims-table-name: ${CLAIMS_TABLE_NAME}
  vdc-database-name: ${VDC_DB_NAME}
  vdc-rules-table-name: ${VDC_RULES_TABLE_NAME}
  vms-input-topic-name: ${KAFKA_VMS_SOURCE_TOPIC_NAME:private.ude-str.vms-connector.flattened.json}

large-message:
  azure:
    credential:
        tenant-id: ${AZURE_CONTRIBUTOR_CREDENTIAL_TENANT_ID}
        client-id: ${AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_ID}
        client-secret: ${AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_SECRET}
    out:
      container-name-template: "{retentionDays}d-{dataOrderId}"
      endpoint: ${AZURE_UPLOAD_BLOB_ENDPOINT}
      read-roles:
        - data-consumer
        - data-order-reader
      write-roles:
        - data-producer
        - data-order-manager
    in:
      container-name-template: "{dataOrderId}"
      endpoint: ${AZURE_DOWNLOAD_BLOB_ENDPOINT}

quarkus:
  log:
    console:
      json: false
  rest-client:
    # Enable the Application-Layer Protocol Negotiation (alpn) TLS extension and the client will negotiate which HTTP
    # version to use over the ones compatible by the server. By default, it will try to use HTTP/2 first
    # and if it’s not enabled, it will use HTTP/1.1.
    alpn: true
    read-timeout: 15000
    confluent-identity-providers-service:
      url: https://api.confluent.cloud/
    confluent-cluster-service:
      url: ${API_CONFLUENT_CLUSTER_API_URL}
  jib:
    base-jvm-image: ${REGISTRY_NAME}.azurecr.io/docker.io/library/eclipse-temurin:21.0.7_6-jre-alpine


mp:
  messaging:
    incoming:
      commands:
        group:
          id: ${API_KAFKA_CONSUMER_GROUP:deployment-service-consumer-group}
        connector: smallrye-kafka
        topic: ${API_KAFKA_INCOMING_TOPIC_COMMAND:private.ude-str.commands.json}
        value-deserialization-failure-handler: deserialization-failure-handler
        enable:
          auto:
            commit: false
    outgoing:
      status:
        connector: smallrye-kafka
        topic: ${API_KAFKA_OUTGOING_TOPIC_STATUS:private.ude-str.status.json}
        value:
          serializer: io.quarkus.kafka.client.serialization.ObjectMapperSerializer
      commands-out:
        connector: smallrye-kafka
        topic: ${API_KAFKA_INCOMING_TOPIC_COMMAND:private.ude-str.commands.json}
        value:
          serializer: io.quarkus.kafka.client.serialization.ObjectMapperSerializer
      dead-letter:
        connector: smallrye-kafka
        topic: ${API_KAFKA_OUTGOING_TOPIC_COMMAND_DEAD_LETTER:private.ude-str.commands-dead-letter.bytes}
        value:
          serializer: io.quarkus.kafka.client.serialization.ObjectMapperSerializer

kafka:
  latest:
    compatibility:
      strict: false
    cache:
      ttl:
        sec: 180
  use:
    latest:
      version: true

"%dev,test":
  quarkus:
    rest-client:
      logging:
        scope: request-response
        body-limit: 10000
    log:
      level: DEBUG
  

"%dev,prod":
  kafka:
    bootstrap:
      servers: ${API_KAFKA_BOOTSTRAP_SERVERS}
    schema:
      format: jsonschema
      registry:
        url: ${API_KAFKA_SCHEMA_REGISTRY_URL}
        basic:
          auth:
            credentials:
              source: USER_INFO
            user:
              info: ${API_SCHEMA_REGISTRY_USERNAME}:${API_SCHEMA_REGISTRY_PASSWORD}

"%prod":
  kafka:
    security:
      protocol: SASL_SSL
    sasl:
      jaas:
        config: org.apache.kafka.common.security.plain.PlainLoginModule required username='${API_KAFKA_SASL_USERNAME}' password='${API_KAFKA_SASL_PASSWORD}';
      mechanism: PLAIN
    acks: all
  quarkus:
    container-image:
      build: true
      push: true
      builder: jib
    log:
      console:
        json: true
    kafka:
      health:
        enabled: true
  api:
    messaging:
      topics:
        # https://stackoverflow.com/questions/60194656/kafka-stream-policyviolationexception-topic-replication-factor-must-be-3
        # kafka provider (confluent) and by default the replication.factor is set to 3 when creating a new topic.
        default-replication-factor: 3

"%test":
  api:
    confluent:
      api-key: API_KEY
      api-key-secret: API_SECRET
      cluster-api-key: API_CLUSTER_KEY
      cluster-api-key-secret: API_CLUSTER_SECRET
      identity-provider: idnt-prvdr
      cluster: clstr
      connection-tenant-id: c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c
  flink-deployment-config:
    namespace: ude-str-test
    flink-gdc-job-image-uri: test/gdc-job:main
    flink-passthrough-job-image-uri: test/passthrough-job:main
    flink-init-container-image-uri: test/init-container:main
    schema-registry-url: ${quarkus.wiremock-url}
    bootstrap-server: bootstrap-server.test:9092
    database-url: test-database
    database-user-name: user
    claims-database-name: claims
    claims-table-name: processing_claim
    vdc-database-name: signals
    vdc-rules-table-name: normalization_rule
    vms-input-topic-name: private.ude-str.vms-connector.flattened.json
  quarkus:
    kafka:
      devservices:
        provider: redpanda
        image-name: docker.redpanda.com/redpandadata/redpanda:v24.3.10
        # Create topics required by the deployment service
        topic-partitions:
          private.ude-str.status.json: 1
          private.ude-str.commands.json: 1
          private.ude-str.commands-dead-letter.bytes: 1
    http:
      test-port: 0
    rest-client:
      confluent-identity-providers-service:
        url: ${quarkus.wiremock-url}
      confluent-cluster-service:
        url: ${quarkus.wiremock-url}
  kafka:
    schema:
      registry:
        url: ${quarkus.wiremock-url}
        basic:
          auth:
            user:
              info: user:pass
  large-message:
    azure:
      credential:
        connection-string: ${testcontainers.azure.devstoreaccount1.connection-string}
      in:
        endpoint: ${testcontainers.azure.devstoreaccount1.endpoint:https://storeandexplorein.blob.core.windows.net}
      out:
        endpoint: ${testcontainers.azure.devstoreaccount2.endpoint:https://storeandexploreout.blob.core.windows.net}
