apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: {{FLINK_APPLICATION_NAME}}
  namespace: {{NAMESPACE}}
  labels:
    argocd.argoproj.io/instance: {{NAMESPACE}}_deployment-service
  annotations:
    argocd.argoproj.io/sync-options: "Prune=false"
    argocd.argoproj.io/compare-options: "IgnoreExtraneous"
    argocd.argoproj.io/hook: "Skip"
spec:
  image: {{FLINK_IMAGE_NAME}}
  imagePullPolicy: Always
  flinkVersion: v1_20
  mode: native
  podTemplate:
    metadata:
      labels: 
        app.kubernetes.io/name: {{FLINK_APPLICATION_NAME}}
        pipelineName: {{PIPELINE_NAME}}
        projectId: {{PROJECT_ID}}
        dataOrderId: {{DATA_ORDER_ID}}
        jobType: passthrough-job
    spec:
      initContainers:
        - name: flink-init-container
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            runAsNonRoot: true
            runAsUser: 9999
          image: busybox
          command: [ "/bin/sh", "/opt/ude/scripts/init-flink.sh" ]
          envFrom:
            - configMapRef:
                name: deployment-service-store-and-explore-config
            - configMapRef:
                name: deployment-service-ude-flink-storage-config
          env:
            - name: FLINK_APPLICATION_NAME
              value: {{FLINK_APPLICATION_NAME}}
            - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: deployment-service-store-and-explore-upload-spn-secret
                  key: azure_credential_client_secret
            - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: deployment-service-ude-flink-storage-spn-secret
                  key: azure_credential_client_secret
                  optional: true
          volumeMounts:
            - name: ude-shell-scripts
              mountPath: /opt/ude/scripts
            - name: ude-flink-custom-configs
              mountPath: /tmp/ude/flink-custom-configs
            - name: ude-flink-devops-tuning-configs
              mountPath: /tmp/ude/flink-devops-tuning
            - name: ude-flink-conf-temp
              mountPath: /tmp/ude/flink-conf
            - name: flink-config-volume
              mountPath: /tmp/real-flink-config
      containers:
        - name: flink-main-container
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            runAsNonRoot: true
            runAsUser: 9999
          envFrom:
            - configMapRef:
                name: deployment-service-store-and-explore-config
            - configMapRef:
                name: deployment-service-ude-flink-storage-config
          env:
            - name: BOOTSTRAP_SERVERS
              value: {{BOOTSTRAP_SERVERS}}
            - name: SCHEMA_REGISTRY_URL
              value: {{SCHEMA_REGISTRY_URL}}
            - name: SCHEMA_REGISTRY_API_KEY
              valueFrom:
                secretKeyRef:
                  name: deployment-service-gdc-job-schema-registry-secret
                  key: confluent_schema_registry_key
            - name: SCHEMA_REGISTRY_API_SECRET
              valueFrom:
                secretKeyRef:
                  name: deployment-service-gdc-job-schema-registry-secret
                  key: confluent_schema_registry_secret
            - name: KAFKA_CLUSTER_API_SECRET
              valueFrom:
                secretKeyRef:
                  name: deployment-service-gdc-job-kafka-credentials
                  key: confluent_kafka_api_secret
            - name: KAFKA_CLUSTER_API_KEY
              valueFrom:
                secretKeyRef:
                  name: deployment-service-gdc-job-kafka-credentials
                  key: confluent_kafka_api_key
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: deployment-service-db-password
                  key: postgresql_admin_password
            - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: deployment-service-store-and-explore-upload-spn-secret
                  key: azure_credential_client_secret
            - name: AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: deployment-service-store-and-explore-download-spn-secret
                  key: azure_credential_client_secret

            - name: FLINK_CONF_DIR
              value: /opt/flink/conf-ude
            # NORMAL env variables
            - name: KAFKA_PASSTHROUGH_SOURCE_TOPIC_NAME
              value: {{FLINK_INPUT_TOPIC}}
            - name: KAFKA_SINK_TOPIC_NAME
              value: {{FLINK_OUTPUT_TOPIC}}
            - name: KAFKA_NON_RETRYABLE_DLQ_TOPIC_NAME
              value: {{NON_RETRYABLE_DLQ_TOPIC}}
            - name: FLINK_GROUP_ID
              value: {{FLINK_GROUP_ID}}
            - name: DB_URL
              value: {{DB_URL}}
            - name: DB_USER_NAME
              value: {{DB_USER_NAME}}
            - name: DB_CLAIMS_DATABASE_NAME
              value: {{CLAIMS_DATABASE_NAME}}
            - name: DB_PROCESSING_CLAIM_TABLE_NAME
              value: {{CLAIMS_PROCESSING_TABLE_NAME}}
            - name: API_USE_CASE_ID
              value: {{USE_CASE_ID}}
            - name: DATA_ORDER_RETENTION_DAYS
              value: '{{DATA_ORDER_RETENTION_DAYS}}'
          volumeMounts:
            - name: ude-flink-conf-temp
              mountPath: /opt/flink/conf-ude
            - mountPath: /opt/flink/log
              name: flink-logs
            - mountPath: /opt/flink/downloads
              name: downloads
      volumes:
        - name: ude-shell-scripts
          configMap:
            name: deployment-service-ude-shell-scripts
        - name: ude-flink-custom-configs
          configMap:
            name: deployment-service-flink-conf-yaml
        - name: ude-flink-devops-tuning-configs
          configMap:
            name: devops-tuning-{{FLINK_APPLICATION_NAME}}
            optional: true
        - name: ude-flink-conf-temp
          emptyDir: { }
        - name: flink-logs
          emptyDir: { }
        - name: downloads
          emptyDir: { }
  flinkConfiguration:
    # ude custom properties, are used by the backend code as small key-value database
    # do NOT remove it
    _ude.pipeline.dataOrderId: {{DATA_ORDER_ID}}
    _ude.pipeline.name: {{PIPELINE_NAME}}
    _ude.pipeline.projectId: {{PROJECT_ID}}
    _ude.pipeline.revision: "1"

    kubernetes.jobmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/
    kubernetes.taskmanager.annotations: prometheus.io/scrape:true,prometheus.io/port:9249,prometheus.io/path:/

    classloader.resolve-order: parent-first

    taskmanager.numberOfTaskSlots: "18"

    #jobmanager.heap.size: ""
    jobmanager.memory.process.size: "1GB"
    #taskmanager.heap.size: ""

    #taskmanager.memory.task.heap.size: "1GB"
    taskmanager.memory.process.size: "5GB"
    env.java.opts.all: "--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED -XX:+UseParallelGC -XX:ParallelGCThreads=2 -Xmx2000m -Xms1048m -XX:SurvivorRatio=3 -XX:GCTimeRatio=2 -XX:MaxGCPauseMillis=6000 -XX:ConcGCThreads=2 -XX:+UseStringDeduplication"
    job.autoscaler.enabled: "false"
    job.autoscaler.memory.tuning.enabled: "false"
    # Enable Adaptive scheduler to play the in-place rescaling.
    jobmanager.scheduler: adaptive
    job.autoscaler.memory.tuning.overhead: "1"
    # jobmanager.memory.jvm-metaspace.size: "1GB"
    # Enable autoscale and scaling
    job.autoscaler.scaling.enabled: "false"
    job.autoscaler.stabilization.interval : 1m
    job.autoscaler.metrics.window : 1m
    # Enable flame graph for statistics !!!! Only for dev and pre-prod envs !!!!
    rest.flamegraph.enabled: "{{#eq NAMESPACE 'prod'}}false{{else}}true{{/eq}}"
    rest.flamegraph.refresh-interval: "10 m"
    rest.profiling.enabled: "{{#eq NAMESPACE 'prod'}}false{{else}}true{{/eq}}"

    metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
    metrics.reporter.prom.port: "9249"
    kubernetes.container.image.pull-secrets: mega-container-registry-secret

  ingress:
    template: "{{NAMESPACE}}.apps.mega.cariad.cloud/\{{name}}(/|$)(.*)"
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/auth-secret: deployment-service-flink-basic-auth
      nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Flink Playground"
      nginx.ingress.kubernetes.io/rewrite-target: "/$2"
  jobManager:
    resource:
      memory: "{{JOB_MANAGER_MEMORY}}"
      cpu: {{JOB_MANAGER_CPU}}
  taskManager:
    resource:
      memory: "{{TASK_MANAGER_MEMORY}}"
      cpu: {{TASK_MANAGER_CPU}}
  serviceAccount: deployment-service
  job:
    jarURI: local:///opt/flink/lib/flink-job-all.jar
    entryClass: org.ude.PassthroughJob
    parallelism: 16
    upgradeMode: stateless
  logConfiguration:
    "logback-console.xml": |
      <configuration>
        <property name="pattern" value="%d{ISO8601} %highlight(%-5.5level) %magenta(%-12.36thread) %cyan(%-12.36logger{36}) %msg%n" />
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.out</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>DENY</onMatch>
            </filter>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
            <target>System.err</target>
            <immediateFlush>true</immediateFlush>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
            <encoder>
                <pattern>${pattern}</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="STDOUT" />
            <appender-ref ref="STDERR" />
        </root>

        <!-- Specific loggers -->
        <logger name="akka" level="INFO"/>
        <logger name="org.apache.kafka" level="INFO"/>
        <logger name="org.apache.hadoop" level="INFO"/>
        <logger name="org.apache.zookeeper" level="INFO"/>
        <logger name="org.apache.flink.shaded.akka.org.jboss.netty.channel.DefaultChannelPipeline" level="OFF"/>
      </configuration>
