analyzer:
  skip_excluded: true
excludes:
  scopes:
    - pattern: "annotationProcessor"
      reason: BUILD_DEPENDENCY_OF
      comment: "Annotation processor used at build time"
    - pattern: "compileClasspath"
      reason: BUILD_DEPENDENCY_OF
      comment: "Compile classpath used during build"
    - pattern: "integrationTest.*"
      reason: TEST_DEPENDENCY_OF
      comment: "Integration test configurations"
    - pattern: "jacoco.*"
      reason: TEST_DEPENDENCY_OF
      comment: "Jacoco test coverage tools"
    - pattern: "lombok"
      reason: BUILD_DEPENDENCY_OF
      comment: "Lombok generates getters and setters during build"
    - pattern: "nativeTest.*"
      reason: TEST_DEPENDENCY_OF
      comment: "Native test configurations"
    - pattern: "quarkusDev.*"
      reason: DEV_DEPENDENCY_OF
      comment: "Quarkus development-time dependencies"
    - pattern: "quarkusGeneratedSources.*"
      reason: BUILD_DEPENDENCY_OF
      comment: "Generated sources for Quarkus during build"
    - pattern: "quarkusProdBaseRuntimeClasspathConfiguration"
      reason: BUILD_DEPENDENCY_OF
      comment: "Base runtime for Quarkus prod"
    - pattern: "quarkusProdCompileOnlyConfiguration"
      reason: BUILD_DEPENDENCY_OF
      comment: "Compile-only configuration for Quarkus prod"
    - pattern: "quarkusProdRuntimeClasspathConfigurationDeployment"
      reason: BUILD_DEPENDENCY_OF
      comment: "Deployment runtime for Quarkus prod"
    - pattern: "quarkusProdRuntimeClasspathConfigurationPlatform"
      reason: BUILD_DEPENDENCY_OF
      comment: "Platform runtime for Quarkus prod"
    - pattern: "quarkusTest.*"
      reason: TEST_DEPENDENCY_OF
      comment: "Quarkus test-related configurations"
    - pattern: "runtimeClasspath"
      reason: RUNTIME_DEPENDENCY_OF
      comment: "Default runtime classpath not used"
    - pattern: "test.*"
      reason: TEST_DEPENDENCY_OF
      comment: "Standard test configurations"
 