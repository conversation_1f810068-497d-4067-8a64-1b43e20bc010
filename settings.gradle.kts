pluginManagement {
  val pluginDependenciesVersion: String by settings
  val pluginGitHookVersion: String by settings
  val pluginLombokVersion: String by settings
  val pluginSonarqubeVersion: String by settings
  val pluginSpotlessVersion: String by settings
  val quarkusPluginVersion: String by settings

  repositories {
    mavenCentral()
    gradlePluginPortal()
    mavenLocal()
  }

  plugins {
    id("com.diffplug.spotless") version pluginSpotlessVersion
    id("com.github.ben-manes.versions") version pluginDependenciesVersion
    id("io.freefair.lombok") version pluginLombokVersion
    id("io.quarkus") version quarkusPluginVersion
    id("org.danilopianini.gradle-pre-commit-git-hooks") version pluginGitHookVersion
    id("org.sonarqube") version pluginSonarqubeVersion
  }
}

plugins {
  id("org.danilopianini.gradle-pre-commit-git-hooks")
}

gitHooks {
  preCommit {
    from {
      "export JAVA_HOME=\"${System.getProperty("java.home")}\"\n"
    }
    tasks("-q", "spotlessCheck", "--info", "--stacktrace")
  }
  createHooks(true)
}

rootProject.name = "ude-str-deployment-service"
