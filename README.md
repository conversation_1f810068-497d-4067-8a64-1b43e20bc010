# ude-str-deployment-service

## Running the application in dev mode

### Configuration options

This setup is valid with connection to Confluent DEV env because Confluent Cloud API cannot be fully
emulated.

| Option                               | Default                                                       | Description                                                                    |
|--------------------------------------|---------------------------------------------------------------|--------------------------------------------------------------------------------|
| `API_KAFKA_BOOTSTRAP_SERVERS`        | `localhost:9092`                                              | List of one or more brokers for Kafka                                          |
| `API_KAFKA_SCHEMA_REGISTRY_URL`      | `http://localhost:8081`                                       | Schema Registry URL for the HTTP request                                       |
| `API_SCHEMA_REGISTRY_USERNAME`       | `ADMIN`                                                       | Schema Registry Login (tmp value for local env)                                |  
| `API_SCHEMA_REGISTRY_PASSWORD`       | `PASSWORD`                                                    | Schema Registry Password (tmp value for local env)                             |
| `API_CONFLUENT_CLOUD_KEY`            | `N/A`                                                         | Secret `ude-str-dev-eu-deployment-svc-cloud-api-service-account-id`            |
| `API_CONFLUENT_CLOUD_SECRET`         | `N/A`                                                         | Secret `ude-str-dev-eu-deployment-svc-cloud-api-service-account-secret`        |
| `API_CONFLUENT_IDENTITY_PROVIDER_ID` | `op-j93O`                                                     | Provider ID from DEV env                                                       |
| `API_CONFLUENT_CLUSTER_ID`           | `lkc-9od2y7`                                                  | ...                                                                            |
| `API_CONFLUENT_CLUSTER_API_URL`      | `https://pkc-09081p.westeurope.azure.confluent.cloud`         |                                                                                |
| `API_CONFLUENT_CLUSTER_KEY`          | `N/A`                                                         | Secret `ude-str-dev-eu-deployment-service-kafka-service-account-public-id`     |
| `API_CONFLUENT_CLUSTER_SECRET`       | `N/A`                                                         | Secret `ude-str-dev-eu-deployment-service-kafka-service-account-public-secret` |
| `API_CONFLUENT_CONNECTION_TENANT_ID` | `c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c`                        | Vokswage CARIAD Tenant                                                         |
| `NAMESPACE`                          | `dev`                                                         | Env to format Flink Job Commands                                               |                                                           |
| `FLINK_GDC_JOB_IMAGE_URI`            | `crmegahubwesteurope.azurecr.io/str-dev/gdc-job:main`         | ...                                                                            |
| `FLINK_PASSTHROUGH_JOB_IMAGE_URI`    | `crmegahubwesteurope.azurecr.io/str-dev/passthrough-job:main` | ...                                                                            |
| `DB_URL`                             | `jdbc:postgresql://<Management API DB>`                       | Connection to Management API DB to support claims                              |
| `DB_USER_NAME`                       | `psqladmin`                                                   |                                                                                |
| `CLAIMS_DB_NAME`                     | `management-api`                                              |                                                                                |
| `CLAIMS_TABLE_NAME`                  | `processing_claim`                                            |                                                                                |
| `VDC_DB_NAME`                        | `signals`                                                     |                                                                                |
| `VDC_RULES_TABLE_NAME`               | `normalization_rule`                                          |                                                                                |

### Start

You can run your application in dev mode that enables live coding and connection to local Redpanda
instance using:

```shell script
docker compose up -d
QUARKUS_PROFILE=dev ./gradlew quarkusDev
```

> **_NOTE:_** Connection errors on Windows WSL ("Error connecting to node redpanda-ude-str:9092")
> could be fixed with folowing registration in local `/etc/hosts`:

```
127.0.0.1       redpanda-ude-str
```

## Commands

### Command Format:

```json
{
  "commandId": "...",
  "commandType": "...",
  "type": "...",
  "objectId": "...",
  "properties": {}
}
```

- `commandId` - Unique ID
- `objectId` - UUID of the associated entity (e.g., Management API database entry)
- `commandType` - (
  see: [CommandType.java](src/main/java/org/ude/deployment/common/command/CommandType.java))
- `type` - (
  see: [ConfigurationType.java](src/main/java/org/ude/deployment/common/domain/ConfigurationType.java))
- `properties` - Dynamic JsonObject

### Existing Commands

| Title                                                                                                                     | `commandType` | `type`             | Description                                                                                                                                                                                           |
|---------------------------------------------------------------------------------------------------------------------------|---------------|--------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| [Update Flink Job](src/main/java/org/ude/deployment/flink/dto/command/UpdatePipelineCommand.java)                         | `UPDATE`      | `FlinkJobPipeline` | This update modifies the existing pipeline by adding `additionalProperties` and `metadataProperties`. Existing Flink jobs will be stopped without creating a savepoint, followed by a new deployment. |
| [Create Azure Blob Storage](src/main/java/org/ude/deployment/azureblobstorage/command/CreateAzureBlobStorageCommand.java) | `CREATE`      | `AzureBlobStorage` | Creates a container based on the `retentionPeriod` for use by a data order on both IN and OUT accounts. See the Azure FS section.                                                                     |

## DevOps

### Flink Deployment Configuration

- All configuration parameters are located in the following directory within the Helm folder:
  `helm/deployment-service/config`.
- Environment variables are supported in the format: `${MY_ENV_VAR}`.

### Flink Deployment Tuning

- Create a new ConfigMap named `devops-tuning-FLINK_APPLICATION_NAME`.  
  For example: `devops-tuning-gdc-0fc335c4-7a70-40fb-aa29-58991c14205f`.
- The contents of this ConfigMap (all the `.yaml` files) will be appended to the end of the Flink
  configuration file.
- After completing the tuning, you may remove the ConfigMap.

## Azure FS

### Configuration

| Option                                       | Default | Description                       |
|----------------------------------------------|---------|-----------------------------------|
| `AZURE_UPLOAD_CREDENTIAL_TENANT_ID`          | `N/A`   | (OUT) Azure Tenant ID             |
| `AZURE_UPLOAD_CREDENTIAL_CLIENT_ID`          | `N/A`   | (OUT) Azure Client ID             |
| `AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET`      | `N/A`   | (OUT) Azure Client Secret         |
| `AZURE_DOWNLOAD_CREDENTIAL_TENANT_ID`        | `N/A`   | (IN) Azure Tenant ID              |
| `AZURE_DOWNLOAD_CREDENTIAL_CLIENT_ID`        | `N/A`   | (IN) Azure Client ID              |
| `AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET`    | `N/A`   | (IN) Azure Client Secret          |
| `AZURE_CONTRIBUTOR_CREDENTIAL_TENANT_ID`     | `N/A`   | (CONTRIBUTOR) Azure Tenant ID     |
| `AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_ID`     | `N/A`   | (CONTRIBUTOR) Azure Client ID     |
| `AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_SECRET` | `N/A`   | (CONTRIBUTOR) Azure Client Secret |
| `AZURE_STORAGE_ACCOUNT_NAME`                 | `N/A`   | Name of the Store&Explore account |

### FileSystems (Containers)

> [!CAUTION]
> The fs driver does not create the root container. DevOps is **responsible** for this.

```shell
az storage container create --name flink --account-name $AZURE_UPLOAD_CREDENTIAL_TENANT_ID --auth-mode login
```

## Packaging and running the application

The application can be packaged using:

```shell script
./gradlew build
```

It produces the `quarkus-run.jar` file in the `build/quarkus-app/` directory.
Be aware that it’s not an _über-jar_ as the dependencies are copied into the`build/quarkus-app/lib/`
directory.

The application is now runnable using `java -jar build/quarkus-app/quarkus-run.jar`.

If you want to build an _über-jar_, execute the following command:

```shell script
./gradlew build -Dquarkus.package.jar.type=uber-jar
```

The application, packaged as an _über-jar_, is now runnable using `java -jar build/*-runner.jar`.

## Creating a native executable

You can create a native executable using:

```shell script
./gradlew build -Dquarkus.native.enabled=true
```

Or, if you don't have GraalVM installed, you can run the native executable build in a container
using:

```shell script
./gradlew build -Dquarkus.native.enabled=true -Dquarkus.native.container-build=true
```

You can then execute your native executable with:
`./build/ude-str-deployment-service-0.0.1-SNAPSHOT-runner`

If you want to learn more about building native executables, please
consult <https://quarkus.io/guides/gradle-tooling>.

## Related Guides

- REST Jackson ([guide](https://quarkus.io/guides/rest#json-serialisation)): Jackson serialization
  support for Quarkus REST. This extension is not compatible with the quarkus-resteasy extension, or
  any of the extensions that depend on it
- Messaging - Kafka Connector ([guide](https://quarkus.io/guides/kafka-getting-started)): Connect to
  Kafka with Reactive Messaging
- SmallRye Health ([guide](https://quarkus.io/guides/smallrye-health)): Monitor service health

## Provided Code

### Messaging codestart

Use Quarkus Messaging

[Related Apache Kafka guide section...](https://quarkus.io/guides/kafka-reactive-getting-started)

### REST

Easily start your REST Web Services

[Related guide section...](https://quarkus.io/guides/getting-started-reactive#reactive-jax-rs-resources)

### SmallRye Health

Monitor your application's health using SmallRye Health

[Related guide section...](https://quarkus.io/guides/smallrye-health)

This project uses Quarkus, the Supersonic Subatomic Java Framework.

If you want to learn more about Quarkus, please visit its website: <https://quarkus.io/>.

## Contribution

After starting a story switch the story state to in progress and create a feature branch
based on the main branch. Please use the following naming schema to create your branch:

`feature/<STORY-ID>-<SmallDescription`>

**Feature branch example:** feature/E3AUDEDM-1234-AddOAuthMechanism

When the story is finished, commit and push it to GitHub.

The final step would be to create a pull request to merge your feature branch back to the
main branch. Please use the format `<STORY_ID>: <TITLE>` as PR title as it will be used for the
release notes generation. Also please use one of the following pull request labels which are used to
categorize the features contained in the realease notes:

| Label                | Description                                                               |
|----------------------|---------------------------------------------------------------------------|
| `ignore-for-release` | Exclude the pull request out of the release notes                         |
| `feature`            | Lists the pull request as new feature                                     |
| `feature-china`      | Used to make China specific config changes more visible to the China team |
| `dependencies`       | Used by dependabot for pull requests to update dependencies               |

Please make sure that you are following our code format standards and keeping our code quality.
After the pull request was merged please make sure that the automatic deployment succeeded.
