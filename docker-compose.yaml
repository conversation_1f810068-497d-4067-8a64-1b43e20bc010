name: redpanda-quickstart-one-broker
networks:
  redpanda_network:
    driver: bridge
volumes:
  redpanda-ude-str: null
services:
  redpanda-ude-str:
    command:
      - redpanda
      - start
      - --kafka-addr internal://0.0.0.0:9092,external://localhost:19092
      # Address the broker advertises to clients that connect to the Kafka API.
      # Use the internal addresses to connect to the Redpanda brokers'
      # from inside the same Docker network.
      # Use the external addresses to connect to the Redpanda brokers'
      # from outside the Docker network.
      - --advertise-kafka-addr internal://redpanda-ude-str:9092,external://localhost:19092
      - --pandaproxy-addr internal://0.0.0.0:8082,external://localhost:18082
      # Address the broker advertises to clients that connect to the HTTP Proxy.
      - --advertise-pandaproxy-addr internal://redpanda-ude-str:8082,external://localhost:18082
      - --schema-registry-addr internal://0.0.0.0:8081,external://localhost:18081
      # Redpanda brokers use the RPC API to communicate with each other internally.
      - --rpc-addr redpanda-ude-str:33145
      - --advertise-rpc-addr redpanda-ude-str:33145
      # Mode dev-container uses well-known configuration properties for development in containers.
      - --mode dev-container
      # Tells Seastar (the framework Redpanda uses under the hood) to use 1 core on the system.
      - --smp 1
      - --default-log-level=info
    image: docker.redpanda.com/redpandadata/redpanda:v24.2.7
    container_name: redpanda-ude-str
    volumes:
      - redpanda-ude-str:/var/lib/redpanda/data
    networks:
      - redpanda_network
    ports:
      - 18081:18081
      - 18082:18082
      - 19092:19092
      - 19644:9644
      - 9092:9092
      - 8081:8081
  
  console:
    container_name: redpanda-console
    image: docker.redpanda.com/redpandadata/console:v2.7.2
    networks:
      - redpanda_network
    entrypoint: /bin/sh
    command: -c 'echo "$$CONSOLE_CONFIG_FILE" > /tmp/config.yml; /app/console'
    environment:
      CONFIG_FILEPATH: /tmp/config.yml
      CONSOLE_CONFIG_FILE: |
        kafka:
          brokers: ["redpanda-ude-str:9092"]
          schemaRegistry:
            enabled: true
            urls: ["http://redpanda-ude-str:8081"]
        redpanda:
          adminApi:
            enabled: true
            urls: ["http://redpanda-ude-str:9644"]
    ports:
      - 8100:8080
    depends_on:
      - redpanda-ude-str
