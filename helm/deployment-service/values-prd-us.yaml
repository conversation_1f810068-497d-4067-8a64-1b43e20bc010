global:
  environment: prd-us
image:
  repository: crmegahubeastus2.azurecr.io/udestrus-prd/deployment-service
  tag: 0.0.9
  pullPolicy: Always
  commitSha: "40f28e3c"
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80
envFrom:
  - configMapRef:
      name: deployment-service-store-and-explore-config
env:
  - name: AZURE_LOG_LEVEL
    value: DEBUG
  - name: API_KAFKA_BOOTSTRAP_SERVERS
    value: pkc-z3rddy.westus2.azure.confluent.cloud:9092
  - name: API_KAFKA_SCHEMA_REGISTRY_URL
    value: https://psrc-4j1d2.westus2.azure.confluent.cloud
  - name: API_KAFKA_INCOMING_TOPIC_COMMAND
    value: private.ude-str.commands.json
  - name: API_KAFKA_OUTGOING_TOPIC_STATUS
    value: private.ude-str.status.json
  - name: API_KAFKA_OUTGOING_TOPIC_COMMAND_DEAD_LETTER
    value: private.ude-str.commands-dead-letter.bytes
  - name: NAMESPACE
    value: udestrus-prd
  - name: FLINK_GDC_JOB_IMAGE_URI
    value: crmegahubeastus2.azurecr.io/udestrus-prd/gdc-job:f120-0.1.2
  - name: FLINK_INIT_CONTAINER_IMAGE_URI
    value: crmegahubeastus2.azurecr.io/docker.io/library/busybox
  - name: FLINK_PASSTHROUGH_JOB_IMAGE_URI
    value: crmegahubeastus2.azurecr.io/udestrus-prd/passthrough-job:main
  - name: DB_URL
    value: "************************************************************************************************,ude-str-prd-us-postgres-replica2-winning-shad.postgres.database.azure.com:5432,ude-str-prd-us-postgres-replica3-winning-shad.postgres.database.azure.com:5432"
  - name: DB_USER_NAME
    value: "psqladmin"
  - name: CLAIMS_DB_NAME
    value: "management-api"
  - name: CLAIMS_TABLE_NAME
    value: "processing_claim"
  - name: KAFKA_VMS_SOURCE_TOPIC_NAME
    value: private.ude-str.vms-connector.flattened.json
  - name: VDC_DB_NAME
    value: "signals"
  - name: VDC_RULES_TABLE_NAME
    value: "normalization_rule"
  - name: API_CONFLUENT_IDENTITY_PROVIDER_ID
    value: op-jxNM
  - name: API_CONFLUENT_CLUSTER_ID
    value: lkc-9rkxnv
  - name: API_CONFLUENT_CLUSTER_API_URL
    value: https://pkc-z3rddy.westus2.azure.confluent.cloud
  - name: API_CONFLUENT_CONNECTION_TENANT_ID
    value: c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c
  - name: QUARKUS_LOG_LEVEL
    value: INFO # should be WARN in the future once prod is stable
configMap:
  store-and-explore-config:
    AZURE_STORAGE_ACCOUNT_NAME: "studestoreexpmwus2pwustl"
    AZURE_UPLOAD_BLOB_ENDPOINT: "https://studestoreexpmwus2pwustl.blob.core.windows.net"
    AZURE_DOWNLOAD_BLOB_ENDPOINT: "https://studestoreexpmeus2psapi.blob.core.windows.net"
    # ude-str-prd-us-se-blob-writer 
    AZURE_UPLOAD_CREDENTIAL_CLIENT_ID: "2a91171e-f20b-49fd-b09f-047524a46c55"
    AZURE_UPLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-prd-us-deployment-svc-blob-data-owner
    AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_ID: "491d3e8a-ae12-435c-a6aa-b96c96f12ffe"
    AZURE_CONTRIBUTOR_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-prd-us-se-blob-reader
    AZURE_DOWNLOAD_CREDENTIAL_CLIENT_ID: "0921deb9-82e4-4f28-95a0-bc5e40dcc986"
    AZURE_DOWNLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
secretStore:
  - authType: "WorkloadIdentity"
    name: 'megatron'
    vaultUrl: "https://boston-d5470e24.vault.azure.net/"
    serviceAccountName: "boston-d5470e24-identity"
  - authType: "WorkloadIdentity"
    name: 'team-kv'
    vaultUrl: "https://ude-str-prd-us-kv.vault.azure.net/"
    serviceAccountName: "boston-d5470e24-identity"
externalSecret:
  enabled: true
  refreshInterval: "1m"
  files:
    kafka-credentials:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        kafka_sasl_username:
          remoteRef:
            key: ude-str-prd-us-ingest-api-kafka-service-account-public-id
        kafka_sasl_password:
          remoteRef:
            key: ude-str-prd-us-ingest-api-kafka-service-account-public-secret
    schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-prd-us-deployment-svc-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-prd-us-deployment-svc-schema-registry-service-account-secret
    confluent-cluster-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-prd-us-deployment-service-kafka-service-account-public-id
        password:
          remoteRef:
            key: ude-str-prd-us-deployment-service-kafka-service-account-public-secret
    confluent-cloud-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-prd-us-deployment-svc-cloud-api-service-account-id
        password:
          remoteRef:
            key: ude-str-prd-us-deployment-svc-cloud-api-service-account-secret
    store-and-explore-upload-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250404T144425
    store-and-explore-download-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250404T144111
    store-and-explore-contributor-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250404T144111
    gdc-job-schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-prd-us-gdc-job-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-prd-us-gdc-job-schema-registry-service-account-secret
    gdc-job-kafka-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_kafka_api_key:
          remoteRef:
            key: ude-str-prd-us-gdc-job-kafka-service-account-public-id
        confluent_kafka_api_secret:
          remoteRef:
            key: ude-str-prd-us-gdc-job-kafka-service-account-public-secret
    db-password:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        postgresql_admin_password:
          remoteRef:
            key: ude-str-prd-us-psql-admin-password
    flink-basic-auth:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        auth:
          remoteRef:
            key: ude-str-prd-us-basicauth
ingress:
  enabled: false
service:
  annotations:
    external-dns.alpha.kubernetes.io/hostname: udestrus-prd-deployment-service.apps.mega.cariad.cloud
    service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: /health/ready
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
  type: LoadBalancer
  port: 80
  targetPort: 8080
