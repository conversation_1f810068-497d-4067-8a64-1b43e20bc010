# GDC-specific Flink configuration
flink-conf.yaml: |
  # jvm (do not enable debugging for pre-prod and prod)
  env.java.opts.all: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED -XX:+UseParallelGC -XX:ParallelGCThreads=2 -Xmx2000m -Xms1048m -XX:SurvivorRatio=3 -XX:GCTimeRatio=2 -XX:MaxGCPauseMillis=6000 -XX:ConcGCThreads=2 -XX:+UseStringDeduplication

  classloader.resolve-order: parent-first

  # Enable flame graph for statistics
  rest.flamegraph.enabled: true
  rest.flamegraph.refresh-interval: 10 m
  rest.profiling.enabled: true
  restart-strategy.type: exponentialdelay

  metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
  metrics.reporter.prom.port: 9249

flink-jobmanager-conf.yaml: |
  jobmanager.memory.process.size: 1GB
  jobmanager.scheduler: adaptive

flink-taskmanager-conf.yaml: |
  taskmanager.numberOfTaskSlots: 4
  taskmanager.memory.process.size: 4GB

flink-job-conf.yaml: |
  job.autoscaler.enabled: false
  job.autoscaler.memory.tuning.enabled: false
  job.autoscaler.memory.tuning.overhead: 1
  job.autoscaler.scaling.enabled: false
  job.autoscaler.stabilization.interval: 1m
  job.autoscaler.metrics.window: 1m

flink-savepoints-and-checkpoints-conf.yaml: |
  execution.checkpointing.externalized-checkpoint-retention: RETAIN_ON_CANCELLATION
  execution.checkpointing.interval: 15 min
  execution.checkpointing.mode: EXACTLY_ONCE
  execution.checkpointing.timeout: 15 min
  execution.checkpointing.min-pause: 2 min
  execution.checkpointing.max-concurrent-checkpoints: 1
  execution.checkpointing.incremental: true
  execution.checkpointing.num-retained: 4
  state.backend: rocksdb
  state.backend.incremental: true