init-flink.sh: |
  #!/bin/sh

  # Usage: init-flink-config.sh

  source_dir="/tmp/real-flink-config"
  destination_dir="/tmp/ude/flink-conf"
  custom_configs_dir="/tmp/ude/flink-custom-configs"
  devops_tuning_configs_dir="/tmp/ude/flink-devops-tuning"

  config_path="${destination_dir}/flink-conf.yaml"
  config_path_tmp="${destination_dir}/flink-conf.yaml.tmp"

  cp -R "${source_dir}/." "${destination_dir}"

  truncate "${config_path}" -s 0
  cat $custom_configs_dir/*.yaml >> "${config_path_tmp}"
  cat $devops_tuning_configs_dir/*.yaml 2>/dev/null >> "${config_path_tmp}"

  result=$( (echo "cat <<UDEEOF"; cat "${config_path_tmp}"; echo UDEEOF) | sh )
  echo "${result}" > "${config_path_tmp}"

  cat "${source_dir}/flink-conf.yaml" | grep -v UDE_REMOVED >> "${config_path}" # real flink-conf.yaml
  cat "${config_path_tmp}" >> "${config_path}"

  unlink "${config_path_tmp}"
