flink-azure-fs-conf.yaml: |
  fs.azure.abfs.endpoint: ${AZURE_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net
  fs.azure.account.auth.type: OAuth
  fs.azure.account.oauth2.client.endpoint: https://login.microsoftonline.com/${AZURE_UPLOAD_CREDENTIAL_TENANT_ID}/oauth2/token
  fs.azure.account.oauth2.client.id: ${AZURE_UPLOAD_CREDENTIAL_CLIENT_ID}
  fs.azure.account.oauth2.client.secret: ${AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET}
  fs.azure.account.oauth.provider.type: org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider
  fs.azure.data.blocks.buffer: bytebuffer
  fs.defaultFS: abfss://flink@${AZURE_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net

flink-savepoints-and-checkpoints-conf.yaml: |
  execution.checkpointing.dir: abfss://flink@${AZURE_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net/${FLINK_APPLICATION_NAME}/checkpoints
  execution.checkpointing.externalized-checkpoint-retention: RETAIN_ON_CANCELLATION
  execution.checkpointing.savepoint-dir: abfss://flink@${AZURE_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net/${FLINK_APPLICATION_NAME}/savepoints

flink-jobmanager-conf.yaml: |
  # jobmanager.heap.size:
  jobmanager.memory.process.size: 1GB
  # jobmanager.memory.jvm-metaspace.size: 1GB

  # Enable Adaptive scheduler to play the in-place rescaling.
  jobmanager.scheduler: adaptive

flink-taskmanager-conf.yaml: |
  taskmanager.numberOfTaskSlots: 4
  taskmanager.memory.process.size: 4GB
  # taskmanager.heap.size:
  # taskmanager.memory.task.heap.size: 1.5GB

flink-job-conf.yaml: |
  job.autoscaler.enabled: false
  job.autoscaler.memory.tuning.enabled: false

  job.autoscaler.memory.tuning.overhead: 1

  # Enable autoscale and scaling
  job.autoscaler.scaling.enabled: false
  job.autoscaler.stabilization.interval : 1m
  job.autoscaler.metrics.window : 1m

flink-conf.yaml: |
  # jvm (do not enable debugging for pre-prod and prod)
  env.java.opts.all: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED

  classloader.resolve-order: parent-first

  # Enable flame graph for statistics !!!! Only for dev and pre-prod envs !!!!
  rest.flamegraph.enabled: false
  rest.profiling.enabled: false
  restart-strategy.type: exponentialdelay

  metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
  metrics.reporter.prom.port: 9249

flink-nodetolerations-conf.yaml: |
  kubernetes.taskmanager.tolerations: key:megatron.cariad.technology/node-context,operator:Equal,value:products,effect:NoSchedule;key:megatron.cariad.technology/node-type,operator:Equal,value:str-prd-us,effect:NoSchedule
  kubernetes.taskmanager.node-selector: megatron.cariad.technology/node-type: str-prd-us
