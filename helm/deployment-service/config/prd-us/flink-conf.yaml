flink-azure-fs-conf.yaml: |
  fs.azure.account.auth.type: OAuth
  fs.azure.block.size: ********
  fs.azure.data.blocks.buffer: bytebuffer
  fs.azure.write.max.concurrent.requests: 8
  fs.azure.write.max.requests.to.queue: 24
  fs.azure.write.request.size: 5242880

  fs.azure.abfs.endpoint.${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: ${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net
  fs.azure.account.auth.type.${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: OAuth
  fs.azure.account.oauth2.client.endpoint.${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: https://login.microsoftonline.com/${AZURE_UDE_STORAGE_CREDENTIAL_TENANT_ID}/oauth2/token
  fs.azure.account.oauth2.client.id.${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: ${AZURE_UDE_CREDENTIAL_CLIENT_ID}
  fs.azure.account.oauth2.client.secret.${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: ${AZURE_UDE_CREDENTIAL_CLIENT_SECRET}
  fs.azure.account.oauth.provider.type.${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider

  fs.azure.abfs.endpoint.${AZURE_UDE_COLD_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: ${AZURE_UDE_COLD_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net
  fs.azure.account.auth.type.${AZURE_UDE_COLD_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: OAuth
  fs.azure.account.oauth2.client.endpoint.${AZURE_UDE_COLD_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: https://login.microsoftonline.com/${AZURE_CONTRIBUTOR_CREDENTIAL_TENANT_ID}/oauth2/token
  fs.azure.account.oauth2.client.id.${AZURE_UDE_COLD_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: ${AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_ID}
  fs.azure.account.oauth2.client.secret.${AZURE_UDE_COLD_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: ${AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_SECRET}
  fs.azure.account.oauth.provider.type.${AZURE_UDE_COLD_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net: org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider

flink-savepoints-and-checkpoints-conf.yaml: |
  execution.checkpointing.dir: abfss://ude-str-prd-us-flink-storage@${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net/${FLINK_APPLICATION_NAME}/checkpoints
  execution.checkpointing.externalized-checkpoint-retention: RETAIN_ON_CANCELLATION
  execution.checkpointing.incremental: true
  execution.checkpointing.interval: 30s
  execution.checkpointing.max-concurrent-checkpoints: 2
  execution.checkpointing.min-pause: 5s
  execution.checkpointing.mode: AT_LEAST_ONCE
  execution.checkpointing.num-retained: 4
  execution.checkpointing.savepoint-dir: abfss://ude-str-prd-us-flink-storage@${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net/${FLINK_APPLICATION_NAME}/savepoints
  execution.checkpointing.timeout: 15min
  execution.checkpointing.tolerable-failed-checkpoints: 5
  state.backend.incremental: true
  state.backend.rocksdb.predefined-options: SPINNING_DISK_OPTIMIZED
  state.backend: rocksdb

flink-jobmanager-conf.yaml: |
  # jobmanager.heap.size:
  jobmanager.memory.process.size: 1GB
  # jobmanager.memory.jvm-metaspace.size: 1GB

  # Enable Adaptive scheduler to play the in-place rescaling.
  jobmanager.scheduler: adaptive

flink-taskmanager-conf.yaml: |
  # Waits for pending timers. The cold storage aggregation time ranges from 1 to 60 seconds
  # (configurable by the customer), which might result in a large file to transfer.
  # The Azure upload operation is NON-interruptible, so this timeout should be set high enough
  # to allow large file uploads to DFS to complete without forcing task termination. In ms.
  task.cancellation.timers.timeout: 60000

  taskmanager.numberOfTaskSlots: 4
  taskmanager.memory.process.size: 4GB
  # taskmanager.heap.size:
  # taskmanager.memory.task.heap.size: 1.5GB

flink-job-conf.yaml: |
  job.autoscaler.enabled: false
  job.autoscaler.memory.tuning.enabled: false

  job.autoscaler.memory.tuning.overhead: 1

  # Enable autoscale and scaling
  job.autoscaler.scaling.enabled: false
  job.autoscaler.stabilization.interval : 1m
  job.autoscaler.metrics.window : 1m

flink-conf.yaml: |
  # jvm (do not enable debugging for pre-prod and prod)
  env.java.opts.all: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED

  classloader.resolve-order: parent-first

  # Enable flame graph for statistics !!!! Only for dev and pre-prod envs !!!!
  rest.flamegraph.enabled: false
  rest.profiling.enabled: false
  restart-strategy.type: exponentialdelay

  metrics.reporter.prom.factory.class: org.apache.flink.metrics.prometheus.PrometheusReporterFactory
  metrics.reporter.prom.port: 9249

flink-nodetolerations-conf.yaml: |
  kubernetes.taskmanager.tolerations: key:megatron.cariad.technology/node-context,operator:Equal,value:products,effect:NoSchedule;key:megatron.cariad.technology/node-type,operator:Equal,value:str-prd-us,effect:NoSchedule
  kubernetes.taskmanager.node-selector: megatron.cariad.technology/node-type: str-prd-us
