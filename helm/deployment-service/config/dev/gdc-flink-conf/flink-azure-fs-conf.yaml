flink-azure-fs-conf.yaml: |
  fs.azure.abfs.endpoint: ${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net
  fs.azure.account.auth.type: OAuth
  fs.azure.account.oauth2.client.endpoint: https://login.microsoftonline.com/${AZURE_UDE_STORAGE_CREDENTIAL_TENANT_ID}/oauth2/token
  fs.azure.account.oauth2.client.id: ${AZURE_UDE_CREDENTIAL_CLIENT_ID}
  fs.azure.account.oauth2.client.secret: ${AZURE_UDE_CREDENTIAL_CLIENT_SECRET}
  fs.azure.account.oauth.provider.type: org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider
  fs.azure.data.blocks.buffer: bytebuffer
  fs.defaultFS: abfss://ude-str-dev-eu-flink-storage@${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net

flink-savepoints-and-checkpoints-conf.yaml: |
  execution.checkpointing.dir: abfss://ude-str-dev-eu-flink-storage@${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net/${FLINK_APPLICATION_NAME}/checkpoints
  execution.checkpointing.savepoint-dir: abfss://ude-str-dev-eu-flink-storage@${AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME}.dfs.core.windows.net/${FLINK_APPLICATION_NAME}/savepoints