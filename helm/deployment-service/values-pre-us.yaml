global:
  environment: pre-us
image:
  repository: crmegahubeastus2.azurecr.io/udestrus-pre/deployment-service
  tag: 0.0.9-RC6
  pullPolicy: Always
  commitSha: "e12f71ccdd"
envFrom:
  - configMapRef:
      name: deployment-service-store-and-explore-config
  - configMapRef:
      name: deployment-service-ude-flink-storage-config
env:
  - name: AZURE_LOG_LEVEL
    value: DEBUG
  - name: API_KAFKA_BOOTSTRAP_SERVERS
    value: pkc-5wkokg.eastus2.azure.confluent.cloud:9092
  - name: API_KAFKA_SCHEMA_REGISTRY_URL
    value: https://psrc-j5nvyw.eastus2.azure.confluent.cloud
  - name: API_KAFKA_INCOMING_TOPIC_COMMAND
    value: private.ude-str.commands.json
  - name: API_KAFKA_OUTGOING_TOPIC_STATUS
    value: private.ude-str.status.json
  - name: API_KAFKA_OUTGOING_TOPIC_COMMAND_DEAD_LETTER
    value: private.ude-str.commands-dead-letter.bytes
  # TODO make the images dynamic for the jobs.
  - name: NAMESPACE
    value: udestrus-pre
  - name: FLINK_GDC_JOB_IMAGE_URI
    value: crmegahubeastus2.azurecr.io/udestrus-pre/gdc-job:f120-0.2.1-RC1
  - name: FLINK_PASSTHROUGH_JOB_IMAGE_URI
    value: crmegahubeastus2.azurecr.io/udestrus-pre/passthrough-job:f120-0.2.1-RC1
  - name: FLINK_INIT_CONTAINER_IMAGE_URI
    value: crmegahubeastus2.azurecr.io/docker.io/library/busybox
  - name: DB_URL
    value: "*********************************************************************************************,ude-str-pre-us-postgres-replica2-in-mammal.postgres.database.azure.com:5432,ude-str-pre-us-postgres-replica3-in-mammal.postgres.database.azure.com:5432"
  - name: DB_USER_NAME
    value: "psqladmin"
  - name: CLAIMS_DB_NAME
    value: "management-api"
  - name: CLAIMS_TABLE_NAME
    value: "processing_claim"
  - name: KAFKA_VMS_SOURCE_TOPIC_NAME
    value: private.ude-str.vms-connector.flattened.json
  - name: VDC_DB_NAME
    value: "signals"
  - name: VDC_RULES_TABLE_NAME
    value: "normalization_rule"
  - name: API_CONFLUENT_IDENTITY_PROVIDER_ID
    value: op-qyWa
  - name: API_CONFLUENT_CLUSTER_ID
    value: lkc-zzw5o3
  - name: API_CONFLUENT_CLUSTER_API_URL
    value: https://pkc-5wkokg.eastus2.azure.confluent.cloud
  - name: API_CONFLUENT_CONNECTION_TENANT_ID
    value: c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c
  - name: API_CONFLUENT_USE_IDENTITY_POOL
    value: "true"
  - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
    valueFrom:
      secretKeyRef:
        name: deployment-service-ude-flink-storage-spn-secret
        key: azure_credential_client_secret
  - name: QUARKUS_LOG_LEVEL
    value: INFO
configMap:
  store-and-explore-config:
    AZURE_STORAGE_ACCOUNT_NAME: "studestoreexpmeus2seustl"
    AZURE_UPLOAD_BLOB_ENDPOINT: "https://studestoreexpmeus2seustl.blob.core.windows.net"
    AZURE_DOWNLOAD_BLOB_ENDPOINT: "https://studestoreexpmeus2sstre.blob.core.windows.net"
    # ude-str-pre-us-se-blob-writer 
    AZURE_UPLOAD_CREDENTIAL_CLIENT_ID: "2540319e-3971-4f53-a769-fc1cc67f5714"
    AZURE_UPLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-pre-us-deployment-svc-blob-data-owner
    AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_ID: "83caa128-27fc-4bde-81c0-7bb743a12472"
    AZURE_CONTRIBUTOR_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-pre-us-se-blob-reader
    AZURE_DOWNLOAD_CREDENTIAL_CLIENT_ID: "7a524b33-2129-4014-96a2-4300d2394b49"
    AZURE_DOWNLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
  ude-flink-storage-config:
    AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME: "udestrpreusflinkstorage"
    AZURE_UDE_STORAGE_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    AZURE_UDE_CREDENTIAL_CLIENT_ID: "e055304b-de35-4c0e-82a3-ba992f038974"
secretStore:
  - authType: "WorkloadIdentity"
    name: 'megatron'
    vaultUrl: "https://boston-ca3f5937.vault.azure.net/"
    serviceAccountName: "boston-ca3f5937-identity"
  - authType: "WorkloadIdentity"
    name: 'team-kv'
    vaultUrl: "https://ude-str-pre-us-kv.vault.azure.net/"
    serviceAccountName: "boston-ca3f5937-identity"
externalSecret:
  enabled: true
  # RefreshInterval is the amount of time before the values reading again from the SecretStore provider
  refreshInterval: "1m"
  files:
    kafka-credentials:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        kafka_sasl_username:
          remoteRef:
            key: ude-str-pre-us-ingest-api-kafka-service-account-public-id
        kafka_sasl_password:
          remoteRef:
            key: ude-str-pre-us-ingest-api-kafka-service-account-public-secret
    schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-pre-us-deployment-svc-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-pre-us-deployment-svc-schema-registry-service-account-secret
    confluent-cluster-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-pre-us-deployment-service-kafka-service-account-public-id
        password:
          remoteRef:
            key: ude-str-pre-us-deployment-service-kafka-service-account-public-secret
    confluent-cloud-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-pre-us-deployment-svc-cloud-api-service-account-id
        password:
          remoteRef:
            key: ude-str-pre-us-deployment-svc-cloud-api-service-account-secret
    ude-flink-storage-spn-secret:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: udestrus-pre-spn
    store-and-explore-upload-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250423T120211
    store-and-explore-download-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250423T120105
    store-and-explore-contributor-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250423T115825
    gdc-job-schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-pre-us-gdc-job-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-pre-us-gdc-job-schema-registry-service-account-secret
    gdc-job-kafka-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_kafka_api_key:
          remoteRef:
            key: ude-str-pre-us-gdc-job-kafka-service-account-public-id
        confluent_kafka_api_secret:
          remoteRef:
            key: ude-str-pre-us-gdc-job-kafka-service-account-public-secret
    db-password:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        postgresql_admin_password:
          remoteRef:
            key: ude-str-pre-us-psql-admin-password
    flink-basic-auth:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        auth:
          remoteRef:
            key: ude-str-pre-us-basicauth
ingress:
  enabled: false
service:
  annotations:
    external-dns.alpha.kubernetes.io/hostname: udestrus-pre-deployment-service.apps.mega.cariad.cloud
    service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: /health/ready
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
  type: LoadBalancer
  port: 80
  targetPort: 8080
