global:
  environment: int
image:
  repository: crmegahubwesteurope.azurecr.io/str-int/deployment-service
  tag: 0.1.1-RC10
  pullPolicy: Always
  commitSha: "b3dda901"
envFrom:
  - configMapRef:
      name: deployment-service-store-and-explore-config
  - configMapRef:
      name: deployment-service-ude-flink-storage-config
env:
  - name: AZURE_LOG_LEVEL
    value: DEBUG
  - name: API_KAFKA_BOOTSTRAP_SERVERS
    value: pkc-vr3yjz.westeurope.azure.confluent.cloud:9092
  - name: API_KAFKA_SCHEMA_REGISTRY_URL
    value: https://psrc-j39np.westeurope.azure.confluent.cloud
  - name: API_KAFKA_INCOMING_TOPIC_COMMAND
    value: private.ude-str.commands.json
  - name: API_KAFKA_OUTGOING_TOPIC_STATUS
    value: private.ude-str.status.json
  - name: API_KAFKA_OUTGOING_TOPIC_COMMAND_DEAD_LETTER
    value: private.ude-str.commands-dead-letter.bytes
  # TODO make the images dynamic for the jobs.
  - name: NAMESPACE
    value: str-int
  - name: FLINK_GDC_JOB_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/str-int/gdc-job:main
  - name: FLINK_PASSTHROUGH_JOB_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/str-int/passthrough-job:main
  - name: FLINK_INIT_CONTAINER_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/docker.io/library/busybox
  - name: DB_URL
    value: "***************************************************************************************************"
  - name: DB_USER_NAME
    value: "psqladmin"
  - name: CLAIMS_DB_NAME
    value: "management-api"
  - name: CLAIMS_TABLE_NAME
    value: "processing_claim"
  - name: KAFKA_VMS_SOURCE_TOPIC_NAME
    value: private.ude-str.vms-connector.flattened.json
  - name: VDC_DB_NAME
    value: "signals"
  - name: VDC_RULES_TABLE_NAME
    value: "normalization_rule"
  - name: API_CONFLUENT_IDENTITY_PROVIDER_ID
    value: op-j93O
  - name: API_CONFLUENT_CLUSTER_ID
    value: lkc-v9kmd5
  - name: API_CONFLUENT_CLUSTER_API_URL
    value: https://pkc-vr3yjz.westeurope.azure.confluent.cloud
  - name: API_CONFLUENT_CONNECTION_TENANT_ID
    value: c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c
  - name: API_CONFLUENT_USE_IDENTITY_POOL
    value: "true"
  - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
    valueFrom:
      secretKeyRef:
        name: deployment-service-ude-flink-storage-spn-secret
        key: azure_credential_client_secret
  - name: QUARKUS_LOG_LEVEL
    value: INFO
configMap:
  store-and-explore-config:
    AZURE_UPLOAD_STORAGE_ACCOUNT_NAME: "studestoreexpmeuwistrlm"
    AZURE_UPLOAD_BLOB_ENDPOINT: "https://studestoreexpmeuwistrlm.blob.core.windows.net"
    AZURE_DOWNLOAD_BLOB_ENDPOINT: "https://studestoreexpmeuwistra2.blob.core.windows.net"
    # ude-str-int-eu-se-blob-writer 
    AZURE_UPLOAD_CREDENTIAL_CLIENT_ID: "39f7f81d-e6e9-4a20-9827-a333d98fdc66"
    AZURE_UPLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-int-eu-deployment-svc-blob-data-owner
    AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_ID: "8362aa40-c82c-4171-9277-6223092c97dd"
    AZURE_CONTRIBUTOR_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-int-eu-se-blob-reader
    AZURE_DOWNLOAD_CREDENTIAL_CLIENT_ID: "4130797b-dad8-4b89-b882-2f430fd2da7e"
    AZURE_DOWNLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
  ude-flink-storage-config:
    AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME: "udestrinteuflinkstorage"
    AZURE_UDE_STORAGE_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    AZURE_UDE_CREDENTIAL_CLIENT_ID: "253d76fe-1405-4cf7-ae71-e7f65666142f"
secretStore:
  - authType: "WorkloadIdentity"
    name: "megatron"
    vaultUrl: "https://riga-d7008c25.vault.azure.net/"
    serviceAccountName: "riga-d7008c25-identity"
  - authType: "WorkloadIdentity"
    name: "team-kv"
    vaultUrl: "https://ude-str-int-eu-kv.vault.azure.net/"
    serviceAccountName: "riga-d7008c25-identity"
externalSecret:
  enabled: true
  # RefreshInterval is the amount of time before the values reading again from the SecretStore provider
  refreshInterval: "1m"
  files:
    kafka-credentials:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        kafka_sasl_username:
          remoteRef:
            key: ude-str-int-eu-ingest-api-kafka-service-account-public-id
        kafka_sasl_password:
          remoteRef:
            key: ude-str-int-eu-ingest-api-kafka-service-account-public-secret
    schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-int-eu-deployment-svc-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-int-eu-deployment-svc-schema-registry-service-account-secret
    confluent-cluster-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-int-eu-deployment-service-kafka-service-account-public-id
        password:
          remoteRef:
            key: ude-str-int-eu-deployment-service-kafka-service-account-public-secret
    confluent-cloud-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-int-eu-deployment-svc-cloud-api-service-account-id
        password:
          remoteRef:
            key: ude-str-int-eu-deployment-svc-cloud-api-service-account-secret
    ude-flink-storage-spn-secret:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: str-int-spn
    store-and-explore-upload-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250219T133355
    store-and-explore-download-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250219T133303
    store-and-explore-contributor-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250331T113205
    gdc-job-schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-int-eu-gdc-job-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-int-eu-gdc-job-schema-registry-service-account-secret
    gdc-job-kafka-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_kafka_api_key:
          remoteRef:
            key: ude-str-int-eu-gdc-job-kafka-service-account-public-id
        confluent_kafka_api_secret:
          remoteRef:
            key: ude-str-int-eu-gdc-job-kafka-service-account-public-secret
    db-password:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        postgresql_admin_password:
          remoteRef:
            key: ude-str-int-eu-psql-admin-password
    flink-basic-auth:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        auth:
          remoteRef:
            key: ude-str-int-eu-basicauth
ingress:
  enabled: false
service:
  annotations:
    external-dns.alpha.kubernetes.io/hostname: str-int-deployment-service.apps.mega.cariad.cloud
    service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: /health/ready
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
  type: LoadBalancer
  port: 80
  targetPort: 8080
nodeSelector:
  megatron.cariad.technology/node-type: str-int
tolerations:
  - key: "megatron.cariad.technology/node-context"
    operator: "Equal"
    value: "products"
    effect: "NoSchedule"
  - key: "megatron.cariad.technology/node-type"
    operator: "Equal"
    value: "str-int"
    effect: "NoSchedule"
