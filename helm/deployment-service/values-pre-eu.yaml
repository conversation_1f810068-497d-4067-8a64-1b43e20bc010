global:
  environment: pre-eu
image:
  repository: crmegahubwesteurope.azurecr.io/str-pre/deployment-service
  tag: 0.1.1-RC13
  pullPolicy: Always
  commitSha: "a50a5fc9"
envFrom:
  - configMapRef:
      name: deployment-service-store-and-explore-config
  - configMapRef:
      name: deployment-service-ude-flink-storage-config
env:
  - name: AZURE_LOG_LEVEL
    value: DEBUG
  - name: API_KAFKA_BOOTSTRAP_SERVERS
    value: pkc-1j8k53.westeurope.azure.confluent.cloud:9092
  - name: API_KAFKA_SCHEMA_REGISTRY_URL
    value: https://psrc-j39np.westeurope.azure.confluent.cloud
  - name: API_KAFKA_INCOMING_TOPIC_COMMAND
    value: private.ude-str.commands.json
  - name: API_KAFKA_OUTGOING_TOPIC_STATUS
    value: private.ude-str.status.json
  - name: API_KAFKA_OUTGOING_TOPIC_COMMAND_DEAD_LETTER
    value: private.ude-str.commands-dead-letter.bytes
  # TODO make the images dynamic for the jobs.
  - name: NAMESPACE
    value: str-pre
  - name: FLINK_GDC_JOB_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/str-pre/gdc-job:main
  - name: FLINK_PASSTHROUGH_JOB_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/str-pre/passthrough-job:main
  - name: FLINK_INIT_CONTAINER_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/docker.io/library/busybox
  - name: FLINK_COLD_STORAGE_BASE_PATH
    value: abfss://<EMAIL>
  - name: DB_URL
    value: "***********************************************************************************************"
  - name: DB_USER_NAME
    value: "psqladmin"
  - name: CLAIMS_DB_NAME
    value: "management-api"
  - name: CLAIMS_TABLE_NAME
    value: "processing_claim"
  - name: KAFKA_VMS_SOURCE_TOPIC_NAME
    value: private.ude-str.vms-connector.flattened.json
  - name: VDC_DB_NAME
    value: "signals"
  - name: VDC_RULES_TABLE_NAME
    value: "normalization_rule"
  - name: API_CONFLUENT_IDENTITY_PROVIDER_ID
    value: op-MBJB
  - name: API_CONFLUENT_CLUSTER_ID
    value: lkc-9oy7vm
  - name: API_CONFLUENT_CLUSTER_API_URL
    value: https://pkc-1j8k53.westeurope.azure.confluent.cloud
  - name: API_CONFLUENT_CONNECTION_TENANT_ID
    value: c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c
  - name: API_CONFLUENT_USE_IDENTITY_POOL
    value: "true"
  - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
    valueFrom:
      secretKeyRef:
        name: deployment-service-ude-flink-storage-spn-secret
        key: azure_credential_client_secret
  - name: QUARKUS_LOG_LEVEL
    value: INFO
configMap:
  store-and-explore-config:
    AZURE_UPLOAD_STORAGE_ACCOUNT_NAME: "studestoreexpmeuwsstrlm"
    AZURE_UPLOAD_BLOB_ENDPOINT: "https://studestoreexpmeuwsstrlm.blob.core.windows.net"
    AZURE_DOWNLOAD_BLOB_ENDPOINT: "https://studestoreexpmeuwsstra2.blob.core.windows.net"
    # ude-str-pre-eu-se-blob-writer 
    AZURE_UPLOAD_CREDENTIAL_CLIENT_ID: "809e04f2-0900-48ea-b61f-6e847bf16ad2"
    AZURE_UPLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-pre-eu-deployment-svc-blob-data-owner
    AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_ID: "f20158e8-dc82-4ba9-bc67-733bab8c1342"
    AZURE_CONTRIBUTOR_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-pre-eu-se-blob-reader
    AZURE_DOWNLOAD_CREDENTIAL_CLIENT_ID: "e7b5342a-6941-460b-9ac1-dd213c42e25d"
    AZURE_DOWNLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
  ude-flink-storage-config:
    AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME: "udestrpreeuflinkstorage"
    AZURE_UDE_STORAGE_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    AZURE_UDE_CREDENTIAL_CLIENT_ID: "c4159b15-271c-4aa5-9479-96e3db5b245b"
    AZURE_UDE_COLD_STORAGE_ACCOUNT_NAME: "studestoreexpmeuwseusc"
secretStore:
  - authType: "WorkloadIdentity"
    name: 'megatron'
    vaultUrl: "https://roja-2cc3971f.vault.azure.net/"
    serviceAccountName: "roja-2cc3971f-identity"
  - authType: "WorkloadIdentity"
    name: 'team-kv'
    vaultUrl: "https://ude-str-pre-eu-kv.vault.azure.net/"
    serviceAccountName: "roja-2cc3971f-identity"
externalSecret:
  enabled: true
  # RefreshInterval is the amount of time before the values reading again from the SecretStore provider
  refreshInterval: "1m"
  files:
    kafka-credentials:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        kafka_sasl_username:
          remoteRef:
            key: ude-str-pre-eu-ingest-api-kafka-service-account-public-id
        kafka_sasl_password:
          remoteRef:
            key: ude-str-pre-eu-ingest-api-kafka-service-account-public-secret
    schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-pre-eu-deployment-svc-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-pre-eu-deployment-svc-schema-registry-service-account-secret
    confluent-cluster-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-pre-eu-deployment-service-kafka-service-account-public-id
        password:
          remoteRef:
            key: ude-str-pre-eu-deployment-service-kafka-service-account-public-secret
    confluent-cloud-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-pre-eu-deployment-svc-cloud-api-service-account-id
        password:
          remoteRef:
            key: ude-str-pre-eu-deployment-svc-cloud-api-service-account-secret
    ude-flink-storage-spn-secret:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: str-pre-spn
    store-and-explore-upload-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250219T153502
    store-and-explore-download-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250219T153410
    store-and-explore-contributor-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250331T113313
    gdc-job-schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-pre-eu-gdc-job-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-pre-eu-gdc-job-schema-registry-service-account-secret
    gdc-job-kafka-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_kafka_api_key:
          remoteRef:
            key: ude-str-pre-eu-gdc-job-kafka-service-account-public-id
        confluent_kafka_api_secret:
          remoteRef:
            key: ude-str-pre-eu-gdc-job-kafka-service-account-public-secret
    db-password:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        postgresql_admin_password:
          remoteRef:
            key: ude-str-pre-eu-psql-admin-password
    flink-basic-auth:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        auth:
          remoteRef:
            key: ude-str-pre-eu-basicauth
ingress:
  enabled: false
service:
  annotations:
    external-dns.alpha.kubernetes.io/hostname: str-pre-deployment-service.apps.mega.cariad.cloud
    service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: /health/ready
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
  type: LoadBalancer
  port: 80
  targetPort: 8080
nodeSelector:
  megatron.cariad.technology/node-type: str-pre-eu
tolerations:
  - key: "megatron.cariad.technology/node-context"
    operator: "Equal"
    value: "products"
    effect: "NoSchedule"
  - key: "megatron.cariad.technology/node-type"
    operator: "Equal"
    value: "str-pre-eu"
    effect: "NoSchedule"
