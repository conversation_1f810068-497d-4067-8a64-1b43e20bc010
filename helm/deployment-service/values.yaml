replicaCount: 1

image:
  repository: crmegahubwesteurope.azurecr.io/str-dev/deployment-service
  tag: 0.0.1-SNAPSHOT
  pullPolicy: Always
  commitSha: ""

nameOverride: ""
fullnameOverride: ""

deploymentAnnotations:
  reloader.stakater.com/auto: "true"

podAnnotations:
  instrumentation.opentelemetry.io/inject-java: java-instrumentation
  sidecar.opentelemetry.io/inject: otel-sidecar

podLabels:
  azure.workload.identity/use: "true"

imagePullSecrets: []

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

podDisruptionBudgets:
  - enabled: true
    # minAvailable: 1
    maxUnavailable: 1
  - name: flink-gdc-job
    enabled: true
    maxUnavailable: 1
    selector:
      matchLabels:
        jobType: gdc-job
  - name: flink-passthrough-job
    enabled: true
    maxUnavailable: 1
    selector:
      matchLabels:
        jobType: passthrough-job

resources:
  limits:
    cpu: 130m
    memory: 1.2Gi
  requests:
    cpu: 100m
    memory: 1Gi

livenessProbe:
  httpGet:
    path: /q/health/live
    port: 8080
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 5

readinessProbe:
  httpGet:
    path: /q/health/ready
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

ingress:
  enabled: false

# Flink resource configuration
flink:
  jobManager:
    memory: "1Gi"
    cpu: 1
  taskManager:
    memory: "4Gi"
    cpu: 6
