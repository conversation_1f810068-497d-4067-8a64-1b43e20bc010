{{- range .Values.podDisruptionBudgets }}
  {{- if .enabled }}
---
{{- $apiVersion := "policy/v1beta1" }}
{{- if $.Capabilities.APIVersions.Has "policy/v1/PodDisruptionBudget" }}
{{- $apiVersion = "policy/v1" }}
{{- end }}
apiVersion: {{ $apiVersion }}
kind: PodDisruptionBudget
metadata:
{{- $name := .name }}
{{- if not $name }}
  {{- $name = include "deployment-service.fullname" $ }}
{{- end }}
  name: {{ $name }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "deployment-service.labels" $ | nindent 4 }}
spec:
{{- if .minAvailable }}
  minAvailable: {{ .minAvailable }}
{{- end }}
{{- if .maxUnavailable }}
  maxUnavailable: {{ .maxUnavailable }}
{{- end }}
  selector:
    matchLabels:
      {{- if .selector }}
        {{- toYaml .selector.matchLabels | nindent 6 }}
      {{- else }}
        {{- include "deployment-service.selectorLabels" $ | nindent 6 }}
      {{- end }}
  {{- end }}
{{- end }}
