apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "deployment-service.fullname" $ }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "deployment-service.fullname" $ }}
  labels:
    {{- include "deployment-service.labels" $ | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - pods
  - configmaps
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - flink.apache.org
  resources:
  - flinkdeployments
  - flinksessionjobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - get
  - list
  - update
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "deployment-service.fullname" $ }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "deployment-service.fullname" $ }}
subjects:
- kind: ServiceAccount
  name: {{ include "deployment-service.fullname" $ }}
  namespace: {{ .Release.Namespace }}
