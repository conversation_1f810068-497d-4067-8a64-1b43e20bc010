{{- range $nameSuffix, $data := .Values.externalSecret.files }}
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: {{ include "deployment-service.fullname" $ }}-{{ $nameSuffix }}
  labels:
  {{- include "deployment-service.labels" $ | nindent 4 }}
spec:
  refreshInterval: {{ $.Values.externalSecret.refreshInterval }}
{{- if and (not $data.data) (not $data.dataFrom) }}
{{- fail "Data or datafrom not specified for secret {{ template 'application.name' $ }}-{{ $nameSuffix }} " }}
{{- end }}
{{- if $data.data }}
  data:
{{- range $secretKey, $remoteRef := $data.data}}
    - secretKey: {{ $secretKey }}
{{ toYaml $remoteRef | indent 6}}
{{- end }}
{{- end }}
{{- if $data.dataFrom }}
  dataFrom:
   - extract:
{{ toYaml $data.dataFrom | indent 6  }}
{{- end }}
  {{- if $data.secretStore }}
  secretStoreRef:
    name: {{ $data.secretStore.name }}
    kind: {{ $data.secretStore.kind | default "SecretStore" }}
  {{- else }}
  secretStoreRef:
    name: {{ include "deployment-service.fullname" $ }}
    kind: "SecretStore"
  {{- end}}
  target:
    name: {{ template "deployment-service.fullname" $ }}-{{ $nameSuffix }}
    template:
      type: {{ $data.type | default "Opaque" }}
{{- if or $data.annotations $data.labels}}
      metadata:
{{- if $data.annotations }}
        annotations: 
{{ toYaml $data.annotations | indent 10 }}
{{- end }}
{{- if $data.labels }}
        labels: 
{{ toYaml $data.labels | indent 10 }}
{{- end }}
{{- end }}
{{- end }}
