{{/* 
  ConfigMaps Template: Handles both shared and environment-specific ConfigMaps 
*/}}

{{/* Shared ConfigMaps from files in the shared config directory */}}
{{- $root := . -}}
{{- $files := .Files.Glob "config/shared/*" }}
{{- if $files }}
{{- range $path, $content := $files }}
{{- $filename := base $path }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "deployment-service.fullname" $root }}-{{ $filename | replace "." "-" }}
  labels:
    {{- include "deployment-service.labels" $root | nindent 4 }}
data:
  {{ $content | toString | nindent 2 | trim }}
{{- end }}
{{- end }}

{{/* Environment-specific ConfigMaps from files in env-specific directories */}}
{{- $env := .Values.global.environment | default "dev" -}}
{{- $envFiles := .Files.Glob (printf "config/%s/*" $env) }}
{{- if $envFiles }}
{{- range $path, $content := $envFiles }}
{{- $filename := base $path }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "deployment-service.fullname" $root }}-{{ $filename | replace "." "-" }}
  labels:
    {{- include "deployment-service.labels" $root | nindent 4 }}
data:
  {{ $content | toString | nindent 2 | trim }}
{{- end }}
{{- end }}

{{/* ConfigMaps defined in values */}}
{{- if .Values.configMap }}
{{- range $nameSuffix, $data := .Values.configMap }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "deployment-service.fullname" $ }}-{{ $nameSuffix }}
  labels:
    {{- include "deployment-service.labels" $ | nindent 4 }}
data:
  {{- toYaml $data | nindent 2 }}
{{- end }}
{{- end }}
