apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "deployment-service.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ include "deployment-service.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.image.commitSha }}
    app.kubernetes.io/commit-sha: {{ .Values.image.commitSha | quote }}
    {{- end}}
  {{- with .Values.deploymentAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "deployment-service.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "deployment-service.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ include "deployment-service.fullname" . }}
      {{- if .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml .Values.nodeSelector | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations:
        {{- toYaml .Values.tolerations | nindent 8 }}
      {{- end }}
      containers:
        - name: deployment-service
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          {{- if .Values.envFrom }}
          envFrom:
            {{- with .Values.envFrom -}}
            {{ toYaml . | nindent 12 }}
            {{- end }}
          {{- end }}
          env: 
          - name: API_KAFKA_SASL_USERNAME
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-kafka-credentials
                key: kafka_sasl_username
          - name: API_KAFKA_SASL_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-kafka-credentials
                key: kafka_sasl_password
          - name: API_SCHEMA_REGISTRY_USERNAME
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-schema-registry-secret
                key: confluent_schema_registry_key
          - name: API_SCHEMA_REGISTRY_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-schema-registry-secret
                key: confluent_schema_registry_secret
          - name: API_CONFLUENT_CLUSTER_KEY
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-confluent-cluster-credentials
                key: username
          - name: API_CONFLUENT_CLUSTER_SECRET
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-confluent-cluster-credentials
                key: password
          - name: API_CONFLUENT_CLOUD_KEY
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-confluent-cloud-credentials
                key: username
          - name: API_CONFLUENT_CLOUD_SECRET
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-confluent-cloud-credentials
                key: password
          - name: AZURE_UPLOAD_CREDENTIAL_CLIENT_SECRET
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-store-and-explore-upload-spn-secret
                key: azure_credential_client_secret
          - name: AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_SECRET
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-store-and-explore-contributor-spn-secret
                key: azure_credential_client_secret
          - name: AZURE_DOWNLOAD_CREDENTIAL_CLIENT_SECRET
            valueFrom:
              secretKeyRef:
                name: {{ include "deployment-service.fullname" . }}-store-and-explore-download-spn-secret
                key: azure_credential_client_secret

          {{- with .Values.env }}
          {{ toYaml . | nindent 10 }}
          {{- end }}
          livenessProbe:
            httpGet:
              path: {{ .Values.livenessProbe.httpGet.path }}
              port: {{ .Values.livenessProbe.httpGet.port }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          readinessProbe:
            httpGet:
              path: {{ .Values.readinessProbe.httpGet.path }}
              port: {{ .Values.readinessProbe.httpGet.port }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          resources:
            limits:
              cpu: {{ .Values.resources.limits.cpu }}
              memory: {{ .Values.resources.limits.memory }}
            requests:
              cpu: {{ .Values.resources.requests.cpu }}
              memory: {{ .Values.resources.requests.memory }}
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            runAsNonRoot: true
            runAsUser: 1000
 {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
