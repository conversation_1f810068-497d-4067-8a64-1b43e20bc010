global:
  environment: dev
image:
  repository: crmegahubwesteurope.azurecr.io/str-dev/deployment-service
  tag: 0.1.1-SNAPSHOT
  pullPolicy: Always
  commitSha: "dd5cd16a"
envFrom:
  - configMapRef:
      name: deployment-service-store-and-explore-config
  - configMapRef:
      name: deployment-service-ude-flink-storage-config
env:
  - name: AZURE_LOG_LEVEL
    value: DEBUG
  - name: API_KAFKA_BOOTSTRAP_SERVERS
    value: pkc-09081p.westeurope.azure.confluent.cloud:9092
  - name: API_KAFKA_SCHEMA_REGISTRY_URL
    value: https://psrc-v6px5.westeurope.azure.confluent.cloud
  - name: API_KAFKA_INCOMING_TOPIC_COMMAND
    value: private.ude-str.commands.json
  - name: API_KAFKA_OUTGOING_TOPIC_STATUS
    value: private.ude-str.status.json
  - name: API_KAFKA_OUTGOING_TOPIC_COMMAND_DEAD_LETTER
    value: private.ude-str.commands-dead-letter.bytes
    # TODO make the images dynamic for the jobs.
  - name: NAMESPACE
    value: str-dev
  - name: FLINK_GDC_JOB_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/str-dev/gdc-job:main
  - name: FLINK_PASSTHROUGH_JOB_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/str-dev/passthrough-job:main
  - name: FLINK_INIT_CONTAINER_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/docker.io/library/busybox
  - name: DB_URL
    value: "*****************************************************************************************"
  - name: DB_USER_NAME
    value: "psqladmin"
  - name: CLAIMS_DB_NAME
    value: "management-api"
  - name: CLAIMS_TABLE_NAME
    value: "processing_claim"
  - name: KAFKA_VMS_SOURCE_TOPIC_NAME
    value: private.ude-str.vms-connector.flattened.json
  - name: VDC_DB_NAME
    value: "signals"
  - name: VDC_RULES_TABLE_NAME
    value: "normalization_rule"
  - name: API_CONFLUENT_IDENTITY_PROVIDER_ID
    value: op-j93O
  - name: API_CONFLUENT_CLUSTER_ID
    value: lkc-9od2y7
  - name: API_CONFLUENT_CLUSTER_API_URL
    value: https://pkc-09081p.westeurope.azure.confluent.cloud
  - name: API_CONFLUENT_CONNECTION_TENANT_ID
    value: c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c
  - name: API_CONFLUENT_USE_IDENTITY_POOL
    value: "true"
  - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
    valueFrom:
      secretKeyRef:
        name: deployment-service-ude-flink-storage-spn-secret
        key: azure_credential_client_secret
  - name: QUARKUS_LOG_LEVEL
    value: DEBUG
  # Flink resource configuration
  - name: FLINK_DEPLOYMENT_CONFIG_GDC_JOB_MANAGER_MEMORY
    value: "{{ .Values.flink.gdc.jobManager.memory }}"
  - name: FLINK_DEPLOYMENT_CONFIG_GDC_JOB_MANAGER_CPU
    value: "{{ .Values.flink.gdc.jobManager.cpu }}"
  - name: FLINK_DEPLOYMENT_CONFIG_GDC_TASK_MANAGER_MEMORY
    value: "{{ .Values.flink.gdc.taskManager.memory }}"
  - name: FLINK_DEPLOYMENT_CONFIG_GDC_TASK_MANAGER_CPU
    value: "{{ .Values.flink.gdc.taskManager.cpu }}"
  - name: FLINK_DEPLOYMENT_CONFIG_PASSTHROUGH_JOB_MANAGER_MEMORY
    value: "{{ .Values.flink.passthrough.jobManager.memory }}"
  - name: FLINK_DEPLOYMENT_CONFIG_PASSTHROUGH_JOB_MANAGER_CPU
    value: "{{ .Values.flink.passthrough.jobManager.cpu }}"
  - name: FLINK_DEPLOYMENT_CONFIG_PASSTHROUGH_TASK_MANAGER_MEMORY
    value: "{{ .Values.flink.passthrough.taskManager.memory }}"
  - name: FLINK_DEPLOYMENT_CONFIG_PASSTHROUGH_TASK_MANAGER_CPU
    value: "{{ .Values.flink.passthrough.taskManager.cpu }}"
configMap:
  store-and-explore-config:
    AZURE_STORAGE_ACCOUNT_NAME: "studestoreexpmeuwdstrlm"
    AZURE_UPLOAD_BLOB_ENDPOINT: "https://studestoreexpmeuwdstrlm.blob.core.windows.net"
    AZURE_DOWNLOAD_BLOB_ENDPOINT: "https://studestoreexpmeuwdstra2.blob.core.windows.net"
    # ude-str-dev-eu-se-blob-writer 
    AZURE_UPLOAD_CREDENTIAL_CLIENT_ID: "960273be-cc08-4eb3-8fb3-61213b7e912a"
    AZURE_UPLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-dev-eu-deployment-svc-blob-data-owner
    AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_ID: "373e46bf-75f2-4761-84d0-0efae84cb9b4"
    AZURE_CONTRIBUTOR_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-dev-eu-se-blob-reader
    AZURE_DOWNLOAD_CREDENTIAL_CLIENT_ID: "ef41fd27-ad32-4db2-a2c3-3dbe1be9ae36"
    AZURE_DOWNLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
  ude-flink-storage-config:
    AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME: "udestrdeveuflinkstorage"
    AZURE_UDE_STORAGE_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    AZURE_UDE_CREDENTIAL_CLIENT_ID: "fdf414b7-3d83-4f0f-a78c-6f2fed1c687e"
secretStore:
  - authType: "WorkloadIdentity"
    name: "megatron"
    vaultUrl: "https://riga-9ab4ff3b.vault.azure.net/"
    serviceAccountName: "riga-9ab4ff3b-identity"
  - authType: "WorkloadIdentity"
    name: "team-kv"
    vaultUrl: "https://ude-str-dev-eu-kv.vault.azure.net/"
    serviceAccountName: "riga-9ab4ff3b-identity"
externalSecret:
  enabled: true
  # RefreshInterval is the amount of time before the values reading again from the SecretStore provider
  refreshInterval: "1m"
  files:
    kafka-credentials:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        kafka_sasl_username:
          remoteRef:
            key: ude-str-dev-eu-deployment-service-kafka-service-account-public-id
        kafka_sasl_password:
          remoteRef:
            key: ude-str-dev-eu-deployment-service-kafka-service-account-public-secret
    schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-dev-eu-deployment-svc-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-dev-eu-deployment-svc-schema-registry-service-account-secret
    confluent-cluster-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-dev-eu-deployment-service-kafka-service-account-public-id
        password:
          remoteRef:
            key: ude-str-dev-eu-deployment-service-kafka-service-account-public-secret
    confluent-cloud-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-dev-eu-deployment-svc-cloud-api-service-account-id
        password:
          remoteRef:
            key: ude-str-dev-eu-deployment-svc-cloud-api-service-account-secret
    ude-flink-storage-spn-secret:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: str-dev-spn
    store-and-explore-upload-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250219T133143
    store-and-explore-download-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250219T133036
    store-and-explore-contributor-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250331T113001
    gdc-job-schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-dev-eu-gdc-job-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-dev-eu-gdc-job-schema-registry-service-account-secret
    gdc-job-kafka-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_kafka_api_key:
          remoteRef:
            key: ude-str-dev-eu-gdc-job-kafka-service-account-public-id
        confluent_kafka_api_secret:
          remoteRef:
            key: ude-str-dev-eu-gdc-job-kafka-service-account-public-secret
    db-password:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        postgresql_admin_password:
          remoteRef:
            key: ude-str-dev-eu-psql-admin-password
    flink-basic-auth:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        auth:
          remoteRef:
            key: ude-str-dev-eu-basicauth
ingress:
  enabled: false
service:
  annotations:
    external-dns.alpha.kubernetes.io/hostname: str-dev-deployment-service.apps.mega.cariad.cloud
    service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: /health/ready
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
  type: LoadBalancer
  port: 80
  targetPort: 8080

# Flink resource configuration for development
flink:
  gdc:
    jobManager:
      memory: "1Gi"  # Default for development
      cpu: 1
    taskManager:
      memory: "2Gi"  # Reduced for development
      cpu: 2
  passthrough:
    jobManager:
      memory: "1Gi"  # Default for development
      cpu: 1
    taskManager:
      memory: "2Gi"  # Reduced for development
      cpu: 2
