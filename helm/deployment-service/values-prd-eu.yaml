global:
  environment: prd-eu
image:
  repository: crmegahubwesteurope.azurecr.io/str-prd/deployment-service
  tag: 0.0.9
  pullPolicy: Always
  commitSha: "fe854b4"
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80
envFrom:
  - configMapRef:
      name: deployment-service-store-and-explore-config
env:
  - name: AZURE_LOG_LEVEL
    value: DEBUG
  - name: API_KAFKA_BOOTSTRAP_SERVERS
    value: pkc-xr5d1z.westeurope.azure.confluent.cloud:9092
  - name: API_KAFKA_SCHEMA_REGISTRY_URL
    value: https://psrc-4nx36.westeurope.azure.confluent.cloud
  - name: API_KAFKA_INCOMING_TOPIC_COMMAND
    value: private.ude-str.commands.json
  - name: API_KAFKA_OUTGOING_TOPIC_STATUS
    value: private.ude-str.status.json
  - name: API_KAFKA_OUTGOING_TOPIC_COMMAND_DEAD_LETTER
    value: private.ude-str.commands-dead-letter.bytes
  - name: NAMESPACE
    value: str-prd
  - name: FLINK_GDC_JOB_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/str-prd/gdc-job:f120-0.1.2
  - name: FLINK_PASSTHROUGH_JOB_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/str-prd/passthrough-job:main
  - name: FLINK_INIT_CONTAINER_IMAGE_URI
    value: crmegahubwesteurope.azurecr.io/docker.io/library/busybox
  - name: DB_URL
    value: "***********************************************************************************************,ude-str-prd-eu-postgres-replica2-huge-marlin.postgres.database.azure.com:5432,ude-str-prd-eu-postgres-replica4-huge-marlin.postgres.database.azure.com:5432,ude-str-prd-eu-postgres-replica5-huge-marlin.postgres.database.azure.com:5432"
  - name: DB_USER_NAME
    value: "psqladmin"
  - name: CLAIMS_DB_NAME
    value: "management-api"
  - name: CLAIMS_TABLE_NAME
    value: "processing_claim"
  - name: KAFKA_VMS_SOURCE_TOPIC_NAME
    value: private.ude-str.vms-connector.flattened.json
  - name: VDC_DB_NAME
    value: "signals"
  - name: VDC_RULES_TABLE_NAME
    value: "normalization_rule"
  - name: API_CONFLUENT_IDENTITY_PROVIDER_ID
    value: op-d3NY
  - name: API_CONFLUENT_CLUSTER_ID
    value: lkc-qnk36p
  - name: API_CONFLUENT_CLUSTER_API_URL
    value: https://pkc-xr5d1z.westeurope.azure.confluent.cloud
  - name: API_CONFLUENT_CONNECTION_TENANT_ID
    value: c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c
  - name: AZURE_UDE_CREDENTIAL_CLIENT_SECRET
    valueFrom:
      secretKeyRef:
        name: deployment-service-ude-flink-storage-spn-secret
        key: azure_credential_client_secret
  - name: QUARKUS_LOG_LEVEL
    value: INFO # should be WARN in the future once prod is stable
  # Flink resource configuration
  - name: GDC_JOB_MANAGER_MEMORY
    value: "{{ .Values.flink.gdc.jobManager.memory }}"
  - name: FLINK_DEPLOYMENT_CONFIG_GDC_JOB_MANAGER_CPU
    value: "{{ .Values.flink.gdc.jobManager.cpu }}"
  - name: FLINK_DEPLOYMENT_CONFIG_GDC_TASK_MANAGER_MEMORY
    value: "{{ .Values.flink.gdc.taskManager.memory }}"
  - name: FLINK_DEPLOYMENT_CONFIG_GDC_TASK_MANAGER_CPU
    value: "{{ .Values.flink.gdc.taskManager.cpu }}"
  - name: FLINK_DEPLOYMENT_CONFIG_PASSTHROUGH_JOB_MANAGER_MEMORY
    value: "{{ .Values.flink.passthrough.jobManager.memory }}"
  - name: FLINK_DEPLOYMENT_CONFIG_PASSTHROUGH_JOB_MANAGER_CPU
    value: "{{ .Values.flink.passthrough.jobManager.cpu }}"
  - name: FLINK_DEPLOYMENT_CONFIG_PASSTHROUGH_TASK_MANAGER_MEMORY
    value: "{{ .Values.flink.passthrough.taskManager.memory }}"
  - name: FLINK_DEPLOYMENT_CONFIG_PASSTHROUGH_TASK_MANAGER_CPU
    value: "{{ .Values.flink.passthrough.taskManager.cpu }}"
configMap:
  store-and-explore-config:
    AZURE_STORAGE_ACCOUNT_NAME: "studestoreexpmeuwpstrlm"
    AZURE_UPLOAD_BLOB_ENDPOINT: "https://studestoreexpmeuwpstrlm.blob.core.windows.net"
    AZURE_DOWNLOAD_BLOB_ENDPOINT: "https://studestoreexpmeuwpsta2.blob.core.windows.net"
    # ude-str-prd-eu-se-blob-writer 
    AZURE_UPLOAD_CREDENTIAL_CLIENT_ID: "7ed3247c-5b8b-4531-bb2b-0428d0f121b4"
    AZURE_UPLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-prd-eu-deployment-svc-blob-data-owner
    AZURE_CONTRIBUTOR_CREDENTIAL_CLIENT_ID: "97a3eead-28c9-4bb3-bab3-73341a80869a"
    AZURE_CONTRIBUTOR_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    # ude-str-prd-eu-se-blob-reader
    AZURE_DOWNLOAD_CREDENTIAL_CLIENT_ID: "c14cbf52-acf3-4921-bd97-34e5e315c8da"
    AZURE_DOWNLOAD_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
  ude-flink-storage-config:
    AZURE_UDE_FLINK_STORAGE_ACCOUNT_NAME: "udestrprdeuflinkstorage"
    AZURE_UDE_STORAGE_CREDENTIAL_TENANT_ID: "c5f6f6e0-4c59-4aa1-bcd7-033f5f211b1c"
    AZURE_UDE_CREDENTIAL_CLIENT_ID: "06bb2df9-e211-44f9-849e-ab591f680712"
secretStore:
  - authType: "WorkloadIdentity"
    name: 'megatron'
    vaultUrl: "https://roja-bb1c1eee.vault.azure.net/"
    serviceAccountName: "roja-bb1c1eee-identity"
  - authType: "WorkloadIdentity"
    name: 'team-kv'
    vaultUrl: "https://ude-str-prd-eu-kv.vault.azure.net/"
    serviceAccountName: "roja-bb1c1eee-identity"
externalSecret:
  enabled: true
  refreshInterval: "1m"
  files:
    kafka-credentials:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        kafka_sasl_username:
          remoteRef:
            key: ude-str-prd-eu-ingest-api-kafka-service-account-public-id
        kafka_sasl_password:
          remoteRef:
            key: ude-str-prd-eu-ingest-api-kafka-service-account-public-secret
    schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-prd-eu-deployment-svc-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-prd-eu-deployment-svc-schema-registry-service-account-secret
    gdc-job-schema-registry-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_schema_registry_key:
          remoteRef:
            key: ude-str-prd-eu-gdc-job-schema-registry-service-account-id
        confluent_schema_registry_secret:
          remoteRef:
            key: ude-str-prd-eu-gdc-job-schema-registry-service-account-secret
    confluent-cluster-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-prd-eu-deployment-service-kafka-service-account-public-id
        password:
          remoteRef:
            key: ude-str-prd-eu-deployment-service-kafka-service-account-public-secret
    confluent-cloud-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        username:
          remoteRef:
            key: ude-str-prd-eu-deployment-svc-cloud-api-service-account-id
        password:
          remoteRef:
            key: ude-str-prd-eu-deployment-svc-cloud-api-service-account-secret
    ude-flink-storage-spn-secret:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: str-prd-spn
    store-and-explore-upload-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250403T113753
    store-and-explore-download-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250403T113633
    store-and-explore-contributor-spn-secret:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        azure_credential_client_secret:
          remoteRef:
            key: azuf-krs-secret-20250403T113312
    gdc-job-kafka-credentials:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        confluent_kafka_api_key:
          remoteRef:
            key: ude-str-prd-eu-gdc-job-kafka-service-account-public-id
        confluent_kafka_api_secret:
          remoteRef:
            key: ude-str-prd-eu-gdc-job-kafka-service-account-public-secret
    db-password:
      secretStore:
        name: 'deployment-service-team-kv'
      data:
        postgresql_admin_password:
          remoteRef:
            key: ude-str-prd-eu-psql-admin-password
    flink-basic-auth:
      secretStore:
        name: 'deployment-service-megatron'
      data:
        auth:
          remoteRef:
            key: ude-str-prd-eu-basicauth
ingress:
  enabled: false
service:
  annotations:
    external-dns.alpha.kubernetes.io/hostname: str-prd-deployment-service.apps.mega.cariad.cloud
    service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: /health/ready
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
  type: LoadBalancer
  port: 80
  targetPort: 8080
nodeSelector:
  megatron.cariad.technology/node-type: str-prod-eu
tolerations:
  - key: "megatron.cariad.technology/node-context"
    operator: "Equal"
    value: "products"
    effect: "NoSchedule"
  - key: "megatron.cariad.technology/node-type"
    operator: "Equal"
    value: "str-prod-eu"
    effect: "NoSchedule"

# Flink resource configuration for production EU
flink:
  gdc:
    jobManager:
      memory: "2Gi"  # Increased for production
      cpu: 2
    taskManager:
      memory: "8Gi"  # Increased for production
      cpu: 8
  passthrough:
    jobManager:
      memory: "2Gi"  # Increased for production
      cpu: 2
    taskManager:
      memory: "6Gi"  # Slightly less than GDC for passthrough
      cpu: 6
