name: PreProd - Deploy Helm Chart
on:
  workflow_dispatch:
    inputs:
      environment:
        description: "PreProd Environment to deploy to"
        type: string
        required: true

permissions:
  contents: write
  packages: read
  id-token: write
  actions: read
  pull-requests: write

jobs:
  get-initial-version-pre-eu:
    if: ${{ inputs.environment == 'pre-eu' }}
    name: Get Initial Version
    environment: pre-eu
    runs-on: [self-hosted, MegatronProducts, westeurope]
    outputs:
      initial_version: ${{ steps.get-version.outputs.version }}
    steps:
      - name: Azure Login
        uses: azure/login@v1
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      
      - name: Install kubectl
        run: |
          curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
          chmod +x ./kubectl
          sudo mv ./kubectl /usr/local/bin/kubectl
      
      - name: Install kubelogin
        run: |
          curl -sSL -o kubelogin.zip https://github.com/Azure/kubelogin/releases/latest/download/kubelogin-linux-amd64.zip
          unzip kubelogin.zip
          install -m 555 bin/linux_amd64/kubelogin /usr/local/bin/kubelogin
          rm -rf bin kubelogin.zip

      - name: Set kubernetes context
        run: |
          az aks get-credentials -g mega-roja -n mega-roja-kubernetes
          kubelogin convert-kubeconfig -l azurecli
          kubectl config set-context --current --namespace=str-pre

      - name: Get initial version
        id: get-version
        run: |
          VERSION=$(kubectl get application.argoproj.io deployment-service -n str-pre -o jsonpath='{.status.sync.revision}')
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Initial version: $VERSION" 

  deploy-pre-eu:
    if: ${{ inputs.environment == 'pre-eu' }}
    name: "pre-eu: Deploy & Push Helm Chart"
    uses: cariad-ude/ude-str-github-actions/.github/workflows/dev-quarkus-build-deploy-rc.yaml@main
    secrets: inherit
    with:
      environment: pre-eu
      product: str-pre
      container-repo: deployment-service
      version: ${{ github.ref_name }}
      keyvault-name: roja-2cc3971f
      resource-group: str-pre
      runner: '["self-hosted", "MegatronProducts", "westeurope"]'

  wait-for-argocd-sync-pre-eu:
    name: Wait for ArgoCD Sync
    runs-on: [self-hosted, MegatronProducts, westeurope]
    needs: [get-initial-version-pre-eu, deploy-pre-eu]
    environment: pre-eu
    steps:
      - name: Azure Login
        uses: azure/login@v1
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      
      - name: Install kubectl
        run: |
          curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
          chmod +x ./kubectl
          sudo mv ./kubectl /usr/local/bin/kubectl
      
      - name: Install kubelogin
        run: |
          curl -sSL -o kubelogin.zip https://github.com/Azure/kubelogin/releases/latest/download/kubelogin-linux-amd64.zip
          unzip kubelogin.zip
          install -m 555 bin/linux_amd64/kubelogin /usr/local/bin/kubelogin
          rm -rf bin kubelogin.zip

      - name: Set kubernetes context
        run: |
          az aks get-credentials -g mega-roja -n mega-roja-kubernetes
          kubelogin convert-kubeconfig -l azurecli
          kubectl config set-context --current --namespace=str-pre

      - name: Wait for ArgoCD sync with new version
        run: |
          INITIAL_VERSION="${{ needs.get-initial-version.outputs.initial_version }}"
          echo "Initial version: $INITIAL_VERSION"
          
          echo "Waiting for ArgoCD sync to complete with new version..."
          for i in {1..90}; do
            # Check if the application is synced and healthy
            SYNC_STATUS=$(kubectl get application.argoproj.io deployment-service -n str-pre -o jsonpath='{.status.sync.status}')
            HEALTH_STATUS=$(kubectl get application.argoproj.io deployment-service -n str-pre -o jsonpath='{.status.health.status}')
            CURRENT_VERSION=$(kubectl get application.argoproj.io deployment-service -n str-pre -o jsonpath='{.status.sync.revision}')
            OPERATION_PHASE=$(kubectl get application.argoproj.io deployment-service -n str-pre -o jsonpath='{.status.operationState.phase}')
            
            echo "Current version: $CURRENT_VERSION"
            echo "Sync status: $SYNC_STATUS, Health status: $HEALTH_STATUS, Operation phase: $OPERATION_PHASE (attempt $i/90)"
            
            # If the version has changed and the application is synced and healthy, and operation is complete
            if [ "$SYNC_STATUS" = "Synced" ] && [ "$HEALTH_STATUS" = "Healthy" ] && [ "$CURRENT_VERSION" != "$INITIAL_VERSION" ] && [ "$OPERATION_PHASE" = "Succeeded" ]; then
              echo "Application is synced and healthy with the new version"
              echo "New version: $CURRENT_VERSION"
              exit 0
            fi
            
            # If we see a version change but operation is not complete, wait for it
            if [ "$CURRENT_VERSION" != "$INITIAL_VERSION" ] && [ -z "$OPERATION_PHASE" ]; then
              echo "Version changed but operation not started yet, waiting..."
            fi
            
            sleep 10
          done
          
          echo "Application did not sync with new version within the timeout period (15 minutes)"
          echo "Last seen version: $CURRENT_VERSION"
          echo "Last sync status: $SYNC_STATUS"
          echo "Last health status: $HEALTH_STATUS"
          echo "Last operation phase: $OPERATION_PHASE"
          exit 1

  e2e-tests-pre-eu:
    name: Trigger E2E Tests
    runs-on: [self-hosted, MegatronProducts, westeurope]
    needs: wait-for-argocd-sync-pre-eu
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}
          owner: ${{ github.repository_owner }}

      - name: Repository Dispatch
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ steps.app-token.outputs.token }}
          repository: cariad-ude/ude-str-tests
          event-type: run-e2e-tests
          client-payload: '{"environment": "preprod"}'

  get-initial-version-pre-us:
    if: ${{ inputs.environment == 'pre-us' }}
    name: Get Initial Version
    runs-on: [self-hosted, MegatronProducts, eastus2]
    environment: pre-us
    outputs:
      initial_version: ${{ steps.get-version.outputs.version }}
    steps:
      - name: Azure Login
        uses: azure/login@v1
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      
      - name: Install kubectl
        run: |
          curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
          chmod +x ./kubectl
          sudo mv ./kubectl /usr/local/bin/kubectl
      
      - name: Install kubelogin
        run: |
          curl -sSL -o kubelogin.zip https://github.com/Azure/kubelogin/releases/latest/download/kubelogin-linux-amd64.zip
          unzip kubelogin.zip
          install -m 555 bin/linux_amd64/kubelogin /usr/local/bin/kubelogin
          rm -rf bin kubelogin.zip

      - name: Set kubernetes context
        run: |
          az aks get-credentials -g mega-boston -n mega-boston-kubernetes
          kubelogin convert-kubeconfig -l azurecli
          kubectl config set-context --current --namespace=udestrus-pre

      - name: Get initial version
        id: get-version
        run: |
          VERSION=$(kubectl get application.argoproj.io deployment-service -n udestrus-pre -o jsonpath='{.status.sync.revision}')
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Initial version: $VERSION"

  deploy-pre-us:
    if: ${{ inputs.environment == 'pre-us' }}
    name: "pre-us: Deploy & Push Helm Chart"
    uses: cariad-ude/ude-str-github-actions/.github/workflows/dev-quarkus-build-deploy-rc.yaml@main
    secrets: inherit
    with:
      environment: pre-us
      product: udestrus-pre
      container-repo: deployment-service
      version: ${{ github.ref_name }}
      keyvault-name: boston-ca3f5937
      resource-group: udestrus-pre
      runner: '["self-hosted", "MegatronProducts", "eastus2"]'

  
  wait-for-argocd-sync-pre-us:
    name: Wait for ArgoCD Sync
    runs-on: [self-hosted, MegatronProducts, eastus2]
    needs: [get-initial-version-pre-us, deploy-pre-us]
    environment: pre-us
    steps:
      - name: Azure Login
        uses: azure/login@v1
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      
      - name: Install kubectl
        run: |
          curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
          chmod +x ./kubectl
          sudo mv ./kubectl /usr/local/bin/kubectl
      
      - name: Install kubelogin
        run: |
          curl -sSL -o kubelogin.zip https://github.com/Azure/kubelogin/releases/latest/download/kubelogin-linux-amd64.zip
          unzip kubelogin.zip
          install -m 555 bin/linux_amd64/kubelogin /usr/local/bin/kubelogin
          rm -rf bin kubelogin.zip

      - name: Set kubernetes context
        run: |
          az aks get-credentials -g mega-boston -n mega-boston-kubernetes
          kubelogin convert-kubeconfig -l azurecli
          kubectl config set-context --current --namespace=udestrus-pre

      - name: Wait for ArgoCD sync with new version
        run: |
          INITIAL_VERSION="${{ needs.get-initial-version.outputs.initial_version }}"
          echo "Initial version: $INITIAL_VERSION"
          
          echo "Waiting for ArgoCD sync to complete with new version..."
          for i in {1..90}; do
            # Check if the application is synced and healthy
            SYNC_STATUS=$(kubectl get application.argoproj.io deployment-service -n udestrus-pre -o jsonpath='{.status.sync.status}')
            HEALTH_STATUS=$(kubectl get application.argoproj.io deployment-service -n udestrus-pre -o jsonpath='{.status.health.status}')
            CURRENT_VERSION=$(kubectl get application.argoproj.io deployment-service -n udestrus-pre -o jsonpath='{.status.sync.revision}')
            OPERATION_PHASE=$(kubectl get application.argoproj.io deployment-service -n udestrus-pre -o jsonpath='{.status.operationState.phase}')
            
            echo "Current version: $CURRENT_VERSION"
            echo "Sync status: $SYNC_STATUS, Health status: $HEALTH_STATUS, Operation phase: $OPERATION_PHASE (attempt $i/90)"
            
            # If the version has changed and the application is synced and healthy, and operation is complete
            if [ "$SYNC_STATUS" = "Synced" ] && [ "$HEALTH_STATUS" = "Healthy" ] && [ "$CURRENT_VERSION" != "$INITIAL_VERSION" ] && [ "$OPERATION_PHASE" = "Succeeded" ]; then
              echo "Application is synced and healthy with the new version"
              echo "New version: $CURRENT_VERSION"
              exit 0
            fi
            
            # If we see a version change but operation is not complete, wait for it
            if [ "$CURRENT_VERSION" != "$INITIAL_VERSION" ] && [ -z "$OPERATION_PHASE" ]; then
              echo "Version changed but operation not started yet, waiting..."
            fi
            
            sleep 10
          done
          
          echo "Application did not sync with new version within the timeout period (15 minutes)"
          echo "Last seen version: $CURRENT_VERSION"
          echo "Last sync status: $SYNC_STATUS"
          echo "Last health status: $HEALTH_STATUS"
          echo "Last operation phase: $OPERATION_PHASE"
          exit 1

  e2e-tests-pre-us:
    name: Trigger E2E Tests
    runs-on: [self-hosted, MegatronProducts, eastus2]
    needs: wait-for-argocd-sync-pre-us
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}
          owner: ${{ github.repository_owner }}

      - name: Repository Dispatch
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ steps.app-token.outputs.token }}
          repository: cariad-ude/ude-str-tests
          event-type: run-e2e-tests
          client-payload: '{"environment": "us-preprod"}'

