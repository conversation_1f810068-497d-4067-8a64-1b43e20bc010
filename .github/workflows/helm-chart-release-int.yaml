name: int - Deploy Helm Chart
on:
  release:
    types: [published]

permissions:
  contents: write
  packages: read
  id-token: write
  actions: read
  pull-requests: write
  security-events: write

jobs:
  check-release:
    name: Check Release Type
    runs-on: ubuntu-latest
    outputs:
      is_rc_release: ${{ steps.check_release.outputs.is_rc_release }}
    steps:
      - name: Check if release is an RC release
        id: check_release
        run: |
          if [[ "${{ github.event.release.tag_name }}" =~ ^[0-9]+\.[0-9]+\.[0-9]+-RC[0-9]+$ ]]; then
            echo "is_rc_release=true" >> $GITHUB_OUTPUT
          else
            echo "is_rc_release=false" >> $GITHUB_OUTPUT
          fi
  get-initial-version:
    name: Get Initial Version
    runs-on: ubuntu-latest
    environment: int
    outputs:
      initial_version: ${{ steps.get-version.outputs.version }}
    steps:
      - name: Azure Login
        uses: azure/login@v1
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      
      - name: Install kubectl
        run: |
          curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
          chmod +x ./kubectl
          sudo mv ./kubectl /usr/local/bin/kubectl
      
      - name: Install kubelogin
        run: |
          curl -sSL -o kubelogin.zip https://github.com/Azure/kubelogin/releases/latest/download/kubelogin-linux-amd64.zip
          unzip kubelogin.zip
          install -m 555 bin/linux_amd64/kubelogin /usr/local/bin/kubelogin
          rm -rf bin kubelogin.zip

      - name: Set kubernetes context
        run: |
          az aks get-credentials -g mega-riga -n mega-riga-kubernetes
          kubelogin convert-kubeconfig -l azurecli
          kubectl config set-context --current --namespace=str-int

      - name: Get initial version
        id: get-version
        run: |
          VERSION=$(kubectl get application.argoproj.io deployment-service -n str-int -o jsonpath='{.status.sync.revision}')
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Initial version: $VERSION"

  deploy-helm-chart:
    name: Deploy & Push Helm Chart
    needs: check-release
    if: needs.check-release.outputs.is_rc_release == 'true'
    uses: cariad-ude/ude-str-github-actions/.github/workflows/dev-quarkus-build-deploy-rc.yaml@main
    secrets: inherit
    with:
      environment: int
      product: str-int
      container-repo: deployment-service
      version: ${{ github.ref_name }}
      keyvault-name: "riga-d7008c25"
      resource-group: "str-int"
 
  wait-for-argocd-sync:
    name: Wait for ArgoCD Sync
    runs-on: ubuntu-latest
    needs: [get-initial-version, deploy-helm-chart]
    environment: int
    steps:
      - name: Azure Login
        uses: azure/login@v1
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
      
      - name: Install kubectl
        run: |
          curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
          chmod +x ./kubectl
          sudo mv ./kubectl /usr/local/bin/kubectl
      
      - name: Install kubelogin
        run: |
          curl -sSL -o kubelogin.zip https://github.com/Azure/kubelogin/releases/latest/download/kubelogin-linux-amd64.zip
          unzip kubelogin.zip
          install -m 555 bin/linux_amd64/kubelogin /usr/local/bin/kubelogin
          rm -rf bin kubelogin.zip

      - name: Set kubernetes context
        run: |
          az aks get-credentials -g mega-riga -n mega-riga-kubernetes
          kubelogin convert-kubeconfig -l azurecli
          kubectl config set-context --current --namespace=str-int

      - name: Wait for ArgoCD sync with new version
        run: |
          INITIAL_VERSION="${{ needs.get-initial-version.outputs.initial_version }}"
          echo "Initial version: $INITIAL_VERSION"
          
          echo "Waiting for ArgoCD sync to complete with new version..."
          for i in {1..90}; do
            # Check if the application is synced and healthy
            SYNC_STATUS=$(kubectl get application.argoproj.io deployment-service -n str-int -o jsonpath='{.status.sync.status}')
            HEALTH_STATUS=$(kubectl get application.argoproj.io deployment-service -n str-int -o jsonpath='{.status.health.status}')
            CURRENT_VERSION=$(kubectl get application.argoproj.io deployment-service -n str-int -o jsonpath='{.status.sync.revision}')
            OPERATION_PHASE=$(kubectl get application.argoproj.io deployment-service -n str-int -o jsonpath='{.status.operationState.phase}')
            
            echo "Current version: $CURRENT_VERSION"
            echo "Sync status: $SYNC_STATUS, Health status: $HEALTH_STATUS, Operation phase: $OPERATION_PHASE (attempt $i/90)"
            
            # If the version has changed and the application is synced and healthy, and operation is complete
            if [ "$SYNC_STATUS" = "Synced" ] && [ "$HEALTH_STATUS" = "Healthy" ] && [ "$CURRENT_VERSION" != "$INITIAL_VERSION" ] && [ "$OPERATION_PHASE" = "Succeeded" ]; then
              echo "Application is synced and healthy with the new version"
              echo "New version: $CURRENT_VERSION"
              exit 0
            fi
            
            # If we see a version change but operation is not complete, wait for it
            if [ "$CURRENT_VERSION" != "$INITIAL_VERSION" ] && [ -z "$OPERATION_PHASE" ]; then
              echo "Version changed but operation not started yet, waiting..."
            fi
            
            sleep 10
          done
          
          echo "Application did not sync with new version within the timeout period (15 minutes)"
          echo "Last seen version: $CURRENT_VERSION"
          echo "Last sync status: $SYNC_STATUS"
          echo "Last health status: $HEALTH_STATUS"
          echo "Last operation phase: $OPERATION_PHASE"
          exit 1
  
  e2e-tests:
    name: Trigger E2E Tests
    runs-on: ubuntu-latest
    needs: wait-for-argocd-sync
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}
          owner: ${{ github.repository_owner }}

      - name: Repository Dispatch
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ steps.app-token.outputs.token }}
          repository: cariad-ude/ude-str-tests
          event-type: run-e2e-tests
          client-payload: '{"environment": "int"}'

  black_duck_scanning:
    name: BlackDuck Scanning Job
    needs: deploy-helm-chart
    if: needs.deploy-helm-chart.result == 'success'
    uses: cariad-ude/ude-str-github-actions/.github/workflows/backduck-scanning.yaml@main
    secrets: inherit
    with:
      blackduck-url: "https://blackduck.mega.cariad.cloud/"
      blackduck-project: "ude_str_deployment_service"
      blackduck-user-group: "data_streaming"
      blackduck-project-group: "data_streaming" 
      blackduck-project-name: "ude_str_deployment_service"
      blackduck-project-version: ${{ github.ref_name }}
      blackduck-code-location: "src"
      blackduck-project-tags: "vcb,mbr,raz"

      environment: int
      product: str-int
      container-repo: deployment-service
      keyvault-name: "riga-d7008c25"
