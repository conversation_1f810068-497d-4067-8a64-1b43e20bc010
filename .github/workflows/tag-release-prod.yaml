name: Tag release for production
on:
  workflow_dispatch:
    inputs:
      openapi_deployment:
        description: "Deploy OpenAPI"
        type: boolean
        default: true

jobs:
  build-and-release-eu:
    name: Build & Release EU
    uses: cariad-ude/ude-str-github-actions/.github/workflows/tag-release-prod.yaml@main
    secrets: inherit
    with:
      environment: prd-eu
      product: str-prd
      container-repo: deployment-service
      keyvault-name: "roja-bb1c1eee"
      resource-group: "str-prd"
      openapi_deployment: ${{ inputs.openapi_deployment }} 
      runner: '["self-hosted", "MegatronProducts", "westeurope"]'
      create_tag: true

  build-and-release-us:
    name: Build & Release US
    uses: cariad-ude/ude-str-github-actions/.github/workflows/tag-release-prod.yaml@main
    secrets: inherit
    with:
      environment: prd-us
      product: udestrus-prd
      container-repo: deployment-service
      keyvault-name: "boston-d5470e24"
      resource-group: "udestrus-prd"
      openapi_deployment: ${{ inputs.openapi_deployment }}
      runner: '["self-hosted", "MegatronProducts", "eastus2"]'
      create_tag: false 