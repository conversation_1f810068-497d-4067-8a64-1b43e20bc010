name: dev - Deploy Helm Chart
on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - '**'
      - '!helm/**'
      - '!**.md'
      - '!.github/**'

jobs:
  deploy-helm-chart:
    name: Deploy & Push Helm Chart
    uses: cariad-ude/ude-str-github-actions/.github/workflows/dev-quarkus-build-deploy-dev.yaml@main
    secrets: inherit
    with:
      environment: dev
      product: str-dev
      container-repo: deployment-service
      keyvault-name: riga-9ab4ff3b
      resource-group: str-dev
