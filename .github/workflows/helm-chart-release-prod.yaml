name: Production - Deploy Helm Chart
on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Select the production environment to deploy to"
        type: choice
        required: true
        options:
          - prd-eu
          - prd-us
      openapi_deployment:
        description: "Deploy OpenAPI specification to API Management"
        required: true
        type: boolean
        default: true

jobs:
  validate-input:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Validate version format
        run: |
          if ! [[ "${{ github.ref_name }}" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            echo "❌ Error: Invalid version format. Expected format: 1.0.0"
            echo "Please select a valid version tag when running the workflow"
            exit 1
          fi
          echo "✅ Version format is valid"

  pre-deployment-checks:
    needs: validate-input
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Log deployment details
        run: |
          echo "📝 Deployment Details:"
          echo "------------------------"
          echo "Version: ${{ github.ref_name }}"
          echo "Environment: ${{ github.event.inputs.environment }}"
          echo "OpenAPI Deployment: ${{ github.event.inputs.openapi_deployment }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "------------------------"

      - name: Pre-deployment health check
        run: |
          # Add your pre-deployment health checks here
          echo "✅ Pre-deployment checks passed"

  deploy-prod-eu:
    needs: pre-deployment-checks
    if: ${{ inputs.environment == 'prd-eu' }}
    name: "prod-eu: Deploy & Push Helm Chart"
    uses: cariad-ude/ude-str-github-actions/.github/workflows/dev-quarkus-build-deploy-rc.yaml@main
    with:
      environment: prd-eu
      product: str-prd
      container-repo: deployment-service
      version: ${{ github.ref_name }}
      keyvault-name: roja-bb1c1eee
      resource-group: str-prd
      runner: '["self-hosted", "MegatronProducts", "westeurope"]'
      apim-name: ${{ github.event.inputs.openapi_deployment == 'true' && 'ude-str-prod-eu-apimanagement' || '' }}
    secrets: inherit

  deploy-prod-us:
    needs: pre-deployment-checks
    if: ${{ inputs.environment == 'prd-us' }}
    name: "prod-us: Deploy & Push Helm Chart"
    uses: cariad-ude/ude-str-github-actions/.github/workflows/dev-quarkus-build-deploy-rc.yaml@main
    with:
      environment: prd-us
      product: udestrus-prd
      container-repo: deployment-service
      version: ${{ github.ref_name }}
      keyvault-name: boston-d5470e24
      resource-group: udestrus-prd
      runner: '["self-hosted", "MegatronProducts", "eastus2"]'
      apim-name: ${{ github.event.inputs.openapi_deployment == 'true' && 'ude-str-produs-apimanagement' || '' }}
    secrets: inherit

  post-deployment-verification:
    needs: [deploy-prod-eu, deploy-prod-us]
    if: always()
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Verify deployment
        run: |
          echo "🔍 Post-deployment verification"
          echo "------------------------"
          echo "Environment: ${{ github.event.inputs.environment }}"
          echo "Version: ${{ github.ref_name }}"
          echo "Status: ${{ needs.deploy-prod-eu.result || needs.deploy-prod-us.result }}"
          echo "------------------------"
          
          # Add your post-deployment verification steps here
          # For example: health checks, smoke tests, etc.
          
          if [ "${{ needs.deploy-prod-eu.result || needs.deploy-prod-us.result }}" != "success" ]; then
            echo "❌ Deployment verification failed"
            exit 1
          fi
          echo "✅ Deployment verification passed" 