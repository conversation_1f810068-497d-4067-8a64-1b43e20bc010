plugins {
    java
    jacoco
    id("com.diffplug.spotless")
    id("com.github.ben-manes.versions")
    id("io.freefair.lombok")
    id("io.quarkus")
    id("org.sonarqube")
}

jacoco {
    toolVersion = "0.8.13"
}

tasks.jacocoTestReport {
    reports {
        xml.required.set(true)
        html.required.set(true)
    }
    finalizedBy(tasks.jacocoTestCoverageVerification)
}

tasks.jacocoTestCoverageVerification {
    dependsOn(tasks.jacocoTestReport)
    violationRules {
        rule {
            limit {
                minimum = "0.3".toBigDecimal()
            }
        }
    }
}

tasks.test {
    finalizedBy(tasks.jacocoTestReport)
    jvmArgs("-Xms1024m", "-Xmx10240m")
}

repositories {
    mavenCentral()
    mavenLocal()
    maven {
        url = uri("https://packages.confluent.io/maven/")
    }
}

dependencies {
    implementation(enforcedPlatform("io.quarkus:quarkus-bom:${property("quarkusPlatformVersion")}"))
    implementation("com.azure:azure-identity:1.15.4")
    implementation("com.azure:azure-storage-blob:12.30.0")
    implementation("com.azure:azure-storage-file-datalake:12.23.0")
    implementation("com.github.jknack:handlebars:4.4.0")
    implementation("io.confluent:kafka-json-schema-serializer:7.9.0")
    implementation("io.confluent:kafka-schema-registry-client:7.9.0")
    implementation("io.quarkus:quarkus-arc")
    implementation("io.quarkus:quarkus-config-yaml")
    implementation("io.quarkus:quarkus-container-image-jib")
    implementation("io.quarkus:quarkus-hibernate-validator")
    implementation("io.quarkus:quarkus-jackson")
    implementation("io.quarkus:quarkus-kubernetes-client")
    implementation("io.quarkus:quarkus-messaging-kafka")
    implementation("io.quarkus:quarkus-rest")
    implementation("io.quarkus:quarkus-rest-client-jackson")
    implementation("io.quarkus:quarkus-rest-jackson")
    implementation("io.quarkus:quarkus-smallrye-health")
    implementation("org.apache.flink:flink-kubernetes-operator-api:1.11.0")

    testImplementation("io.quarkiverse.wiremock:quarkus-wiremock-test:1.4.1")
    testImplementation("io.quarkus:quarkus-junit5")
    testImplementation("io.quarkus:quarkus-junit5-mockito")
    testImplementation("io.quarkus:quarkus-test-kafka-companion")
    testImplementation("io.quarkus:quarkus-test-kubernetes-client")
    testImplementation("io.rest-assured:rest-assured")
    testImplementation("org.assertj:assertj-core:3.27.3")
    testImplementation("org.awaitility:awaitility:4.3.0")
    testImplementation("org.testcontainers:azure")
    testImplementation("io.quarkus:quarkus-jacoco")
}

group = "org.ude"
version = "0.2.0-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21

    toolchain {
        languageVersion.set(JavaLanguageVersion.of(21))
    }
}

tasks.withType<Test> {
    systemProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager")
}

tasks.withType<JavaCompile> {
    options.encoding = "UTF-8"
    options.compilerArgs.add("-parameters")
}

sonar {
    properties {
        property("sonar.projectKey", "udestr:ude-str-deployment-service")
        property("sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/test/jacocoTestReport.xml")
        property("sonar.qualitygate", "true")
        property("sonar.jacoco.reportPath", "build/jacoco/test.exec")
        property("sonar.junit.reportPaths", "build/test-results/test/")
    }
}

spotless {
    java {
        importOrder()
        removeUnusedImports()
        googleJavaFormat()
        formatAnnotations()
        custom(
            "NoWildcardImports",
            object : java.io.Serializable, com.diffplug.spotless.FormatterFunc {
                override fun apply(input: String): String {
                    if (input.contains("*;\n")) {
                        throw GradleException("Wildcard imports are not allowed")
                    }
                    return input
                }
            }
        )
    }
}
